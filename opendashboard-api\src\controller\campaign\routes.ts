import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {CampaignController} from "./controller";

export const campaignRoutes: Routes = {
    basePath: '/workspaces/:id/campaigns',
    middleware: [verifyJWT],
    routes: {
        "/": {
            post: {controller: CampaignController, action: "create"},
            get: {controller: CampaignController, action: "getAllCampaigns"},
        },
        '/:campaignId': {
            get: {controller: CampaignController, action: "get"},
            patch: {controller: CampaignController, action: "update"},
            delete: {controller: CampaignController, action: "doDelete"},
        },
        '/:campaignId/review': {
            post: {controller: CampaignController, action: "review"},
            delete: {controller: CampaignController, action: "removeReview"},
        },
        '/:campaignId/publish': {
            post: {controller: CampaignController, action: "publish"},
        },
        '/:campaignId/sequences': {
            post: {controller: CampaignController, action: "createSeq"},
        },
        '/:campaignId/emails/:emailId': {
            patch: {controller: CampaignController, action: "updateEmail"},
        },
    }
}
