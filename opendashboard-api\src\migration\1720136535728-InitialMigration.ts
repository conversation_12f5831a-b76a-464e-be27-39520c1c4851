import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1720136535728 implements MigrationInterface {
    name = 'InitialMigration1720136535728'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`activity\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`databaseId\` varchar(255) NULL, \`pageId\` varchar(255) NULL, \`recordId\` varchar(255) NULL, \`objectType\` varchar(255) NOT NULL, \`objectId\` varchar(255) NULL, \`createdById\` varchar(255) NOT NULL, \`parentId\` varchar(255) NULL, \`activityType\` varchar(255) NOT NULL, \`changeData\` json NULL, \`isResolved\` tinyint NOT NULL DEFAULT 0, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_26dd80869eb20b6e4733e513d4\` (\`workspaceId\`), INDEX \`IDX_2e69eec78cb1822dc859d3cab1\` (\`databaseId\`), INDEX \`IDX_056432793744bd25a4c572193e\` (\`pageId\`), INDEX \`IDX_2e3a9f9108eb8ea9dad7112699\` (\`recordId\`), INDEX \`IDX_2b8bd3f00c1450118c2d417c51\` (\`objectType\`), INDEX \`IDX_2dd2ad6a696f31dfad4f20af7b\` (\`objectId\`), INDEX \`IDX_72d2e57f824d488c48281819cd\` (\`createdById\`), INDEX \`IDX_f1253e909ae2f1ea48c44d2d5a\` (\`parentId\`), INDEX \`IDX_e635d1956359354c63a36205db\` (\`activityType\`), INDEX \`IDX_478b04bbf56fe88743227d3f58\` (\`isResolved\`), INDEX \`IDX_caa645b86d8db66739106100a2\` (\`createdAt\`), INDEX \`IDX_96f5b8f674eb85c6f34e0ed558\` (\`updatedAt\`), INDEX \`IDX_271eba6040bf7f3f322492932c\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`billing_cycle\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`stripeSubscriptionId\` varchar(255) NULL, \`futureStripeSubscriptionId\` varchar(255) NULL, \`planId\` varchar(255) NULL, \`priceId\` varchar(255) NULL, \`futurePriceId\` varchar(255) NULL, \`anchorDay\` int NOT NULL DEFAULT '0', \`startsAt\` timestamp NULL, \`endsAt\` timestamp NULL, \`endedAt\` timestamp NULL, \`isActive\` tinyint NOT NULL DEFAULT 0, \`isPaid\` tinyint NOT NULL DEFAULT 0, \`isRenewing\` tinyint NOT NULL DEFAULT 0, \`costInCents\` decimal NOT NULL DEFAULT '0', \`amountPaidInCents\` decimal NOT NULL DEFAULT '0', \`paidAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`cyclePlanQuota\` json NOT NULL, \`cycleUsage\` json NOT NULL, \`addOnsQuota\` json NOT NULL, \`usersQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))) STORED NOT NULL, \`collaboratorsQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))) STORED NOT NULL, \`recordsQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))) STORED NOT NULL, \`auditLog\` json NULL, \`meta\` json NULL, INDEX \`IDX_bc7bfa85ebc427572ff76a7d14\` (\`workspaceId\`), INDEX \`IDX_975316095831d0e4d889125218\` (\`stripeSubscriptionId\`), INDEX \`IDX_bd3bc6a1a895f9603b53b3484b\` (\`futureStripeSubscriptionId\`), INDEX \`IDX_c0a438744b0122d92753f8a638\` (\`futurePriceId\`), INDEX \`IDX_a295f71822454de2b2ad452118\` (\`isActive\`), INDEX \`IDX_f502aa5f2f6c4e37c090b10846\` (\`isPaid\`), INDEX \`IDX_2ba1eeb5cfe0f1c00597672d45\` (\`isRenewing\`), INDEX \`IDX_f6a47f9552eefe05e23413904e\` (\`costInCents\`), INDEX \`IDX_1841248cc721d7e3ea082e7a34\` (\`amountPaidInCents\`), INDEX \`IDX_8229390c53eed700a0c2321161\` (\`paidAt\`), INDEX \`IDX_87dd5727462d5834893cd7a1d5\` (\`usersQuota\`), INDEX \`IDX_ca9c93b791243d3095bc5f8b54\` (\`collaboratorsQuota\`), INDEX \`IDX_d791afc8fa9fd702974916459a\` (\`recordsQuota\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`INSERT INTO \`typeorm_metadata\`(\`database\`, \`schema\`, \`table\`, \`type\`, \`name\`, \`value\`) VALUES (DEFAULT, ?, ?, ?, ?, ?)`, ["opendashboardv2","billing_cycle","GENERATED_COLUMN","usersQuota","(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))"]);
        await queryRunner.query(`INSERT INTO \`typeorm_metadata\`(\`database\`, \`schema\`, \`table\`, \`type\`, \`name\`, \`value\`) VALUES (DEFAULT, ?, ?, ?, ?, ?)`, ["opendashboardv2","billing_cycle","GENERATED_COLUMN","collaboratorsQuota","(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))"]);
        await queryRunner.query(`INSERT INTO \`typeorm_metadata\`(\`database\`, \`schema\`, \`table\`, \`type\`, \`name\`, \`value\`) VALUES (DEFAULT, ?, ?, ?, ?, ?)`, ["opendashboardv2","billing_cycle","GENERATED_COLUMN","recordsQuota","(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))"]);
        await queryRunner.query(`CREATE TABLE \`ai_content_generation_log\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`provider\` varchar(255) NOT NULL, \`providerReference\` varchar(255) NULL, \`modelName\` varchar(255) NULL, \`systemPrompt\` text NULL, \`userPrompt\` longtext NULL, \`contentGenerated\` text NULL, \`providerResponse\` text NULL, \`inputTokens\` int NOT NULL DEFAULT '0', \`outputTokens\` int NOT NULL DEFAULT '0', \`estimatedCostInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`rating\` tinyint UNSIGNED NOT NULL DEFAULT '0', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_18f2192e7648bac46ffdcf14f9\` (\`workspaceId\`), INDEX \`IDX_408735afcb72409ddb10f8dada\` (\`provider\`), INDEX \`IDX_ca2c786768495c7d396021efbe\` (\`providerReference\`), INDEX \`IDX_f0697f19d8ba48457a891fa70c\` (\`modelName\`), INDEX \`IDX_2623411e4c826319fd18482122\` (\`inputTokens\`), INDEX \`IDX_802af9bcce445b1b56d1704b24\` (\`outputTokens\`), INDEX \`IDX_7eac1d4fda433baf8f1528ec55\` (\`estimatedCostInCents\`), INDEX \`IDX_abb73edd3a08d07129552a2e6b\` (\`rating\`), INDEX \`IDX_226bdd92556029a470699b7d70\` (\`createdAt\`), INDEX \`IDX_bdf6072736e3d4caa3330cfeac\` (\`updatedAt\`), INDEX \`IDX_ebef6baa610503f8ac59d9e378\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`campaign_analytic\` (\`id\` int NOT NULL AUTO_INCREMENT, \`campaignId\` varchar(255) NOT NULL, \`emailId\` int NOT NULL, \`linkId\` int NULL, \`openCount\` int NOT NULL DEFAULT '0', \`clickCount\` int NOT NULL DEFAULT '0', \`eventAt\` datetime NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_288373b3a3bff5c6795b9205fb\` (\`campaignId\`), INDEX \`IDX_67572f7fe58e557913a1786368\` (\`emailId\`), INDEX \`IDX_7ee967f1d1afed90c4014437a6\` (\`linkId\`), INDEX \`IDX_cb7610ce639e0ab20557c5d7c6\` (\`eventAt\`), INDEX \`IDX_5dbbb0d27f9380b22c57d37512\` (\`createdAt\`), INDEX \`IDX_55dd05a4bbdd2296fd507b510a\` (\`updatedAt\`), INDEX \`IDX_784a7562ae3e367ab19d65122b\` (\`deletedAt\`), UNIQUE INDEX \`IDX_3aceab20f0ba5dd17fa3224c78\` (\`campaignId\`, \`emailId\`, \`linkId\`, \`eventAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`campaign\` (\`id\` varchar(36) NOT NULL, \`parentId\` varchar(255) NULL, \`subject\` varchar(255) NULL, \`contentText\` text NULL, \`cc\` json NULL, \`bcc\` json NULL, \`attachments\` json NULL, \`type\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`createdById\` varchar(255) NOT NULL, \`updatedById\` varchar(255) NOT NULL, \`databaseId\` varchar(255) NOT NULL, \`targetDatabaseId\` varchar(255) NULL, \`targetColId\` varchar(255) NULL, \`recordIds\` json NULL, \`targetRecords\` json NULL, \`targetRecordIdsToSkip\` json NULL, \`sequenceRecordFilter\` json NULL, \`sequenceTarget\` varchar(255) NULL DEFAULT 'all', \`status\` varchar(255) NOT NULL DEFAULT 'draft', \`failedReason\` varchar(255) NULL, \`senderId\` varchar(255) NULL, \`deliverAt\` timestamp NULL, \`deliveredAt\` timestamp NULL, \`deliveryHeartbeatAt\` timestamp NULL, \`sendAtLocalTime\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`auditLog\` json NULL, \`meta\` json NULL, INDEX \`IDX_cfe2d8c57b4fabb084971a0563\` (\`parentId\`), INDEX \`IDX_4f0678ebd019c0ffcc5f60c78c\` (\`type\`), INDEX \`IDX_5a0877c4a9da13621d124867a5\` (\`workspaceId\`), INDEX \`IDX_3b241eb12b1b0324034cf8168f\` (\`createdById\`), INDEX \`IDX_0e03af5b087896e61a0b788dcf\` (\`updatedById\`), INDEX \`IDX_16d47ffb02d94252452a052155\` (\`databaseId\`), INDEX \`IDX_217a2a9ddd813c0a5333d7e690\` (\`targetDatabaseId\`), INDEX \`IDX_f5b0bf5ae20fe76bb278770bf5\` (\`targetColId\`), INDEX \`IDX_aa53dd2f032888a188b9380765\` (\`sequenceTarget\`), INDEX \`IDX_08135474cffdb8282c4ff37271\` (\`status\`), INDEX \`IDX_73ab13babe6ce113714ec8ba59\` (\`senderId\`), INDEX \`IDX_d2625f9f444f128462f3980d0f\` (\`deliveryHeartbeatAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`campaign_email\` (\`id\` int NOT NULL AUTO_INCREMENT, \`campaignId\` varchar(255) NOT NULL, \`targetRecordId\` varchar(255) NULL, \`recordId\` varchar(255) NOT NULL, \`status\` varchar(255) NOT NULL, \`skippedReason\` varchar(255) NULL, \`emailTo\` json NULL, \`emailFrom\` json NULL, \`cc\` json NULL, \`bcc\` json NULL, \`attachments\` json NULL, \`subject\` text NULL, \`contentText\` text NULL, \`messageId\` varchar(255) NULL, \`sentAt\` timestamp NULL, \`isSent\` tinyint NOT NULL DEFAULT 0, \`bouncedAt\` timestamp NULL, \`deliveredAt\` timestamp NULL, \`spamReportedAt\` timestamp NULL, \`isSkipped\` tinyint NOT NULL DEFAULT 0, \`isBounced\` tinyint NOT NULL DEFAULT 0, \`isUnsubscribed\` tinyint NOT NULL DEFAULT 0, \`isDelivered\` tinyint NOT NULL DEFAULT 0, \`isSpamReported\` tinyint NOT NULL DEFAULT 0, \`unsubscribedAt\` timestamp NULL, \`unsubscribeReason\` text NULL, \`bounceReason\` text NULL, \`deliveryException\` text NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`auditLog\` json NULL, \`meta\` json NULL, INDEX \`IDX_e39b0d7bda783ebb9a266928f6\` (\`campaignId\`), INDEX \`IDX_16e46b929237871426202dd33b\` (\`targetRecordId\`), INDEX \`IDX_891ab18aa964ac2f87f4592d02\` (\`recordId\`), INDEX \`IDX_e0a8032b0417c10d343efd953e\` (\`status\`), INDEX \`IDX_a49e22533ba889df4b27b179dd\` (\`messageId\`), INDEX \`IDX_1204d68e31d47d86d45e5092f7\` (\`sentAt\`), INDEX \`IDX_d71cbc6cc70729ae38517f62b4\` (\`isSent\`), INDEX \`IDX_a4a07fb14ee6db2b9bc00417de\` (\`bouncedAt\`), INDEX \`IDX_0df3c9efe3c9524412ae716599\` (\`deliveredAt\`), INDEX \`IDX_a0a57537f86ca150b773715b71\` (\`spamReportedAt\`), INDEX \`IDX_8b22d6ae028fa8c507ac835b41\` (\`isSkipped\`), INDEX \`IDX_a3f88f32b13a9ccfa44ddda49f\` (\`isBounced\`), INDEX \`IDX_e8c1df6f092195a80b90879485\` (\`isUnsubscribed\`), INDEX \`IDX_074abcb00396b3a57791d6804e\` (\`isDelivered\`), INDEX \`IDX_9dc28be4b9396cf111906e8514\` (\`isSpamReported\`), INDEX \`IDX_274e364d985587da0120b7ed3e\` (\`unsubscribedAt\`), INDEX \`IDX_b0858936373e415473f5afbab0\` (\`createdAt\`), INDEX \`IDX_d4cf868ed2c2f3e88e359c7072\` (\`updatedAt\`), INDEX \`IDX_9e70801ef519e9085641dbf66a\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`database\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NULL, \`definition\` json NULL, \`workspaceId\` varchar(255) NOT NULL, \`srcPackageName\` varchar(255) NULL, \`srcVersionNumber\` int NULL, \`srcVersionName\` varchar(255) NULL, \`packageName\` varchar(255) NULL, \`versionNumber\` int NULL, \`versionName\` varchar(255) NULL, \`createdById\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_3a876646ed2427b5fd626ce1fc\` (\`name\`), INDEX \`IDX_593e4680cd57e73e6c27bc309f\` (\`workspaceId\`), INDEX \`IDX_f3ba71ac6f385135e39c8bd3cf\` (\`srcPackageName\`), INDEX \`IDX_6f3b16bc022dfb2d7fdb504889\` (\`packageName\`), INDEX \`IDX_a560c05515299e79d6f02f189a\` (\`createdById\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`document\` (\`id\` varchar(36) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`name\` varchar(255) NULL, \`contentJSON\` json NULL, \`contentText\` text NULL, \`viewId\` varchar(255) NOT NULL, \`createdById\` varchar(255) NULL, \`updatedById\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_41070461ad429c324c36752537\` (\`workspaceId\`), INDEX \`IDX_6be4f75c40f5f3878d18d9806c\` (\`name\`), FULLTEXT INDEX \`IDX_d8f3916286cf18be51c8a0f6c4\` (\`contentText\`), INDEX \`IDX_7a269d560b28960db295d33d49\` (\`viewId\`), INDEX \`IDX_9eac3612452020c976207f37b0\` (\`createdById\`), INDEX \`IDX_3d0038df8ca900f5fda2ff896a\` (\`updatedById\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`page\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NULL, \`icon\` json NULL, \`workspaceId\` varchar(255) NOT NULL, \`databaseId\` varchar(255) NULL, \`slug\` varchar(255) NULL, \`visibility\` varchar(255) NOT NULL DEFAULT 'open', \`accessLevel\` varchar(255) NULL DEFAULT 'full', \`viewsOrder\` json NULL, \`ownerId\` varchar(36) NULL, \`createdById\` varchar(36) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_b82c19c08afb292de4600d99e4\` (\`name\`), INDEX \`IDX_3e419598ba888095f081633c51\` (\`workspaceId\`), INDEX \`IDX_527514df011f586dd730125d0b\` (\`databaseId\`), INDEX \`IDX_58f346ec69270db5796f3cd6e9\` (\`visibility\`), INDEX \`IDX_181d08bb1a292753ad89ec74ea\` (\`ownerId\`), INDEX \`IDX_5021d47dea711cebdc2b8b3232\` (\`createdById\`), UNIQUE INDEX \`IDX_298e8d85c446da9877db925123\` (\`workspaceId\`, \`slug\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`domain\` (\`id\` int NOT NULL AUTO_INCREMENT, \`addedByUserId\` varchar(255) NOT NULL, \`subdomain\` varchar(255) NOT NULL, \`domain\` varchar(255) NOT NULL, \`fullAuthorizedDomain\` varchar(255) NOT NULL, \`sendgridDomainId\` varchar(255) NULL, \`configs\` json NULL, \`isVerified\` tinyint NOT NULL DEFAULT 0, \`verifiedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, UNIQUE INDEX \`IDX_0f1578799ebbe7b91b5b40fe4d\` (\`fullAuthorizedDomain\`), UNIQUE INDEX \`IDX_401d46173265d542bd749a49cb\` (\`sendgridDomainId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`page_permission\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(100) NOT NULL, \`pageId\` varchar(100) NOT NULL, \`userId\` varchar(100) NOT NULL, \`accessLevel\` varchar(20) NULL DEFAULT 'edit', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_9ede8636cf4158970bc0a4aaa7\` (\`workspaceId\`), INDEX \`IDX_4f07900aafdb506ffb30f30271\` (\`pageId\`), INDEX \`IDX_1c92bb3609947b3368d48f958f\` (\`userId\`), UNIQUE INDEX \`IDX_2ca2fff04ac03b8bf738f10ac2\` (\`workspaceId\`, \`pageId\`, \`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`link\` (\`id\` int NOT NULL AUTO_INCREMENT, \`linkUrl\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_5f66e43b97ceeb7d40ee3116ba\` (\`createdAt\`), INDEX \`IDX_8d4f2def879981a56403d75c89\` (\`updatedAt\`), INDEX \`IDX_0457fcd59f565bf429ea43baf4\` (\`deletedAt\`), UNIQUE INDEX \`IDX_49ff6e4d30aff91dbfdaa7b505\` (\`linkUrl\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`settings\` (\`id\` int NOT NULL AUTO_INCREMENT, \`userId\` varchar(255) NOT NULL, \`notification\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_9175e059b0a720536f7726a88c\` (\`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`record\` (\`id\` varchar(36) NOT NULL, \`databaseId\` varchar(255) NOT NULL, \`createdById\` varchar(255) NULL, \`updatedById\` varchar(255) NULL, \`recordValues\` json NULL, \`uniqueValue\` varchar(255) NULL, \`summaryJSON\` json NULL, \`summaryText\` text NULL, \`title\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_b644594912e8109679b9eeec40\` (\`databaseId\`), INDEX \`IDX_278bee369877fd3fe2d913a7b9\` (\`createdById\`), INDEX \`IDX_478244b5ec579a9ccf7e94f8a5\` (\`updatedById\`), FULLTEXT INDEX \`IDX_f8350b6a10f5ec5dbf0efb0d5a\` (\`summaryText\`), UNIQUE INDEX \`IDX_960b5f19a132cb624277cafe30\` (\`databaseId\`, \`uniqueValue\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`subscription\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`stripeSubscriptionId\` varchar(255) NOT NULL, \`status\` varchar(255) NOT NULL, \`planId\` varchar(255) NULL, \`priceId\` varchar(255) NULL, \`anchorDay\` int NOT NULL DEFAULT '0', \`startsAt\` timestamp NULL, \`endsAt\` timestamp NULL, \`endedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`auditLog\` json NULL, \`meta\` json NULL, INDEX \`IDX_a644824d4e72109d2ed988895e\` (\`workspaceId\`), INDEX \`IDX_944f71468e89554380d6c0d197\` (\`status\`), UNIQUE INDEX \`IDX_77dbeeb547dcfa35907431005b\` (\`stripeSubscriptionId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`token\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NULL, \`purpose\` int NOT NULL, \`status\` tinyint NOT NULL DEFAULT '1', \`userId\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NULL, \`token\` varchar(255) NOT NULL, \`clientName\` varchar(255) NULL, \`lastActiveAt\` timestamp NULL, \`expiresAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_7e943f35ecba345eb3c898d700\` (\`purpose\`), INDEX \`IDX_9e062dd9e27767637c7c48a98d\` (\`status\`), INDEX \`IDX_94f168faad896c0786646fa3d4\` (\`userId\`), INDEX \`IDX_5ce95d9321f7221f198907ae07\` (\`workspaceId\`), INDEX \`IDX_d9959ee7e17e2293893444ea37\` (\`token\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_billable_transaction\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`debitAmountInCents\` int NOT NULL, \`summary\` text NULL, INDEX \`IDX_602c8df8cf202aea4889fea862\` (\`workspaceId\`), INDEX \`IDX_db4f85b3001cc59b991ad14e28\` (\`createdAt\`), INDEX \`IDX_ad81085a845846e4f0dc775bd5\` (\`updatedAt\`), INDEX \`IDX_b751e46e842cfadee2d68cc785\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`view\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NULL, \`description\` varchar(255) NULL, \`pageId\` varchar(255) NOT NULL, \`slug\` varchar(255) NULL, \`type\` varchar(255) NOT NULL, \`definition\` json NULL, \`isPublished\` tinyint NOT NULL DEFAULT 0, \`allowSearchEngineIndex\` tinyint NOT NULL DEFAULT 0, \`publishedAt\` timestamp NULL, \`createdById\` varchar(36) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_11b0d38c2a07f642397643870c\` (\`name\`), INDEX \`IDX_211aeda56439ea21150200ab6d\` (\`pageId\`), INDEX \`IDX_e3e5907f868f10b24627e4982e\` (\`createdById\`), UNIQUE INDEX \`IDX_5d08d8121bcb93359fd5c8c00f\` (\`pageId\`, \`slug\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_credit\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`purchasedAt\` timestamp NULL, \`costInCents\` decimal NOT NULL DEFAULT '0', \`creditAmountInCents\` decimal NOT NULL DEFAULT '0', \`creditRemainingInCents\` decimal NOT NULL DEFAULT '0', \`validFrom\` timestamp NULL, \`expiresAt\` timestamp NULL, \`stripeInvoiceId\` varchar(255) NULL, \`auditLog\` json NULL, INDEX \`IDX_c65ce89b56ac570ca8dda6908d\` (\`workspaceId\`), INDEX \`IDX_74cc15b3ff0f5dfb8193ff8f9a\` (\`createdAt\`), INDEX \`IDX_29b95b2aafa2795d6a0430e457\` (\`updatedAt\`), INDEX \`IDX_0709945c8b818ccb95b9e10f14\` (\`deletedAt\`), INDEX \`IDX_33bcd5be6c640fc6000abd9a3b\` (\`costInCents\`), INDEX \`IDX_6f968d636b35d65f86310fa8a1\` (\`creditRemainingInCents\`), INDEX \`IDX_62045c733a0f3800e7cb8a5562\` (\`validFrom\`), INDEX \`IDX_f68aa670f843bcd43f34f9cdaa\` (\`expiresAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`domain\` varchar(255) NOT NULL, \`isSetupCompleted\` tinyint NOT NULL DEFAULT 0, \`logo\` varchar(255) NULL, \`createdById\` varchar(255) NOT NULL, \`ownerId\` varchar(255) NOT NULL, \`stripeCustomerId\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`orgUseCase\` varchar(255) NULL, \`orgType\` varchar(255) NULL, \`orgSize\` varchar(255) NULL, \`orgPhoneNumber\` varchar(255) NULL, \`deletionReason\` varchar(255) NULL, \`timezone\` varchar(255) NOT NULL DEFAULT 'UTC', \`isSupportAccessEnabled\` tinyint NOT NULL DEFAULT 0, \`isFunctionalityLimited\` tinyint NOT NULL DEFAULT 0, \`meta\` json NULL, INDEX \`IDX_3a3bf40f63cb3d745a8c58b0af\` (\`isSetupCompleted\`), INDEX \`IDX_51f2194e4a415202512807d2f6\` (\`ownerId\`), INDEX \`IDX_e0dadb504d672c0fa061a3a6a0\` (\`stripeCustomerId\`), INDEX \`IDX_875861360ccf551467b277dd76\` (\`timezone\`), INDEX \`IDX_69671cfda612c374ec5c852592\` (\`isSupportAccessEnabled\`), UNIQUE INDEX \`IDX_98f3bcf982a3db7219b2b3047b\` (\`domain\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`user\` (\`id\` varchar(36) NOT NULL, \`firstName\` varchar(255) NULL, \`lastName\` varchar(255) NULL, \`email\` varchar(255) NOT NULL, \`profilePhoto\` varchar(255) NULL, \`isEmailVerified\` tinyint NOT NULL DEFAULT 0, \`isSetupCompleted\` tinyint NOT NULL DEFAULT 0, \`isSupportAccount\` tinyint NOT NULL DEFAULT 0, \`activeWorkspaceId\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`auditLog\` json NULL, UNIQUE INDEX \`IDX_e12875dfb3b1d92d7d7c5377e2\` (\`email\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_domain\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`addedByUserId\` varchar(255) NOT NULL, \`domainId\` int NOT NULL, \`isVerified\` tinyint NOT NULL DEFAULT 0, \`verifiedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_b140c6bbd50969886dd94488a3\` (\`workspaceId\`), UNIQUE INDEX \`IDX_81a1d6b559e2b8dde569697477\` (\`workspaceId\`, \`domainId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_member\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`userId\` varchar(255) NOT NULL, \`role\` varchar(255) NOT NULL, \`invitedById\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_15b622cbfffabc30d7dbc52fed\` (\`workspaceId\`), INDEX \`IDX_03ce416ae83c188274dec61205\` (\`userId\`), UNIQUE INDEX \`IDX_b4104c0c92e2afdca1127be445\` (\`workspaceId\`, \`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_invitation\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`email\` varchar(255) NULL, \`userId\` varchar(255) NULL, \`role\` varchar(255) NULL, \`token\` varchar(255) NOT NULL, \`expiresAt\` timestamp NULL, \`invitedById\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_c060076f1277c3c957151ec132\` (\`workspaceId\`), INDEX \`IDX_353a007588fb0ce02e603f1a67\` (\`email\`), INDEX \`IDX_00a522900f16bb56f1148cc258\` (\`userId\`), INDEX \`IDX_49e1b235ed0b102d72b8f299d6\` (\`token\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_member_settings\` (\`id\` int NOT NULL AUTO_INCREMENT, \`userId\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`settings\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_7ac4881d8984a6b289967543b4\` (\`workspaceId\`, \`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_sender_email\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`addedByUserId\` varchar(255) NOT NULL, \`email\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`workspaceDomainId\` int NOT NULL, \`isVerified\` tinyint NOT NULL DEFAULT 0, \`verifiedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_8ace4eb912896b1f79478c0905\` (\`workspaceId\`), INDEX \`IDX_d6a3a16d333244a340fe724186\` (\`workspaceDomainId\`), UNIQUE INDEX \`IDX_dd5f61a9df2ebfbf8b90ba866c\` (\`workspaceId\`, \`email\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_risk_log\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`riskType\` varchar(255) NOT NULL, \`isResolved\` tinyint NOT NULL DEFAULT 0, \`isClosed\` tinyint NOT NULL DEFAULT 0, \`startAt\` timestamp NULL, \`resolvedAt\` timestamp NULL, \`lastNotifiedAt\` timestamp NULL, \`notificationCount\` tinyint UNSIGNED NOT NULL DEFAULT '0', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_f3b4688c649161cd07c9238272\` (\`workspaceId\`), INDEX \`IDX_8b080815ac84b9962658e72306\` (\`riskType\`), INDEX \`IDX_3c57595f4a7370a03a7e147b5a\` (\`isResolved\`), INDEX \`IDX_35e280f6fb68094d6f6ac133f6\` (\`isClosed\`), INDEX \`IDX_43ad6990b30da2a842261b3efc\` (\`startAt\`), INDEX \`IDX_521accefff4c7d4379c146c4a4\` (\`resolvedAt\`), INDEX \`IDX_2d0f1cc67c5b7927a2e9d9e6e0\` (\`lastNotifiedAt\`), INDEX \`IDX_d67ade5ec01df9385f4ed7b156\` (\`notificationCount\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_upload\` (\`id\` int NOT NULL AUTO_INCREMENT, \`userId\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`name\` varchar(255) NULL, \`mimeType\` varchar(255) NULL, \`type\` varchar(255) NULL, \`finalUrl\` varchar(255) NULL, \`doSpaceKey\` varchar(255) NULL, \`thumbnailUrl\` varchar(255) NULL, \`size\` bigint NOT NULL DEFAULT '0', \`width\` int NOT NULL DEFAULT '0', \`height\` int NOT NULL DEFAULT '0', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_e011de5febe328bf47e07ce9fe\` (\`userId\`), INDEX \`IDX_7c5c64b38604e3ad88da5e3129\` (\`workspaceId\`), INDEX \`IDX_c7c897684a409cabe7488cf5f6\` (\`name\`), INDEX \`IDX_4e36269e5b78d6297f0d96c46b\` (\`type\`), INDEX \`IDX_4543581626f44600110b23a10b\` (\`createdAt\`), INDEX \`IDX_ca2e32d2090d2d055f04bd93f1\` (\`updatedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_stats\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`users\` smallint UNSIGNED NOT NULL, \`collaborators\` smallint UNSIGNED NOT NULL, \`records\` int UNSIGNED NOT NULL, \`sendingEmails\` smallint UNSIGNED NOT NULL, \`sendingDomains\` smallint UNSIGNED NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_25b32eb6d6d0b68ba80daa1c5d\` (\`users\`), INDEX \`IDX_8dd8c475f6821dcd83487d4267\` (\`collaborators\`), INDEX \`IDX_daf8cb891d77fbe11d382800a9\` (\`records\`), INDEX \`IDX_40e94b0c2487309105482ad094\` (\`sendingEmails\`), INDEX \`IDX_b2d3b72f81f2b76b26e88461dd\` (\`sendingDomains\`), UNIQUE INDEX \`IDX_fd8e1d9275ec6665ef5af44b7f\` (\`workspaceId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_usage\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`hour\` int NOT NULL DEFAULT '0', \`day\` int NOT NULL DEFAULT '0', \`month\` int NOT NULL DEFAULT '0', \`year\` int NOT NULL DEFAULT '0', \`aiGeneration\` int NOT NULL DEFAULT '0', \`enrichment\` int NOT NULL DEFAULT '0', \`emailSent\` int NOT NULL DEFAULT '0', \`creditBilledInCents\` int NOT NULL DEFAULT '0', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_45c2da4d32780f4f0a6a7cede4\` (\`createdAt\`), INDEX \`IDX_983d1f951a1e8adb261cbd24e7\` (\`updatedAt\`), INDEX \`IDX_52cea74e9b2c5b44b6b53773e7\` (\`deletedAt\`), UNIQUE INDEX \`IDX_cef56e027183b39415cbb7fbc9\` (\`workspaceId\`, \`hour\`, \`day\`, \`month\`, \`year\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_cef56e027183b39415cbb7fbc9\` ON \`workspace_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_52cea74e9b2c5b44b6b53773e7\` ON \`workspace_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_983d1f951a1e8adb261cbd24e7\` ON \`workspace_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_45c2da4d32780f4f0a6a7cede4\` ON \`workspace_usage\``);
        await queryRunner.query(`DROP TABLE \`workspace_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_fd8e1d9275ec6665ef5af44b7f\` ON \`workspace_stats\``);
        await queryRunner.query(`DROP INDEX \`IDX_b2d3b72f81f2b76b26e88461dd\` ON \`workspace_stats\``);
        await queryRunner.query(`DROP INDEX \`IDX_40e94b0c2487309105482ad094\` ON \`workspace_stats\``);
        await queryRunner.query(`DROP INDEX \`IDX_daf8cb891d77fbe11d382800a9\` ON \`workspace_stats\``);
        await queryRunner.query(`DROP INDEX \`IDX_8dd8c475f6821dcd83487d4267\` ON \`workspace_stats\``);
        await queryRunner.query(`DROP INDEX \`IDX_25b32eb6d6d0b68ba80daa1c5d\` ON \`workspace_stats\``);
        await queryRunner.query(`DROP TABLE \`workspace_stats\``);
        await queryRunner.query(`DROP INDEX \`IDX_ca2e32d2090d2d055f04bd93f1\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_4543581626f44600110b23a10b\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_4e36269e5b78d6297f0d96c46b\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_c7c897684a409cabe7488cf5f6\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_7c5c64b38604e3ad88da5e3129\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_e011de5febe328bf47e07ce9fe\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP TABLE \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_d67ade5ec01df9385f4ed7b156\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_2d0f1cc67c5b7927a2e9d9e6e0\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_521accefff4c7d4379c146c4a4\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_43ad6990b30da2a842261b3efc\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_35e280f6fb68094d6f6ac133f6\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_3c57595f4a7370a03a7e147b5a\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_8b080815ac84b9962658e72306\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_f3b4688c649161cd07c9238272\` ON \`workspace_risk_log\``);
        await queryRunner.query(`DROP TABLE \`workspace_risk_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_dd5f61a9df2ebfbf8b90ba866c\` ON \`workspace_sender_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_d6a3a16d333244a340fe724186\` ON \`workspace_sender_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_8ace4eb912896b1f79478c0905\` ON \`workspace_sender_email\``);
        await queryRunner.query(`DROP TABLE \`workspace_sender_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_7ac4881d8984a6b289967543b4\` ON \`workspace_member_settings\``);
        await queryRunner.query(`DROP TABLE \`workspace_member_settings\``);
        await queryRunner.query(`DROP INDEX \`IDX_49e1b235ed0b102d72b8f299d6\` ON \`workspace_invitation\``);
        await queryRunner.query(`DROP INDEX \`IDX_00a522900f16bb56f1148cc258\` ON \`workspace_invitation\``);
        await queryRunner.query(`DROP INDEX \`IDX_353a007588fb0ce02e603f1a67\` ON \`workspace_invitation\``);
        await queryRunner.query(`DROP INDEX \`IDX_c060076f1277c3c957151ec132\` ON \`workspace_invitation\``);
        await queryRunner.query(`DROP TABLE \`workspace_invitation\``);
        await queryRunner.query(`DROP INDEX \`IDX_b4104c0c92e2afdca1127be445\` ON \`workspace_member\``);
        await queryRunner.query(`DROP INDEX \`IDX_03ce416ae83c188274dec61205\` ON \`workspace_member\``);
        await queryRunner.query(`DROP INDEX \`IDX_15b622cbfffabc30d7dbc52fed\` ON \`workspace_member\``);
        await queryRunner.query(`DROP TABLE \`workspace_member\``);
        await queryRunner.query(`DROP INDEX \`IDX_81a1d6b559e2b8dde569697477\` ON \`workspace_domain\``);
        await queryRunner.query(`DROP INDEX \`IDX_b140c6bbd50969886dd94488a3\` ON \`workspace_domain\``);
        await queryRunner.query(`DROP TABLE \`workspace_domain\``);
        await queryRunner.query(`DROP INDEX \`IDX_e12875dfb3b1d92d7d7c5377e2\` ON \`user\``);
        await queryRunner.query(`DROP TABLE \`user\``);
        await queryRunner.query(`DROP INDEX \`IDX_98f3bcf982a3db7219b2b3047b\` ON \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_69671cfda612c374ec5c852592\` ON \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_875861360ccf551467b277dd76\` ON \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_e0dadb504d672c0fa061a3a6a0\` ON \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_51f2194e4a415202512807d2f6\` ON \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_3a3bf40f63cb3d745a8c58b0af\` ON \`workspace\``);
        await queryRunner.query(`DROP TABLE \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_f68aa670f843bcd43f34f9cdaa\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_62045c733a0f3800e7cb8a5562\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_6f968d636b35d65f86310fa8a1\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_33bcd5be6c640fc6000abd9a3b\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_0709945c8b818ccb95b9e10f14\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_29b95b2aafa2795d6a0430e457\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_74cc15b3ff0f5dfb8193ff8f9a\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_c65ce89b56ac570ca8dda6908d\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP TABLE \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_5d08d8121bcb93359fd5c8c00f\` ON \`view\``);
        await queryRunner.query(`DROP INDEX \`IDX_e3e5907f868f10b24627e4982e\` ON \`view\``);
        await queryRunner.query(`DROP INDEX \`IDX_211aeda56439ea21150200ab6d\` ON \`view\``);
        await queryRunner.query(`DROP INDEX \`IDX_11b0d38c2a07f642397643870c\` ON \`view\``);
        await queryRunner.query(`DROP TABLE \`view\``);
        await queryRunner.query(`DROP INDEX \`IDX_b751e46e842cfadee2d68cc785\` ON \`workspace_billable_transaction\``);
        await queryRunner.query(`DROP INDEX \`IDX_ad81085a845846e4f0dc775bd5\` ON \`workspace_billable_transaction\``);
        await queryRunner.query(`DROP INDEX \`IDX_db4f85b3001cc59b991ad14e28\` ON \`workspace_billable_transaction\``);
        await queryRunner.query(`DROP INDEX \`IDX_602c8df8cf202aea4889fea862\` ON \`workspace_billable_transaction\``);
        await queryRunner.query(`DROP TABLE \`workspace_billable_transaction\``);
        await queryRunner.query(`DROP INDEX \`IDX_d9959ee7e17e2293893444ea37\` ON \`token\``);
        await queryRunner.query(`DROP INDEX \`IDX_5ce95d9321f7221f198907ae07\` ON \`token\``);
        await queryRunner.query(`DROP INDEX \`IDX_94f168faad896c0786646fa3d4\` ON \`token\``);
        await queryRunner.query(`DROP INDEX \`IDX_9e062dd9e27767637c7c48a98d\` ON \`token\``);
        await queryRunner.query(`DROP INDEX \`IDX_7e943f35ecba345eb3c898d700\` ON \`token\``);
        await queryRunner.query(`DROP TABLE \`token\``);
        await queryRunner.query(`DROP INDEX \`IDX_77dbeeb547dcfa35907431005b\` ON \`subscription\``);
        await queryRunner.query(`DROP INDEX \`IDX_944f71468e89554380d6c0d197\` ON \`subscription\``);
        await queryRunner.query(`DROP INDEX \`IDX_a644824d4e72109d2ed988895e\` ON \`subscription\``);
        await queryRunner.query(`DROP TABLE \`subscription\``);
        await queryRunner.query(`DROP INDEX \`IDX_960b5f19a132cb624277cafe30\` ON \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_f8350b6a10f5ec5dbf0efb0d5a\` ON \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_478244b5ec579a9ccf7e94f8a5\` ON \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_278bee369877fd3fe2d913a7b9\` ON \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_b644594912e8109679b9eeec40\` ON \`record\``);
        await queryRunner.query(`DROP TABLE \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_9175e059b0a720536f7726a88c\` ON \`settings\``);
        await queryRunner.query(`DROP TABLE \`settings\``);
        await queryRunner.query(`DROP INDEX \`IDX_49ff6e4d30aff91dbfdaa7b505\` ON \`link\``);
        await queryRunner.query(`DROP INDEX \`IDX_0457fcd59f565bf429ea43baf4\` ON \`link\``);
        await queryRunner.query(`DROP INDEX \`IDX_8d4f2def879981a56403d75c89\` ON \`link\``);
        await queryRunner.query(`DROP INDEX \`IDX_5f66e43b97ceeb7d40ee3116ba\` ON \`link\``);
        await queryRunner.query(`DROP TABLE \`link\``);
        await queryRunner.query(`DROP INDEX \`IDX_2ca2fff04ac03b8bf738f10ac2\` ON \`page_permission\``);
        await queryRunner.query(`DROP INDEX \`IDX_1c92bb3609947b3368d48f958f\` ON \`page_permission\``);
        await queryRunner.query(`DROP INDEX \`IDX_4f07900aafdb506ffb30f30271\` ON \`page_permission\``);
        await queryRunner.query(`DROP INDEX \`IDX_9ede8636cf4158970bc0a4aaa7\` ON \`page_permission\``);
        await queryRunner.query(`DROP TABLE \`page_permission\``);
        await queryRunner.query(`DROP INDEX \`IDX_401d46173265d542bd749a49cb\` ON \`domain\``);
        await queryRunner.query(`DROP INDEX \`IDX_0f1578799ebbe7b91b5b40fe4d\` ON \`domain\``);
        await queryRunner.query(`DROP TABLE \`domain\``);
        await queryRunner.query(`DROP INDEX \`IDX_298e8d85c446da9877db925123\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_5021d47dea711cebdc2b8b3232\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_181d08bb1a292753ad89ec74ea\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_58f346ec69270db5796f3cd6e9\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_527514df011f586dd730125d0b\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_3e419598ba888095f081633c51\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_b82c19c08afb292de4600d99e4\` ON \`page\``);
        await queryRunner.query(`DROP TABLE \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_3d0038df8ca900f5fda2ff896a\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_9eac3612452020c976207f37b0\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_7a269d560b28960db295d33d49\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_d8f3916286cf18be51c8a0f6c4\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_6be4f75c40f5f3878d18d9806c\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_41070461ad429c324c36752537\` ON \`document\``);
        await queryRunner.query(`DROP TABLE \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_a560c05515299e79d6f02f189a\` ON \`database\``);
        await queryRunner.query(`DROP INDEX \`IDX_6f3b16bc022dfb2d7fdb504889\` ON \`database\``);
        await queryRunner.query(`DROP INDEX \`IDX_f3ba71ac6f385135e39c8bd3cf\` ON \`database\``);
        await queryRunner.query(`DROP INDEX \`IDX_593e4680cd57e73e6c27bc309f\` ON \`database\``);
        await queryRunner.query(`DROP INDEX \`IDX_3a876646ed2427b5fd626ce1fc\` ON \`database\``);
        await queryRunner.query(`DROP TABLE \`database\``);
        await queryRunner.query(`DROP INDEX \`IDX_9e70801ef519e9085641dbf66a\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_d4cf868ed2c2f3e88e359c7072\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_b0858936373e415473f5afbab0\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_274e364d985587da0120b7ed3e\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_9dc28be4b9396cf111906e8514\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_074abcb00396b3a57791d6804e\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_e8c1df6f092195a80b90879485\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_a3f88f32b13a9ccfa44ddda49f\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_8b22d6ae028fa8c507ac835b41\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_a0a57537f86ca150b773715b71\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_0df3c9efe3c9524412ae716599\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_a4a07fb14ee6db2b9bc00417de\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_d71cbc6cc70729ae38517f62b4\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_1204d68e31d47d86d45e5092f7\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_a49e22533ba889df4b27b179dd\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_e0a8032b0417c10d343efd953e\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_891ab18aa964ac2f87f4592d02\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_16e46b929237871426202dd33b\` ON \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_e39b0d7bda783ebb9a266928f6\` ON \`campaign_email\``);
        await queryRunner.query(`DROP TABLE \`campaign_email\``);
        await queryRunner.query(`DROP INDEX \`IDX_d2625f9f444f128462f3980d0f\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_73ab13babe6ce113714ec8ba59\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_08135474cffdb8282c4ff37271\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_aa53dd2f032888a188b9380765\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_f5b0bf5ae20fe76bb278770bf5\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_217a2a9ddd813c0a5333d7e690\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_16d47ffb02d94252452a052155\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_0e03af5b087896e61a0b788dcf\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_3b241eb12b1b0324034cf8168f\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_5a0877c4a9da13621d124867a5\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_4f0678ebd019c0ffcc5f60c78c\` ON \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_cfe2d8c57b4fabb084971a0563\` ON \`campaign\``);
        await queryRunner.query(`DROP TABLE \`campaign\``);
        await queryRunner.query(`DROP INDEX \`IDX_3aceab20f0ba5dd17fa3224c78\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_784a7562ae3e367ab19d65122b\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_55dd05a4bbdd2296fd507b510a\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_5dbbb0d27f9380b22c57d37512\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_cb7610ce639e0ab20557c5d7c6\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_7ee967f1d1afed90c4014437a6\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_67572f7fe58e557913a1786368\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_288373b3a3bff5c6795b9205fb\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP TABLE \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_ebef6baa610503f8ac59d9e378\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_bdf6072736e3d4caa3330cfeac\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_226bdd92556029a470699b7d70\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_abb73edd3a08d07129552a2e6b\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_7eac1d4fda433baf8f1528ec55\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_802af9bcce445b1b56d1704b24\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_2623411e4c826319fd18482122\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_f0697f19d8ba48457a891fa70c\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_ca2c786768495c7d396021efbe\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_408735afcb72409ddb10f8dada\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP INDEX \`IDX_18f2192e7648bac46ffdcf14f9\` ON \`ai_content_generation_log\``);
        await queryRunner.query(`DROP TABLE \`ai_content_generation_log\``);
        await queryRunner.query(`DELETE FROM \`typeorm_metadata\` WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["GENERATED_COLUMN","recordsQuota","opendashboardv2","billing_cycle"]);
        await queryRunner.query(`DELETE FROM \`typeorm_metadata\` WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["GENERATED_COLUMN","collaboratorsQuota","opendashboardv2","billing_cycle"]);
        await queryRunner.query(`DELETE FROM \`typeorm_metadata\` WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["GENERATED_COLUMN","usersQuota","opendashboardv2","billing_cycle"]);
        await queryRunner.query(`DROP INDEX \`IDX_d791afc8fa9fd702974916459a\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_ca9c93b791243d3095bc5f8b54\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_87dd5727462d5834893cd7a1d5\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_8229390c53eed700a0c2321161\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_1841248cc721d7e3ea082e7a34\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_f6a47f9552eefe05e23413904e\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_2ba1eeb5cfe0f1c00597672d45\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_f502aa5f2f6c4e37c090b10846\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_a295f71822454de2b2ad452118\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_c0a438744b0122d92753f8a638\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_bd3bc6a1a895f9603b53b3484b\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_975316095831d0e4d889125218\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_bc7bfa85ebc427572ff76a7d14\` ON \`billing_cycle\``);
        await queryRunner.query(`DROP TABLE \`billing_cycle\``);
        await queryRunner.query(`DROP INDEX \`IDX_271eba6040bf7f3f322492932c\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_96f5b8f674eb85c6f34e0ed558\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_caa645b86d8db66739106100a2\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_478b04bbf56fe88743227d3f58\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_e635d1956359354c63a36205db\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_f1253e909ae2f1ea48c44d2d5a\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_72d2e57f824d488c48281819cd\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_2dd2ad6a696f31dfad4f20af7b\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_2b8bd3f00c1450118c2d417c51\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_2e3a9f9108eb8ea9dad7112699\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_056432793744bd25a4c572193e\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_2e69eec78cb1822dc859d3cab1\` ON \`activity\``);
        await queryRunner.query(`DROP INDEX \`IDX_26dd80869eb20b6e4733e513d4\` ON \`activity\``);
        await queryRunner.query(`DROP TABLE \`activity\``);
    }

}
