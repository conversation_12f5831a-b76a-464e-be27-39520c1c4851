import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {DomainConfig} from "./WorkspaceDomain";


@Entity()
export class Domain {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Column({type: 'varchar', nullable: false})
    addedByUserId: string

    @Column({type: 'varchar', nullable: false})
    subdomain: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    domain: string

    @Column({type: 'varchar', nullable: false, unique: true})
    fullAuthorizedDomain: string

    @Column({type: 'varchar', nullable: true, unique: true})
    sendgridDomainId: string

    @Column({type: 'json', nullable: true})
    configs: DomainConfig[]

    @Column({default: false, type: 'boolean'})
    isVerified: boolean

    @Column({type: 'timestamp', nullable: true})
    verifiedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}