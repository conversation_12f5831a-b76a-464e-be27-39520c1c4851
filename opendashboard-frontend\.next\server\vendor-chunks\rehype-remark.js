"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-remark";
exports.ids = ["vendor-chunks/rehype-remark"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-remark/index.js":
/*!*********************************************!*\
  !*** ./node_modules/rehype-remark/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* reexport safe */ hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_1__.all),\n/* harmony export */   \"default\": () => (/* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   defaultHandlers: () => (/* reexport safe */ hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.handlers),\n/* harmony export */   one: () => (/* reexport safe */ hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_2__.one)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-mdast */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/handlers/index.js\");\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-mdast */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/all.js\");\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-mdast */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/one.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/index.js */ \"(ssr)/./node_modules/rehype-remark/lib/index.js\");\n/**\n * @typedef {import('hast-util-to-mdast').Context} Context\n * @typedef {import('hast-util-to-mdast').H} H\n * @typedef {import('hast-util-to-mdast').Handle} Handle\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').Processor} Processor\n */\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLXJlbWFyay9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQ0EsYUFBYSxzQ0FBc0M7QUFDbkQsYUFBYSxnQ0FBZ0M7QUFDN0MsYUFBYSxxQ0FBcUM7QUFDbEQsYUFBYSxrQ0FBa0M7QUFDL0MsYUFBYSxvQ0FBb0M7QUFDakQ7O0FBRTREO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWh5cGUtcmVtYXJrL2luZGV4LmpzPzYxN2YiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0LXV0aWwtdG8tbWRhc3QnKS5Db250ZXh0fSBDb250ZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0LXV0aWwtdG8tbWRhc3QnKS5IfSBIXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0LXV0aWwtdG8tbWRhc3QnKS5IYW5kbGV9IEhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9saWIvaW5kZXguanMnKS5PcHRpb25zfSBPcHRpb25zXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi9pbmRleC5qcycpLlByb2Nlc3Nvcn0gUHJvY2Vzc29yXG4gKi9cblxuZXhwb3J0IHtkZWZhdWx0SGFuZGxlcnMsIGFsbCwgb25lfSBmcm9tICdoYXN0LXV0aWwtdG8tbWRhc3QnXG5leHBvcnQge2RlZmF1bHR9IGZyb20gJy4vbGliL2luZGV4LmpzJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-remark/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-remark/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/rehype-remark/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-to-mdast */ \"(ssr)/./node_modules/hast-util-to-mdast/lib/index.js\");\n/**\n * @typedef {import('hast-util-to-mdast').Options} Options\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n */\n\n\n\n/**\n * Plugin to bridge or mutate to rehype.\n *\n * If a destination is given, runs the destination with the new mdast\n * tree (bridge-mode).\n * Without destination, returns the mdast tree: further plugins run on that\n * tree (mutate-mode).\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `hast-util-to-mdast`.\n */\nconst rehypeRemark =\n  /**\n   * @type {(import('unified').Plugin<[Processor, Options?], HastRoot> & import('unified').Plugin<[Options?]|void[], HastRoot, MdastRoot>)}\n   */\n  (\n    /**\n     * @param {Processor|Options} [destination]\n     * @param {Options} [options]\n     */\n    function (destination, options) {\n      /** @type {Options|undefined} */\n      let settings\n      /** @type {Processor|undefined} */\n      let processor\n\n      if (typeof destination === 'function') {\n        processor = destination\n        settings = options || {}\n      } else {\n        settings = destination || {}\n      }\n\n      if (settings.document === undefined || settings.document === null) {\n        settings = Object.assign({}, settings, {document: true})\n      }\n\n      return processor ? bridge(processor, settings) : mutate(settings)\n    }\n  )\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (rehypeRemark);\n\n/**\n * Bridge-mode.\n * Runs the destination with the new mdast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], HastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run((0,hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.toMdast)(node, options), file, (error) => {\n      next(error)\n    })\n  }\n}\n\n/**\n * Mutate-mode.\n * Further transformers run on the mdast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], HastRoot, MdastRoot>}\n */\nfunction mutate(options = {}) {\n  return (node) => {\n    const result = /** @type {MdastRoot} */ ((0,hast_util_to_mdast__WEBPACK_IMPORTED_MODULE_0__.toMdast)(node, options))\n    return result\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-remark/lib/index.js\n");

/***/ })

};
;