import {getIntegrationEntryPath, getOAuth2Configs} from "./integrationHelpers";
import {createCoreApi} from "@opendashboard-inc/integration-core-api";


const core = createCoreApi({
    getIntegrationEntryPath: async (params) => {
        // const preResolvedPath = integrationPath ? `${integrationPath}?d=${Date.now()}` : ''
        // const resolvedPath = path.resolve(preResolvedPath);
        // // 🧠 You can customize this if integrations are built into a dist folder later
        // console.log("Integration Paths: ", {integrationPath, preResolvedPath, resolvedPath})
        const integrationPath = await getIntegrationEntryPath(params)

        // const resolvedPath = path.resolve(integrationPath || '');
        // delete require.cache[require.resolve(resolvedPath)];

        return integrationPath
    },
    getOAuth2Configs: async (integration) => {
        return await getOAuth2Configs(integration)
    },
});

export const getCoreApi = () => {
    return core
}
