import {
	Column,
	CreateDateColumn,
	DeleteDateColumn,
	Entity,
	Index,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from "typeorm";
import {AddOnsQuota, UsageLimits} from "../businessLogic/subscription";

@Entity()
export class BillingCycle {

	@PrimaryGeneratedColumn()
	id: number;

	@Index()
	@Column({type: "varchar"})
	workspaceId: string

	@Index()
	@Column({type: "varchar", nullable: true})
	stripeSubscriptionId: string

	@Index()
	@Column({type: "varchar", nullable: true})
	futureStripeSubscriptionId: string

	@Column({type: "varchar", nullable: true})
	planId: string

	@Column({type: "varchar", nullable: true})
	priceId: string

	@Index()
	@Column({type: "varchar", nullable: true})
	futurePriceId: string

	@Column({type: "int", default: 0})
	anchorDay: number

	@Column({type: "timestamp", nullable: true})
	startsAt: Date

	@Column({type: "timestamp", nullable: true})
	endsAt: Date

	@Column({type: "timestamp", nullable: true})
	endedAt: Date

	@Index()
	@Column({type: "bool", default: false})
	isActive: boolean

	@Index()
	@Column({type: "bool", default: false})
	isPaid: boolean

	@Index()
	@Column({type: "bool", default: false})
	isRenewing: boolean

	@Index()
	@Column({type: "decimal", default: 0})
	costInCents: number

	@Index()
	@Column({type: "decimal", default: 0})
	amountPaidInCents: number

	@Index()
	@Column({type: "timestamp", nullable: true})
	paidAt: Date

	@CreateDateColumn({type: 'timestamp', nullable: false})
	createdAt: Date

	@UpdateDateColumn({type: 'timestamp', nullable: false})
	updatedAt: Date

	@DeleteDateColumn({type: 'timestamp', nullable: true})
	deletedAt: Date

	@Column({type: 'json', nullable: false})
	cyclePlanQuota: UsageLimits

	@Column({type: 'json', nullable: false})
	cycleUsage: UsageLimits

	@Column({type: 'json', nullable: false})
	addOnsQuota: AddOnsQuota

	@Index()
	@Column({
		asExpression: "(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))",
		type: "int",
		generatedType: "STORED"

	})
	usersQuota: number

	@Index()
	@Column({
		asExpression: "(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))",
		type: "int",
		generatedType: "STORED"

	})
	collaboratorsQuota: number

	@Index()
	@Column({
		asExpression: "(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))",
		type: "int",
		generatedType: "STORED"

	})
	recordsQuota: number

	@Column({type: 'json', nullable: true, select: false})
	auditLog: string[]

	@Column({type: "json", nullable: true})
	meta: object

}