import {Column, CreateDate<PERSON>olumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {SelectData} from "opendb-app-db-utils/lib/typings/common";

export interface DatabaseDependencyMap {
    [key: string]: {
        dependsOn: string[]
    }
}

export interface ObjectMappings {
    databases: { [key: string]: string },
    records: { [key: string]: string },
    pages: { [key: string]: string },
    views: { [key: string]: string },
    documents: { [key: string]: string }
    workflows: { [key: string]: string }
}

export interface ItemSelectionMap {
    [key: string]: {
        viewIds: string[]
    } | undefined | null
}

export interface TemplateHomePage {
    databaseId?: string
    pageId?: string
    viewId?: string
}

export interface WorkflowItemSelectionMap {
    [key: string]: string
}

export interface TemplatePrepareData {
    databases: ItemSelectionMap
    pages: ItemSelectionMap
    workflows: WorkflowItemSelectionMap
    homePage?: TemplateHomePage
}

@Entity()
@Index(["templateId", "versionNumber"], {unique: true})
export class TemplateRelease {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    templateId: string

    @Column({type: 'int', default: 0, unsigned: true})
    versionNumber: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Column({type: 'json', nullable: true, select: false})
    dbProcessingOrder: SelectData[];

    @Column({type: 'json', nullable: true, select: false})
    dependencyMap: DatabaseDependencyMap;

    @Column({type: 'json', nullable: true, select: false})
    objectMappings: ObjectMappings;

    @Column({type: 'json', nullable: true})
    prepareData: TemplatePrepareData;

    @Column({type: "json", nullable: true})
    meta: { homePage?: TemplateHomePage } & object

    @Index()
    @Column({type: 'bool', default: false})
    isReady: boolean

    @Index()
    @Column({type: 'bool', default: false})
    isFeaturedInMarketplace: boolean

    @Column({type: 'text', nullable: true})
    releaseNote: string

    @Column({type: 'timestamp', nullable: true})
    featuredAt: Date

    @Column({type: 'timestamp', nullable: true})
    replacedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}