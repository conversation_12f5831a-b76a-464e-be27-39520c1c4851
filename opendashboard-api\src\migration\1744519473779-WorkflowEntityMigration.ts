import { MigrationInterface, QueryRunner } from "typeorm";

export class WorkflowEntityMigration1744519473779 implements MigrationInterface {
    name = 'WorkflowEntityMigration1744519473779'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workflow\` ADD \`errorCount\` int NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`workflow\` ADD \`error\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workflow\` ADD \`lastErrorAt\` timestamp NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_de0d9599cca98f0f39732ddef5\` ON \`workflow\` (\`errorCount\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_eaa3ec87eddd647ffa9a3a0827\` ON \`workflow\` (\`lastErrorAt\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_eaa3ec87eddd647ffa9a3a0827\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_de0d9599cca98f0f39732ddef5\` ON \`workflow\``);
        await queryRunner.query(`ALTER TABLE \`workflow\` DROP COLUMN \`lastErrorAt\``);
        await queryRunner.query(`ALTER TABLE \`workflow\` DROP COLUMN \`error\``);
        await queryRunner.query(`ALTER TABLE \`workflow\` DROP COLUMN \`errorCount\``);
    }

}
