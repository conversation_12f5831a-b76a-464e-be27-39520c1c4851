import {Workspace<PERSON>ontroller} from "./controller";
import {Routes} from "../../../routes";
import {verifyApi<PERSON><PERSON>} from "../../../middleware/verifyApiKey";

export const workspaceRoutes: Routes = {
    basePath: '/workspaces',
    middleware: [verifyApi<PERSON>ey],
    routes: {
        '/': {
            get: {
                controller: WorkspaceController,
                action: "getWorkspaces"
            }
        },
        '/:id': {
            get: {
                controller: WorkspaceController,
                action: "getWorkspace"
            }
        },
        '/:id/members': {
            get: {
                controller: WorkspaceController,
                action: "getMembers"
            }
        },
        '/:id/uploads': {
            post: {
                controller: WorkspaceController,
                action: "uploadNewWorkspaceFile"
            }
        },
        '/:id/databases': {
            get: {
                controller: WorkspaceController,
                action: "getDatabases"
            }
        },
        '/:id/databases/:databaseId': {
            get: {
                controller: WorkspaceController,
                action: "getDatabase"
            }
        },
        '/:id/databases/:databaseId/processed-records': {
            get: {
                controller: WorkspaceController,
                action: "getProcessedRecords"
            }
        },
        '/:id/databases/:databaseId/records': {
            get: {
                controller: WorkspaceController,
                action: "getRecords"
            },
            post: {
                controller: WorkspaceController,
                action: "addRecords"
            },
            patch: {
                controller: WorkspaceController,
                action: "updateRecords"
            },
            delete: {
                controller: WorkspaceController,
                action: "deleteRecords"
            },
        },
        // '/:id/databases/:databaseId/match-records': {
        //     post: {
        //         controller: WorkspaceController,
        //         action: "matchRecords"
        //     },
        // },

    }
};


