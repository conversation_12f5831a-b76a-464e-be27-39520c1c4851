const flatCache = require('flat-cache')
const path = require('path');

class Cache {
	private readonly name: any;
	private readonly path: any;
	private cache: any;
	private readonly expire: boolean | number;

	constructor(name, path, cacheTime = 0) {
		this.name = name
		this.path = path
		this.cache = flatCache.load(name, path)
		this.expire = cacheTime === 0 ? false : cacheTime * 1000 * 60
	}

	getKey(key) {
		const now = new Date().getTime();
		const value = this.cache.getKey(key);
		if (value === undefined || (value.expire !== false && value.expire < now)) {
			return undefined
		} else {
			return value.data
		}
	}

	setKey(key, value, expiry?: number) {
		const now = new Date().getTime();
		this.cache.setKey(key, {
			expire: expiry ? now + expiry : this.expire === false ? false : now + Number(this.expire),
			data: value
		})
	}

	removeKey(key) {
		this.cache.removeKey(key)
	}

	save() {
		this.cache.save(true)
	}

	remove() {
		flatCache.clearCacheById(this.name, this.path)
	}
}

const cache = new Cache('app-cache', path.resolve(__dirname,'../../.cache'), 10);


// const cacheOptions = {
// 	ttl: 90, // expires in 90 secs
// 	engine: 'in-file',
// }
// const cache = new Cacheman("file-cache", cacheOptions);

// const NodeCache = require( "node-cache" );
//
//
//
//
// const stdTTL=1
//
//
// const myCache = new NodeCache();
//
//
//
export const putInCache = (key: string, value: any, ttlSeconds: number) => {
	cache.setKey(key, value, ttlSeconds * 1000)
	cache.save()
	return true
}

export const getInCache = (key: string) => {
	return cache.getKey(key)
}

export const deleteInCache = (key: string) => {
	cache.removeKey(key)
	cache.save()
	return true
}

export const emptyCache = () => {
	cache.remove()
	cache.save()
	return true
}

