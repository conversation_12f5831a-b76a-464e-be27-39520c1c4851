import {Workflow} from "../../entity/workflow";
import {CreateWorkflowTaskData} from "../../entity/workflowTask";
import {CreateWorkflowInstanceData} from "../../entity/workflowInstance";
import {arrayDeDuplicate, generateUUID} from "opendb-app-db-utils/lib";
import {WorkflowInstanceService} from "../../service/workflowInstance";
import {WorkflowTaskService} from "../../service/workflowTask";
import {WorkflowService} from "../../service/workflow";
import {In, LessThanOrEqual} from "typeorm";
import {ScheduleTriggerWorkflowNode_Configs, ScheduleType, WorkflowStatus, WorkflowTriggerType} from "opendb-app-db-utils/lib/typings/workflow";
import {consoleLog} from "../logtail";
import {getNextScheduledDates} from "./getNextScheduledDates";
import {redisAcquireLock, redisReleaseLock} from "../../connection/redis";
import {ServerProcessingError} from "../../errors/AppError";
import {CRON_LOCK_KEYS, CurrentBillingCycle} from "../billing";
import {datePlusMinutes} from "opendb-app-db-utils/lib/methods/date";
import {redisPubSubPublish} from "../../redis-pubsub/publisher";
import {broadcastWorkflowActivity} from "../../socketio/workspace";
import {getIntegration} from "../integrationHelpers";
import {WorkspacePlanPriceIdMap} from "../subscription";
import {ExecuteWorkspaceIntegrationActionData, ExecuteWorkspaceIntegrationActionNoAuth, GetWorkspaceById, getWorkspaceMemberNoCacheNoAuth, MyWorkspaceMember} from "../workspace";
import {appUrl} from "../../config";
import {EmailButton, EmailUser, SendEmailWithContent} from "../email";
import {WorkspaceMemberRole} from "../../entity/WorkspaceMember";
import {Workspace} from "../../entity/Workspace";
import {handleError} from "../../config/error";


export interface WorkflowRunData {
    workflow: Workflow,
    // workflowId: string,
    triggerOutput: object,
    triggerNodeId: string
    databaseId?: string,
    recordId?: string,
    taskMeta?: object
    postTriggerUpdate?: Pick<Workflow, 'nextRunAt'>
}

export const StartWorkflowRuns = async (runDatas: WorkflowRunData[]) => {
    const instancesData: CreateWorkflowInstanceData[] = []
    const tasksData: CreateWorkflowTaskData[] = []

    for (let runData of runDatas) {
        const instanceData: CreateWorkflowInstanceData = {
            id: generateUUID(),
            workflowId: runData.workflow.id,
            databaseId: runData.databaseId,
            recordId: runData.recordId
        }

        const taskData: CreateWorkflowTaskData = {
            id: generateUUID(),
            workflowId: runData.workflow.id,
            instanceId: instanceData.id,
            nodeId: runData.triggerNodeId,
            taskOutput: runData.triggerOutput,
            executeAt: new Date(),
            meta: runData.taskMeta
        }

        instancesData.push(instanceData)
        tasksData.push(taskData)
    }
    const s = new WorkflowInstanceService()
    const s2 = new WorkflowTaskService()
    const s3 = new WorkflowService()

    const instances = await s.batchAdd(instancesData)
    const tasks = await s2.batchAdd(tasksData)

    const updateMap: { [key: string]: Partial<Workflow> } = {}
    const idsWithoutPostTriggerUpdate = arrayDeDuplicate(runDatas.filter(runData => !runData.postTriggerUpdate).map(runData => runData.workflow.id))
    for (let runData of runDatas) {
        if (idsWithoutPostTriggerUpdate.includes(runData.workflow.id)) {
            continue
        }
        updateMap[runData.workflow.id] = runData.postTriggerUpdate || {}
    }

    for (let [id, value] of Object.entries(updateMap)) {
        await s3.update({id}, {
            ...value,
            lastExecutedAt: new Date(),
            runsCount: () => `runsCount + 1`,
            errorCount: 0,
            error: null
        })
    }
    if (idsWithoutPostTriggerUpdate.length > 0) {
        await s3.update({id: In(idsWithoutPostTriggerUpdate)}, {
            lastExecutedAt: new Date(),
            runsCount: () => `runsCount + 1`,
            errorCount: 0,
            error: null
        })
    }

    const workflowIdMap: { [key: string]: Workflow } = {}
    for (let runData of runDatas) {
        workflowIdMap[runData.workflow.id] = runData.workflow
    }
    const uniqueWorkflowIds = Object.keys(workflowIdMap)
    for (let id of uniqueWorkflowIds) {
        const workflow = workflowIdMap[id]
        const newInstances = instances.filter(i => i.workflowId === id)
        const newTasks = tasks.filter(t => t.workflowId === id)

        broadcastWorkflowActivity(workflow.workspaceId, workflow, newInstances, newTasks)
    }
    triggerWorkflowRunner(`Started ${instances.length} workflow instances`)

    return {instances, tasks}
}

export const getPollingTimeoutForWorkspace = async (workspaceId: string) => {
    const billingCycle = await CurrentBillingCycle(workspaceId)
    const plan = WorkspacePlanPriceIdMap[billingCycle.planId || 'free']

    return plan.limits.pollingIntervalMins
}

export const FindAndTriggerScheduledWorkflows = async () => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.FIND_AND_TRIGGER_SCHEDULED_WORKFLOWS)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }
    const s = new WorkflowService()
    const workflows = await s.find({
        nextRunAt: LessThanOrEqual(datePlusMinutes(new Date(), 1)),
        status: WorkflowStatus.Published,
        // triggerType: WorkflowTriggerType.Schedule_OnSchedule
    })
    const noTriggerWorkflowIds: string[] = []

    const workspacePollingIntervalMap: { [key: string]: number } = {}

    const cache: WorkflowTriggerCache = {
        workspaces: {}
    }

    const runDatas: WorkflowRunData[] = []
    for (let workflow of workflows) {
        const definition = workflow.definition
        const triggerNode = definition.map[definition.triggerId]
        const triggerType = workflow.triggerType

        let nextRunAt: Date | undefined = undefined

        if (triggerType === WorkflowTriggerType.Schedule_OnSchedule) {
            const config = triggerNode.data.configs as ScheduleTriggerWorkflowNode_Configs
            if (config.type === ScheduleType.Recurring) {
                try {
                    nextRunAt = getNextScheduledDates(config, new Date(), 1)[0]
                } catch (e) {
                    consoleLog("Error getting next run at", e)
                }
            }
            runDatas.push({
                workflow,
                triggerOutput: {},
                triggerNodeId: definition.triggerId,
                postTriggerUpdate: {
                    nextRunAt
                }
            })
        } else {
            const typeSplit = triggerType.split(':')
            if (typeSplit.length < 2) {
                // not an integration trigger
                consoleLog("Not an integration trigger type", {workflowId: workflow.id, triggerType, typeSplit})
                noTriggerWorkflowIds.push(workflow.id)
                continue;
            }
            const integration = typeSplit[0]
            const trigger = typeSplit[1]
            const integrationInfo = await getIntegration(integration)
            if (!(integrationInfo && Object.keys(integrationInfo.triggers || {}).includes(trigger))) {
                // trigger not found
                consoleLog("Integration trigger not found", {workflowId: workflow.id, triggerType, integration, trigger})
                noTriggerWorkflowIds.push(workflow.id)
                continue;
            }
            const triggerInfo = (integrationInfo.triggers || {})[trigger]
            if (triggerInfo.type !== "POLLING") {
                consoleLog("Integration not a polling trigger", {workflowId: workflow.id, triggerType, integration, trigger, triggerInfo})
                noTriggerWorkflowIds.push(workflow.id)
                continue;
            }
            let pollingTimeoutMins = 30
            if (workspacePollingIntervalMap[workflow.workspaceId]) {
                pollingTimeoutMins = workspacePollingIntervalMap[workflow.workspaceId]
            } else {
                pollingTimeoutMins = await getPollingTimeoutForWorkspace(workflow.workspaceId)
                workspacePollingIntervalMap[workflow.workspaceId] = pollingTimeoutMins
            }
            nextRunAt = datePlusMinutes(new Date(), pollingTimeoutMins)
            const connectionId = triggerNode.data.connectionId
            const propsValue = triggerNode.data.configs || {}
            const data: ExecuteWorkspaceIntegrationActionData = {
                name: trigger,
                connectionId,
                propsValue,
                mode: "run",
                type: 'trigger',
            }
            try {
                const {result} = await ExecuteWorkspaceIntegrationActionNoAuth(workflow.workspaceId, integration, data, workflow.id)
                const instanceData: any[] = Array.isArray(result) ? result : [result]
                const runData: WorkflowRunData[] = instanceData.map((data) => ({
                    triggerNodeId: triggerNode.id,
                    triggerOutput: data,
                    workflow,
                    postTriggerUpdate: {
                        nextRunAt
                    }
                }))
                runDatas.push(...runData)
                consoleLog("Integration trigger executed successfully", {workflowId: workflow.id, triggerType, integration, trigger, data, newInstances: runData.length})
            } catch (e) {
                consoleLog("Error executing integration trigger", {workflowId: workflow.id, triggerType, integration, trigger, data, e})
                handleError(e, {workflowId: workflow.id, ...data}, false)
                await HandleWorkflowTriggerError(workflow, e, cache)
            }
        }
    }
    if (noTriggerWorkflowIds.length > 0) {
        await s.update({id: In(noTriggerWorkflowIds)}, {nextRunAt: null})
    }
    await StartWorkflowRuns(runDatas)

    await redisReleaseLock(CRON_LOCK_KEYS.FIND_AND_TRIGGER_SCHEDULED_WORKFLOWS)

    return {
        length: workflows.length,
        ids: workflows.map(w => w.id)
    }
}

interface WorkflowTriggerCache {
    workspaces: {
        [key: string]: {
            workspace: Workspace
            owner: MyWorkspaceMember
            admin: MyWorkspaceMember[]
        }
    }
}

const HandleWorkflowTriggerError = async (workflow: Workflow, e: Error, cache: WorkflowTriggerCache) => {
    // retry up to 5 times and pause, send an email to the user
    const message = e.message

    const errorCount = workflow.errorCount || 0
    let shouldRetry = errorCount < 5

    if (!cache.workspaces[workflow.workspaceId]) {
        const workspace = await GetWorkspaceById(workflow.workspaceId)
        const members = await getWorkspaceMemberNoCacheNoAuth(workflow.workspaceId)
        const owner = members.find(k => k.user.id === workspace.ownerId)
        const admin = members.filter(k => k.workspaceMember.role === WorkspaceMemberRole.Admin)

        cache.workspaces[workflow.workspaceId] = {
            workspace,
            owner,
            admin
        }
    }
    const s = new WorkflowService()
    if (shouldRetry) {
        await s.update({id: workflow.id}, {
            errorCount: () => `errorCount + 1`,
            error: message,
            lastExecutedAt: new Date(),
            nextRunAt: datePlusMinutes(new Date(), 5),
        })
    } else {
        await s.update({id: workflow.id}, {
            errorCount: () => `errorCount + 1`,
            error: message,
            lastExecutedAt: new Date(),
            status: WorkflowStatus.Paused,
            nextRunAt: null,
        })
    }

    const {workspace, owner, admin} = cache.workspaces[workflow.workspaceId]

    const workflowUrl = appUrl(`/${workspace.domain}/workflows/${workflow.id}`);

    const to: EmailUser = {
        email: owner.user.email,
        name: `${owner.user.firstName} ${owner.user.lastName}`.trim()
    };

    const cc = admin.map(m => m.user.email)

    let errorNotice = ''
    if (shouldRetry) {
        // as this is a polling trigger, we will retry the workflow automatically up to 5 times before pausing the workflow
        errorNotice = `
        <p style="color:#313539;">
           This workflow has failed ${errorCount} times. We will retry the workflow automatically up to 5 times, after which it will be paused.
        </p>
        `
    } else {
        // pause the workflow
        errorNotice = `
        <p style="color:#313539;">
            This workflow has failed ${errorCount} consecutive times and has been paused. You can edit the workflow and re-publish it to resume the workflow.
        </p>
        `
    }

    const body = `
    <p style="font-size: 24px; font-weight: 600;">You've exceeded your allocated quota</p>
    <p style="color:#313539;">Hello there,</p>
    <p style="color:#313539;">
        We ran into a problem with fetching data for your workflow 
        <strong>
            <a href="${workflowUrl}" style="color: #313539; text-decoration: underline; font-weight: 600;">
                ${workflow.name || 'Untitled'}
            </a>
        </strong>, see error below:
    </p>
     <p style="color:red;">${message}</p>
       ${errorNotice}
    `;

    const button: EmailButton = {
        label: "View Workflow",
        url: workflowUrl
    };
    const subject = `Workflow ${workflow.name || 'Untitled'} has failed please check`;

    const bcc = ['<EMAIL>']

    const messageId = await SendEmailWithContent(to, subject, body, button, null, true, undefined, undefined, undefined, undefined, false, cc, bcc);

    consoleLog("Handle workflow trigger email sent", {messageId, owner, admin, shouldRetry, bcc});
    return {messageId};
}

export const CountWorkflows = async (workspaceId: string, triggerType: WorkflowTriggerType, triggerObjectId: string) => {
    const s = new WorkflowService()
    return await s.count({
        workspaceId,
        triggerType,
        triggerObjectId,
        status: WorkflowStatus.Published
    })
}

export const triggerWorkflowRunner = (message: string) => {
    setTimeout(() => {
        // delay for one second to allow the workflow instance to be created
        redisPubSubPublish('start-workflow-runner', message).then() // fire and forget
    }, 1000)

}
