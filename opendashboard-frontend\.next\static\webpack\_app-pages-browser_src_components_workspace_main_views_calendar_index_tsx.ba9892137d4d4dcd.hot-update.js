"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, selectedEvent, onClick, canEditData } = param;\n    const handleDragStart = (e)=>{\n        if (!canEditData) return;\n        e.dataTransfer.setData(\"text/plain\", JSON.stringify(event));\n        // Get the actual event element to match its size and appearance\n        const eventElement = e.currentTarget;\n        const eventRect = eventElement.getBoundingClientRect();\n        const containerElement = document.getElementById(\"week-view-container\");\n        const containerRect = containerElement === null || containerElement === void 0 ? void 0 : containerElement.getBoundingClientRect();\n        // Calculate the actual width available for events in week view (1/7 of container minus time column)\n        const timeColumnWidth = 80; // w-20 = 80px\n        const availableWidth = containerRect ? (containerRect.width - timeColumnWidth) / 7 - 8 : 150; // divide by 7 days, subtract padding\n        // Create a drag image that exactly matches the original event size\n        const dragImg = document.createElement(\"div\");\n        dragImg.innerHTML = '\\n      <div style=\"\\n        background: white;\\n        color: #1f2937;\\n        border: 1px solid #d1d5db;\\n        border-radius: 6px;\\n        padding: 8px 12px;\\n        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n        font-size: 12px;\\n        font-weight: 500;\\n        width: '.concat(Math.max(availableWidth, 120), \"px;\\n        height: \").concat(Math.max(eventRect.height, 40), 'px;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        opacity: 0.95;\\n      \">\\n        <div style=\"font-weight: 600; margin-bottom: 2px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: #1f2937; line-height: 1.3;\">\\n          ').concat(event.title, '\\n        </div>\\n        <div style=\"font-size: 10px; opacity: 0.7; color: #6b7280; line-height: 1.3;\">\\n          ').concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n        </div>\\n      </div>\\n    \");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-2000px\";\n        dragImg.style.left = \"-2000px\";\n        dragImg.style.pointerEvents = \"none\";\n        document.body.appendChild(dragImg);\n        // Use the center of the drag image for better visual feedback\n        const dragWidth = Math.max(availableWidth, 120);\n        e.dataTransfer.setDragImage(dragImg, dragWidth / 2, eventRect.height / 2);\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: handleDragStart,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute left-1 right-1 px-2 py-1.5 rounded-md text-xs shadow-sm border cursor-pointer\", \"transition-all duration-200 hover:shadow-md\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700\"),\n        style: style,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium truncate leading-tight\",\n                children: event.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-[10px] mt-0.5 opacity-75\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(new Date(event.start), \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CalendarEventItem;\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onClick, onDrop, canEditData } = param;\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: handleDragOver,\n        onDrop: onDrop,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative border-r border-b border-gray-100\", \"cursor-pointer\"),\n        onClick: onClick,\n        style: {\n            height: \"60px\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, onEventDrop } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_4__.useScreenSize)();\n    const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, {\n        weekStartsOn: 0\n    });\n    const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, {\n        weekStartsOn: 0\n    });\n    const days = Array.from({\n        length: 7\n    }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(weekStart, i));\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day));\n    const currentTimePosition = todayIndex !== -1 ? {\n        dayIndex: todayIndex,\n        hour: new Date().getHours(),\n        minutes: new Date().getMinutes()\n    } : null;\n    // Helper function for event duration calculation\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(30, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Check if there are any events for the current week\n    const weekEvents = events.filter((event)=>{\n        const eventStart = new Date(event.start);\n        return eventStart >= weekStart && eventStart <= weekEnd;\n    });\n    const handleDrop = (day, hour, e)=>{\n        e.preventDefault();\n        if (!canEditData) return;\n        try {\n            const eventData = JSON.parse(e.dataTransfer.getData(\"text/plain\"));\n            const originalDate = new Date(eventData.start);\n            // Calculate minutes based on drop position within the time slot\n            const timeSlotHeight = 60; // height of time slot in pixels\n            const rect = e.target.getBoundingClientRect();\n            const relativeY = e.clientY - rect.top;\n            const minutes = Math.floor(relativeY / timeSlotHeight * 60);\n            // Create new date with calculated minutes\n            const newDate = new Date(day);\n            newDate.setHours(hour, minutes, 0, 0);\n            // Check if the new date is the same as the original\n            if (newDate.getTime() === originalDate.getTime()) {\n                return;\n            }\n            onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(eventData, newDate);\n        } catch (error) {\n            console.error(\"Error handling drop:\", error);\n        }\n    };\n    if (weekEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b bg-secondary sticky top-0 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex\", isMobile && \"overflow-x-auto\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-r sticky left-0 bg-secondary z-10\", isMobile ? \"w-14\" : \"w-20\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-1\", isMobile && \"min-w-[700px]\"),\n                                children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 text-center cursor-pointer transition-colors\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, selectedDate) ? \"bg-accent\" : \"hover:bg-accent/50\", isMobile ? \"py-3 px-1\" : \"py-4 px-2\"),\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-xs\" : \"text-sm\"),\n                                                children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, isMobile ? \"EEE\" : \"EEE\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                                children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, \"d\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events this week\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: \"Your week is completely free. Add some events to get organized!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>openAddEventForm(selectedDate),\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-secondary sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex\", isMobile && \"overflow-x-auto\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-r sticky left-0 bg-secondary z-10\", isMobile ? \"w-14\" : \"w-20\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-1\", isMobile && \"min-w-[700px]\"),\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 text-center cursor-pointer transition-colors\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, selectedDate) ? \"bg-accent\" : \"hover:bg-accent/50\", isMobile ? \"py-3 px-1\" : \"py-4 px-2\"),\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-xs\" : \"text-sm\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, isMobile ? \"EEE\" : \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative bg-white\", isMobile ? \"overflow-x-auto\" : \"overflow-auto\"),\n                id: \"week-view-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(isMobile && \"min-w-[700px]\"),\n                        children: hours.map((hour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex border-b border-gray-50 hover:bg-gray-25 transition-colors\",\n                                style: {\n                                    height: \"60px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 bg-white z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(), hour), \"h\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-[10px] text-gray-400\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(), hour), \"a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    days.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                            day: day,\n                                            hour: hour,\n                                            canEditData: canEditData,\n                                            onClick: ()=>{\n                                                if (canEditData) {\n                                                    const newDate = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, hour);\n                                                    openAddEventForm(newDate);\n                                                }\n                                            },\n                                            onDrop: (e)=>handleDrop(day, hour, e),\n                                            children: events.filter((event)=>{\n                                                const eventDate = new Date(event.start);\n                                                return (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(eventDate, day) && eventDate.getHours() === hour;\n                                            }).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                                    event: event,\n                                                    selectedEvent: selectedEvent,\n                                                    canEditData: canEditData,\n                                                    style: {\n                                                        top: \"\".concat(new Date(event.start).getMinutes() / 60 * 100, \"%\"),\n                                                        height: \"\".concat(Math.max(30, getEventDurationInMinutes(event) / 60 * 100), \"%\"),\n                                                        zIndex: selectedEvent === event.id ? 20 : 10\n                                                    },\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        const container = document.getElementById(\"week-view-container\");\n                                                        if (container) {\n                                                            savedScrollTop.current = container.scrollTop;\n                                                        }\n                                                        setSelectedEvent(event.id);\n                                                        handleEventClick(event);\n                                                    }\n                                                }, event.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, hour, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 right-0 flex items-center z-50 pointer-events-none\",\n                        style: {\n                            top: \"\".concat(currentTimePosition.hour * 60 + currentTimePosition.minutes, \"px\"),\n                            width: isMobile ? \"calc(100% - 14px)\" : \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex justify-end pr-3 sticky left-0 z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-red-500 border-2 border-white rounded-full shadow-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WeekView, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_4__.useScreenSize\n    ];\n});\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"TimeSlot\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});