import * as React from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { cn } from "@/lib/utils";

export type CalendarProps = {
  className?: string;
  mode?: "single" | "range" | "multiple";
  selected?: Date | null;
  onSelect?: (date: Date) => void;
  components?: any;
  showOutsideDays?: boolean;
};

export function Calendar({
  className,
  mode = "single",
  selected,
  onSelect,
  ...props
}: CalendarProps) {
  const handleChange = (date: Date | null, event: React.SyntheticEvent<any, Event> | undefined) => {
    if (onSelect && date) {
      onSelect(date);
    }
  };

  return (
    <div className={cn("p-0", className)}>
      <DatePicker
        selected={selected}
        onChange={handleChange}
        inline
        showMonthDropdown
        showYearDropdown
        dropdownMode="select"
        calendarClassName="shadow-none"
        {...props}
      />
      <style jsx global>{`
        .react-datepicker {
          border: none;
          border-radius: 0;
          font-family: inherit;
          background: transparent;
          width: 100%;
        }
        .react-datepicker__header {
          background: transparent;
          border-bottom: 1px solid #e5e7eb;
          border-radius: 0;
          padding-top: 0.5rem;
        }
        /* Add spacing between month/year text and dropdowns */
        .react-datepicker__header select {
          margin: 0 0.5rem;
        }
        .react-datepicker__day-names {
          margin-top: 0.5rem;
        }
        .react-datepicker__day-name {
          color: #6b7280;
          font-size: 0.75rem;
        }
        .react-datepicker__day {
          margin: 0.2rem;
          border-radius: 0;
          color: #374151;
          font-size: 0.75rem;
        }
        .react-datepicker__day--selected {
          background-color: #1e293b;
          border-radius: 9999px;
          color: white;
        }
        .react-datepicker__day--today {
          font-weight: 500;
        }
        .react-datepicker__day:hover {
          border-radius: 9999px;
          background-color: #e5e7eb;
        }
        .react-datepicker__day--outside-month {
          color: #9ca3af;
        }
        .react-datepicker__month-select,
        .react-datepicker__year-select {
          border: 1px solid #e5e7eb;
          border-radius: 0.25rem;
          padding: 0.25rem;
          font-size: 0.75rem;
          background-color: white;
          margin-left: 0.5rem;
        }
        .react-datepicker__current-month {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-right: 0.5rem;
        }
        .react-datepicker__navigation {
          top: 0.5rem;
        }
        .react-datepicker__month-container {
          width: 100%;
        }
        /* Add a line under the calendar */
        .react-datepicker__month {
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.5rem;
        }
      `}</style>
    </div>
  );
}
