import {isLocal, isProduction} from "../../config";

const gettingStartedContentJSON = [
    {
        "id": "0123ecc1-9390-4d7f-91f4-7c4f4b0a0b9c",
        "type": "paragraph",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Thank you for signing up and choosing Opendashboard to power your business. We're here to help you simplify your operations, improve collaboration, and drive growth.",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "8442f5d0-b89d-4c0d-aa48-ba6e622ad4b0",
        "type": "heading",
        "props": {
            "level": 3,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Our Promise to You",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": []
    },
    {
        "id": "01885ac9-fb7d-4fa7-833d-f074e9763ec1",
        "type": "paragraph",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "We’re committed to providing a platform that grows with your business—whether you're managing projects, tracking sales, or analyzing performance.",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "6d307cf6-3167-4b98-8a83-797c494ded30",
        "type": "heading",
        "props": {
            "level": 3,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Getting Started",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": []
    },
    {
        "id": "6a606ea2-6887-4fbf-99e2-513349c2eeb6",
        "type": "paragraph",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Here’s a quick checklist to get you up and running:",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "28a01c41-8343-4828-a3e5-88099e618c63",
        "type": "checkListItem",
        "props": {
            "checked": false,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Watch the tutorial video",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": [
            {
                "id": "53a0c37a-d09e-417b-90f4-ea166f58fd2c",
                "type": "bulletListItem",
                "props": {
                    "textColor": "default",
                    "textAlignment": "left",
                    "backgroundColor": "default"
                },
                "content": [
                    {
                        "text": "Learn the ins and outs of Opendashboard in just a few minutes. ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "href": "https://www.youtube.com/watch?v=M_g2rkSFseM&list=PLpsRGo3XDw1MBwyGTUOHBw9WtfstiPR1T",
                        "type": "link",
                        "content": [
                            {
                                "text": "Watch now on YouTube",
                                "type": "text",
                                "styles": {}
                            }
                        ]
                    },
                    {
                        "text": ".",
                        "type": "text",
                        "styles": {}
                    }
                ],
                "children": []
            }
        ]
    },
    {
        "id": "12efacaf-e70f-48c0-bc11-d98a9dd98b4a",
        "type": "checkListItem",
        "props": {
            "checked": false,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Invite your teammates",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": [
            {
                "id": "8857de16-7adf-4d9c-a6ec-9d268a098c96",
                "type": "bulletListItem",
                "props": {
                    "textColor": "default",
                    "textAlignment": "left",
                    "backgroundColor": "default"
                },
                "content": [
                    {
                        "text": "Go to ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "text": "Settings > Members",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    },
                    {
                        "text": " and add team members.",
                        "type": "text",
                        "styles": {}
                    }
                ],
                "children": []
            },
            {
                "id": "e450e450-54dc-48a1-809e-d6ee928b6398",
                "type": "bulletListItem",
                "props": {
                    "textColor": "default",
                    "textAlignment": "left",
                    "backgroundColor": "default"
                },
                "content": [
                    {
                        "text": "Assign roles: ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "text": "Admin",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    },
                    {
                        "text": ", ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "text": "Member",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    },
                    {
                        "text": ", or ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "text": "Collaborator",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    },
                    {
                        "text": " to control access levels.",
                        "type": "text",
                        "styles": {}
                    }
                ],
                "children": []
            }
        ]
    },
    {
        "id": "f54abf0b-aa73-49aa-8e23-75e1203bb15f",
        "type": "checkListItem",
        "props": {
            "checked": false,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Explore templates",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": []
    },
    {
        "id": "f1bd86b3-24fa-40c8-bba8-82bd493ded18",
        "type": "bulletListItem",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Check out our ",
                "type": "text",
                "styles": {}
            },
            {
                "text": "Templates",
                "type": "text",
                "styles": {
                    "bold": true
                }
            },
            {
                "text": " page to discover pre-built templates that can be installed and customized to fit your needs.",
                "type": "text",
                "styles": {}
            },
            {
                "text": " ",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": []
    },
    {
        "id": "67b8b123-5a12-4cf4-95c6-bf133ca6db31",
        "type": "checkListItem",
        "props": {
            "checked": false,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Add your data",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": [
            {
                "id": "8a159668-2a33-4431-a295-a9504d34e6e4",
                "type": "bulletListItem",
                "props": {
                    "textColor": "default",
                    "textAlignment": "left",
                    "backgroundColor": "default"
                },
                "content": [
                    {
                        "text": "Import existing project or customer data into Opendashboard to hit the ground running.",
                        "type": "text",
                        "styles": {}
                    }
                ],
                "children": []
            }
        ]
    },
    {
        "id": "b0b2e267-5370-4bb3-9e3e-a08e3bfaf1a8",
        "type": "checkListItem",
        "props": {
            "checked": false,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Customize your dashboard",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": [
            {
                "id": "6a0a1373-8c6d-45cb-835a-b0cef6693fa6",
                "type": "bulletListItem",
                "props": {
                    "textColor": "default",
                    "textAlignment": "left",
                    "backgroundColor": "default"
                },
                "content": [
                    {
                        "text": "Personalize your dashboard with widgets, layouts, and the data you care about most.",
                        "type": "text",
                        "styles": {}
                    }
                ],
                "children": []
            }
        ]
    },
    {
        "id": "6468603f-7227-41c6-b501-bb82d361701e",
        "type": "checkListItem",
        "props": {
            "checked": false,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Need support?",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": [
            {
                "id": "b1d257f8-aee3-4f3d-b5e5-42288f4c5c14",
                "type": "bulletListItem",
                "props": {
                    "textColor": "default",
                    "textAlignment": "left",
                    "backgroundColor": "default"
                },
                "content": [
                    {
                        "text": "Click the ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "text": "?",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    },
                    {
                        "text": " button in the bottom-right corner and select ",
                        "type": "text",
                        "styles": {}
                    },
                    {
                        "text": "Chat with Support",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    },
                    {
                        "text": " to connect with our team for help.",
                        "type": "text",
                        "styles": {}
                    }
                ],
                "children": []
            }
        ]
    },
    {
        "id": "c9ed535d-c224-4550-b5aa-53a890fe3ac1",
        "type": "heading",
        "props": {
            "level": 3,
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "Helpful Resources",
                "type": "text",
                "styles": {
                    "bold": true
                }
            }
        ],
        "children": []
    },
    {
        "id": "288c8503-86af-478e-a1f8-3c1a04f8d5c5",
        "type": "bulletListItem",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "href": "/templates",
                "type": "link",
                "content": [
                    {
                        "text": "Opendashboard Templates",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    }
                ]
            },
            {
                "text": ": Explore ready-made templates to save time.",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "81cd82ba-9d51-4a21-bd1a-d54f6b5b62c6",
        "type": "bulletListItem",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "href": "https://community.opendashboard.app/",
                "type": "link",
                "content": [
                    {
                        "text": "Help Center",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    }
                ]
            },
            {
                "text": ": FAQs and step-by-step guides for common tasks.",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "865c7d11-d340-4f9d-8319-632eac5682b0",
        "type": "bulletListItem",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "href": "https://community.opendashboard.app/",
                "type": "link",
                "content": [
                    {
                        "text": "Community",
                        "type": "text",
                        "styles": {
                            "bold": true
                        }
                    }
                ]
            },
            {
                "text": ": Join other users to share tips and best practices.",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "4e5b1a7b-1fd3-42ea-8bd0-566106796986",
        "type": "paragraph",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [
            {
                "text": "We’re thrilled to have you onboard and can’t wait to see what you accomplish with Opendashboard!",
                "type": "text",
                "styles": {}
            }
        ],
        "children": []
    },
    {
        "id": "c5942a24-6036-428f-843c-0eb5c7ab53c2",
        "type": "paragraph",
        "props": {
            "textColor": "default",
            "textAlignment": "left",
            "backgroundColor": "default"
        },
        "content": [],
        "children": []
    }
]
const gettingStartedContentText = `
Thank you for signing up and choosing Opendashboard to power your business.
We're here to help you simplify your operations, improve collaboration, and
drive growth.


OUR PROMISE TO YOU

We’re committed to providing a platform that grows with your business—whether
you're managing projects, tracking sales, or analyzing performance.


GETTING STARTED

Here’s a quick checklist to get you up and running:

Watch the tutorial video

Learn the ins and outs of Opendashboard in just a few minutes. Watch now on
YouTube
[https://www.youtube.com/watch?v=M_g2rkSFseM&list=PLpsRGo3XDw1MBwyGTUOHBw9WtfstiPR1T].

Invite your teammates

Go to Settings > Members and add team members.

Assign roles: Admin, Member, or Collaborator to control access levels.

Explore templates

Check out our Templates page to discover pre-built templates that can be
installed and customized to fit your needs.

Add your data

Import existing project or customer data into Opendashboard to hit the ground
running.

Customize your dashboard

Personalize your dashboard with widgets, layouts, and the data you care about
most.

Need support?

Click the ? button in the bottom-right corner and select Chat with Support to
connect with our team for help.


HELPFUL RESOURCES

Opendashboard Templates [/templates]: Explore ready-made templates to save time.

Help Center [https://community.opendashboard.app/]: FAQs and step-by-step guides
for common tasks.

Community [https://community.opendashboard.app/]: Join other users to share tips
and best practices.

We’re thrilled to have you onboard and can’t wait to see what you accomplish
with Opendashboard!


`

export const GettingStartedDoc = () => {
    return {contentText: gettingStartedContentText, contentJSON: gettingStartedContentJSON, docTitle: '👋 Welcome to Opendashboard'}
}


export const TemplateOnboardingMapping = Object.freeze({
        sales: isProduction() ? '' : isLocal() ? 'f6a7d959-1a29-43e9-ab80-71fb39b2148c' : '',
        projects: isProduction() ? '' : isLocal() ? '' : '',
        marketing: isProduction() ? '' : isLocal() ? '' : '',
        hr: isProduction() ? '' : isLocal() ? '' : '',
    }
)
