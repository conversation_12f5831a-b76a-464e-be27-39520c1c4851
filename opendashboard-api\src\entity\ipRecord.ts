import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

export interface Coordinate {
    latitude: string
    longitude: string
}

export interface CreateIpRecord {
    ipAddress: string
    city: string
    state: string
    country: string
    timezone: string
    coordinates: Coordinate
    meta: object
}

@Entity()
export class IpRecord {

    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({type: "varchar", nullable: false})
    ipAddress: string

    @Column({type: "varchar"})
    city: string

    @Column({type: "varchar"})
    state: string

    @Column({type: "varchar"})
    country: string

    @Column({type: "varchar"})
    timezone: string

    @Column({type: "json", nullable: true})
    coordinates: Coordinate

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: string

    @Index()
    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: string

    @Index()
    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt: Date;

    @Column({type: 'json', nullable: true,})
    meta: object


}