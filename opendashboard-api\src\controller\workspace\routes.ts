import {Routes} from "../../routes";
import {WorkspaceController} from "./controller"
import {verifyJWT} from "../../middleware/verifyJWT";

export const workspaceRoutes: Routes = {
    basePath: '/workspaces',
    middleware: [verifyJWT],
    routes: {
        '/': {
            get: {controller: WorkspaceController, action: "getWorkspaces"},
            post: {controller: WorkspaceController, action: "createWorkspace"}
        },
        '/v2': {
            post: {controller: WorkspaceController, action: "createWorkspaceViaOnboarding"}
        },
        '/domain/:domain': {
            get: {controller: WorkspaceController, action: "getWorkspaceByDomain"}
        },
        '/:id': {
            get: {controller: WorkspaceController, action: "getWorkspace"},
            patch: {controller: WorkspaceController, action: "updateWorkspace"},
            delete: {controller: WorkspaceController, action: "deleteWorkspace"}
        },
        '/:id/logo': {
            patch: {controller: WorkspaceController, action: "updateWorkspace<PERSON>ogo"}
        },
        '/:id/complete-setup': {
            post: {controller: Workspace<PERSON>ontroller, action: "completeSetup"},
        },
        '/:id/switch-to': {
            post: {controller: WorkspaceController, action: "switchToWorkspace"}
        },
        '/:id/suggest-import-map': {
            post: {controller: WorkspaceController, action: "suggestImportMap"}
        },
        '/:id/support-access': {
            patch: {controller: WorkspaceController, action: "toggleSupportAccess"}
        },
        // '/:id/leave': {
        //     patch: {controller: WorkspaceController, action: "toggleSupportAccess"}
        // },
        '/:id/members': {
            get: {controller: WorkspaceController, action: "getMembers"},
            post: {controller: WorkspaceController, action: "inviteMember"},
            patch: {controller: WorkspaceController, action: "updateMember"},
            delete: {controller: WorkspaceController, action: "deleteMember"}
        },
        '/:id/members/:userId/make-owner': {
            post: {controller: WorkspaceController, action: "makeOwner"},
        },
        '/:id/invitations/:inviteId/resend': {
            post: {controller: WorkspaceController, action: "resendInvitation"}
        },
        '/:id/invitations/:inviteId': {
            delete: {controller: WorkspaceController, action: "deleteInvitation"}
        },
        '/:id/invitation': {
            get: {controller: WorkspaceController, action: "getInvitation"}
        },
        '/:id/invitation/accept': {
            post: {controller: WorkspaceController, action: "acceptInvitation"}
        },
        '/:id/invitation/decline': {
            post: {controller: WorkspaceController, action: "declineInvitation"}
        },
        '/:id/senders': {
            get: {controller: WorkspaceController, action: "getSenders"},
            post: {controller: WorkspaceController, action: "addSender"}
        },
        '/:id/senders/:senderId': {
            delete: {controller: WorkspaceController, action: "deleteSender"}
        },
        '/:id/senders/:senderId/prompt-verification': {
            post: {controller: WorkspaceController, action: "promptSenderVerification"}
        },
        '/:id/domains/:domainId/verify': {
            post: {controller: WorkspaceController, action: "verifyDomain"}
        },
        '/:id/domains/:domainId': {
            delete: {controller: WorkspaceController, action: "deleteDomain"}
        },
        '/:id/usage': {
            get: {controller: WorkspaceController, action: "getUsage"}
        },
        '/:id/risklog': {
            get: {controller: WorkspaceController, action: "getActiveRiskLog"}
        },
        '/:id/purchase-credit': {
            post: {controller: WorkspaceController, action: "purchaseCredit"}
        },
        '/:id/modify-addons': {
            post: {controller: WorkspaceController, action: "modifyAddOns"}
        },
        '/:id/plans': {
            get: {controller: WorkspaceController, action: "getPlans"}
        },
        '/:id/checkout-session': {
            get: {controller: WorkspaceController, action: "getCheckoutSession"}
        },
        '/:id/customer-portal': {
            get: {controller: WorkspaceController, action: "getCustomerPortal"}
        },
        '/:id/future-subscription': {
            delete: {controller: WorkspaceController, action: "cancelFutureSubscription"}
        },
        '/:id/subscription': {
            delete: {controller: WorkspaceController, action: "cancelSubscription"}
        },
        '/:id/settings': {
            get: {controller: WorkspaceController, action: "getWorkspaceMemberSettings"}
        },
        '/:id/uploads': {
            post: {controller: WorkspaceController, action: "uploadFile"}
        },
        '/:id/templates': {
            get: {controller: WorkspaceController, action: "getInstalledTemplates"}
        },
        '/:id/notifications': {
            get: {controller: WorkspaceController, action: "getNotifications"},
            patch: {controller: WorkspaceController, action: "updateNotification"},
        },
        '/:id/notifications/create': {
            post: {controller: WorkspaceController, action: "createNotification"},
        },
        '/:id/notifications/stats': {
            get: {controller: WorkspaceController, action: "getNotificationStats"},
        },
        '/:id/notes': {
            get: {controller: WorkspaceController, action: "getNotes"},
            post: {controller: WorkspaceController, action: "createNote"},
            patch: {controller: WorkspaceController, action: "updateNote"},
            delete: {controller: WorkspaceController, action: "deleteNote"},
        },
        '/:id/reminders': {
            get: {controller: WorkspaceController, action: "getReminders"},
            post: {controller: WorkspaceController, action: "createReminder"},
            patch: {controller: WorkspaceController, action: "updateReminder"},
            delete: {controller: WorkspaceController, action: "deleteReminder"},
        },
        '/:id/reminders/:reminderId/resolve': {
            post: {controller: WorkspaceController, action: "resolveReminder"},
        },
        '/:id/document-history': {
            get: {controller: WorkspaceController, action: "getDocumentHistory"},
        },
        '/:id/secrets': {
            get: {controller: WorkspaceController, action: "getSecrets"},
            post: {controller: WorkspaceController, action: "createSecret"},
            patch: {controller: WorkspaceController, action: "updateSecret"},
            delete: {controller: WorkspaceController, action: "deleteSecret"},
        },
        "/:workspaceId/search": {
            get: {controller: WorkspaceController, action: "searchWorkspaces"}
        },
        "/:workspaceId/integrations/:integration/connections": {
            get: {controller: WorkspaceController, action: "getIntegrationConnections"},
            post: {controller: WorkspaceController, action: "saveIntegrationConnection"},
            delete: {controller: WorkspaceController, action: "deleteIntegrationConnection"},
        },
        "/:workspaceId/integrations/:integration/oauth2/redirect": {
            post: {controller: WorkspaceController, action: "integrationOAuth2Redirect"},
        },
        "/:workspaceId/integrations/:integration/options": {
            post: {controller: WorkspaceController, action: "getIntegrationPropsOptions"},
        },
        "/:workspaceId/integrations/:integration/action": {
            post: {controller: WorkspaceController, action: "executeIntegrationAction"},
        },
        "/:workspaceId/integrations/:integration/trigger": {
            post: {controller: WorkspaceController, action: "executeIntegrationTrigger"},
        },
    },

};
