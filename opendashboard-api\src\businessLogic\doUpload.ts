import * as aws from "aws-sdk";
import config from "../config";
import imageSize from "image-size";
import * as formidable from "formidable-serverless";
import {Request} from "express";
import * as fs from "fs";
import {BadRequestError, RequiredParameterError, ServerProcessingError} from "../errors/AppError";


export enum FileType {
    Image = "image",
    File = "file"
}

const spacesEndpoint = new aws.Endpoint(config.DO_SPACE.endpoint);
const maxUploadSize = 1024 * 1024 * 50; // 50Mb

aws.config.update({
    accessKeyId: config.DO_SPACE.access_key,
    secretAccessKey: config.DO_SPACE.access_secret,
    
});

const s3 = new aws.S3({
    endpoint: spacesEndpoint,
    accessKeyId: config.DO_SPACE.access_key,
    secretAccessKey: config.DO_SPACE.access_secret,
});

export const validExt = [
    "jpg", "jpeg", "png", "gif", "pdf", "xlsx", "xls", "docx", "pptx", "ppt", "csv",
    "doc", "txt", "pps", "ppsx", "xltx", "xltm", "rtf", "odt"
];

const isExtensionValid = (extension: string) => {
    return validExt.includes(extension?.toLowerCase());
};

export function getExtension(filename: string) {
    return filename.split(".").pop();
}

let unlinkUpload = true;

export const UnlinkUpload = (value: boolean) => {
    unlinkUpload = value
}

export interface UploadData {
    name: string;
    key: string;
    location: string;
    size: number;
    sizeReadable: string;
    mime: string;
    type: string;
    width: number;
    height: number;
}

const sizeOf = function (bytes: number) {
    if (bytes == 0) {
        return "0.00 B";
    }
    const e = Math.floor(Math.log(bytes) / Math.log(1024));
    return (bytes / Math.pow(1024, e)).toFixed(2) + " " + " KMGTP".charAt(e) + "b";
};

const imageDimension = (path) => {
    return imageSize(path);
};

export const uploadToS3 = (
    fileContent: Buffer,
    filePath: string
): Promise<aws.S3.ManagedUpload.SendData> => {
    const params: aws.S3.PutObjectRequest = {
        Bucket: config.DO_SPACE.bucket,
        ACL: "public-read",
        Key: filePath,
        Body: fileContent,
    };

    const options: aws.S3.ManagedUpload.ManagedUploadOptions = {
        partSize: 10 * 1024 * 1024,
        queueSize: 10,
    };

    return s3.upload(params, options).promise();
};

export const uploadBufferToS3 = (
    params: {
        fileContent: Buffer,
        filePath: string,
        imageDimen: { width: number, height: number, type: string },
        file: { name: string, type: string, size: number }
    }
): Promise<UploadData> => {
    const {fileContent, filePath, imageDimen, file} = params
    return new Promise<UploadData>((resolve, reject) => {
        uploadToS3(fileContent, filePath)
            .then((data) => {

                const uploadData: UploadData = {
                    name: file.name,
                    height: imageDimen.height,
                    width: imageDimen.width,
                    mime: file.type,
                    key: data.Key,
                    location: data.Location,
                    size: file.size,
                    sizeReadable: sizeOf(file.size),
                    type: file.type,
                };

                resolve(uploadData);
            })
            .catch((uploadErr) => {
                reject(uploadErr);
            });
    })
}

export const DOSpaceUpload = (
    file: formidable.File,
    fileName: string,
    rootPath: string,
    type: FileType
): Promise<UploadData> => {
    return new Promise<UploadData>((resolve, reject) => {
        const ext = getExtension(file.name);
        if (!isExtensionValid(ext)) {
            reject(new BadRequestError(`Invalid file extension '${ext}'`));
            return;
        }
        let imageDimen;

        const fileTypeFormat = file.mimeType || file.type
        const fileFormat = fileTypeFormat.split("/")[0]

        try {
            switch (type) {
                case FileType.Image:
                    imageDimen = imageDimension(file.path);
                    break;
                case FileType.File:
                    if (fileFormat == "image") {
                        imageDimen = imageDimension(file.path);
                    } else {
                        // setting the width and height to 0 for files
                        imageDimen = {height: 0, width: 0, type: 'file'}
                    }
                    break
                default:
                    break;
            }
        } catch (e) {
            reject(new BadRequestError(`Failed to read image, please check the image and try again`));
            return;
        }

        if (file.size > maxUploadSize) {
            reject(
                new BadRequestError(`File too large, please make sure your file is below ${sizeOf(maxUploadSize)}`)
            );
            return;
        }

        // const date = new Date();
        // fileName = fileName
        // 	? fileName
        // 	: `${date.getTime() / 1000}-${sha1(file.name)}`.toString();
        let filePath;
        rootPath = rootPath.replace(/^[\\/]+|[\\/]+$/g, "").trim();
        if (!rootPath) {
            reject(new ServerProcessingError("Root Path should not be a slash"));
            return;
        }
        filePath = `${rootPath}/${fileName}.${ext}`;

        const fileContent = fs.readFileSync(file.path);

        uploadToS3(fileContent, filePath)
            .then((data) => {

                if (unlinkUpload) {
                    fs.unlinkSync(file.path);
                }

                const uploadData: UploadData = {
                    // name: file.name.replace(/-/g, " ").replace(`.${ext}`, ""),
                    name: file.name,
                    height: imageDimen.height,
                    width: imageDimen.width,
                    mime: file.type,
                    key: data.Key,
                    location: data.Location,
                    size: file.size,
                    sizeReadable: sizeOf(file.size),
                    type: file.type,
                };

                resolve(uploadData);
            })
            .catch((uploadErr) => {
                reject(uploadErr);
            });
    });
};

export const ProcessFormToSpaceUpload = (
    request: Request,
    rootPath: string,
    fileName: string,
    fileType: FileType
): Promise<UploadData> => {
    return new Promise<UploadData>((resolve, reject) => {
        const form = new formidable.IncomingForm();

        form.parse(request, async (err, fields, files) => {
            if (err) {
                reject(new ServerProcessingError(err))
            }
            if (!files.file) {
                reject(new RequiredParameterError('file'))
            }

            const file = files.file;

            DOSpaceUpload(file, fileName, rootPath, fileType)
                .then((uploadData) => {
                    resolve(uploadData);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    });
};
