import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from 'typeorm';

@Entity()
@Index(['campaignId', 'workflowId', 'emailId', 'linkId', 'eventAt'], {unique: true})
export class CampaignAnalytic {

    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({type: 'varchar', nullable: true})
    campaignId: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    workflowId: string;

    @Index()
    @Column({type: 'int', nullable: true})
    emailId: number;

    @Index()
    @Column({type: 'int', nullable: true})
    linkId: number;

    @Column({type: 'int', default: 0})
    openCount: number;

    @Column({type: 'int', default: 0})
    clickCount: number;

    @Index()
    @Column({type: 'datetime', nullable: false})
    eventAt: Date;

    @Index()
    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @Index()
    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

}


