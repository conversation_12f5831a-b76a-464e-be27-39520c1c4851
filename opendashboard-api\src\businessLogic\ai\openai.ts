import OpenAI from "openai";
import {RequiredParameterError} from "../../errors/AppError";
import {ChatCompletionMessageParam} from "openai/resources/chat/completions";
import config from "../../config";
import {AIModelPricing} from "./ai";
import ChatModel = OpenAI.ChatModel;

const openai = new OpenAI({apiKey: config.OPENAI.api_key});

// export const GPT3_5_Turbo:AIModelPricing = Object.freeze({
//     modelName: 'gpt-3.5-turbo-0125',
//     costInCentsPerInputToken: 0.00005,
//     costInCentsPerOutputToken: 0.00015
// })

export const GPT4_Turbo: AIModelPricing = Object.freeze({
    modelName: 'gpt-4-turbo',
    costInCentsPerInputToken: 0.003,
    costInCentsPerOutputToken: 0.006
})

export const GPT4_O_Mini: AIModelPricing = Object.freeze({
    modelName: 'gpt-4o-mini',
    costInCentsPerInputToken: 0.000015,
    costInCentsPerOutputToken: 0.00006
})


export const generateMessage = async (prompt: string, options: { model?: ChatModel, system?: string, max_tokens?: number, temperature?: number }) => {
    if (!prompt || !prompt.trim()) throw new RequiredParameterError("prompt")
    // const model = options.model || 'gpt-4-turbo'
    const model = options.model || GPT4_O_Mini.modelName
    const system = options.system || ''
    const max_tokens = options.max_tokens || 1024
    const temperature = options.temperature || 0.7

    const messages: ChatCompletionMessageParam[] = []

    if (system) messages.push({
        role: 'system',
        content: system
    })
    messages.push({
        role: 'system',
        content: prompt
    })

    return openai.chat.completions.create({
        model,
        max_tokens,
        temperature,
        messages
    });
}
