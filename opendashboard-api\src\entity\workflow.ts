import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {IWorkflowDefinition, WorkflowMeta, WorkflowStatus, WorkflowTriggerType} from "opendb-app-db-utils/lib/typings/workflow";

@Entity()
export class Workflow {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Index()
    @Column({type: "varchar"})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string;

    @Column({type: 'varchar', nullable: true})
    description: string;

    @Index()
    @Column({type: 'int', default: 0})
    nodeCount: number

    @Column({type: 'json', nullable: true})
    nodesList: string[]

    @Index()
    @Column({type: "varchar", nullable: true})
    triggerType: string

    @Index()
    @Column({type: "varchar", nullable: true})
    triggerObjectId: string

    @Column({type: 'json', nullable: true})
    definition: IWorkflowDefinition

    @Column({type: 'json', nullable: true})
    draftDefinition: IWorkflowDefinition

    @Column({type: 'varchar', default: WorkflowStatus.Draft})
    status: WorkflowStatus;

    @Column({type: 'json', nullable: true})
    meta: WorkflowMeta

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    updatedById: string

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateReleaseId: number

    @Index()
    @Column({type: 'int', default: 0})
    runsCount: number

    @Index()
    @Column({type: 'timestamp', nullable: true})
    nextRunAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    publishedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    lastExecutedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    errorRateWarningAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    lastResultAt: Date

    @Index()
    @Column({type: 'int', default: 0})
    errorCount: number

    @Column({type: 'varchar', nullable: true})
    error: string

    @Index()
    @Column({type: 'timestamp', nullable: true})
    lastErrorAt: Date

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}



