import {Routes} from "../../routes";
import {AccountController} from "./controller"
import {verifyJWT} from "../../middleware/verifyJWT";


export const accountsRoutes: Routes = {
    basePath: "/account",
    middleware: [verifyJWT],
    routes: {
        "/": {
            get: {
                controller: AccountController,
                action: "getProfile",
            },
            patch: {
                controller: AccountController,
                action: "updateProfile"
            }
        },
        '/photo': {
            patch: {
                controller: AccountController,
                action: "updateProfilePhoto"
            }
        },
        '/settings': {
            get: {
                controller: AccountController,
                action: "getSettings"
            },
            patch: {
                controller: AccountController,
                action: "updateSettings"
            }
        },
        '/sessions': {
            get: {
                controller: AccountController,
                action: "getSessions"
            },
            delete: {
                controller: AccountController,
                action: "revokeSession"
            }
        },
        '/ping': {
            post: {
                controller: AccountController,
                action: "pingSession"
            },
        },
        '/event': {
            post: {
                controller: Account<PERSON>ontroller,
                action: "pushEvent"
            },
        },
        '/prompt-email-verification': {
            post: {
                controller: AccountController,
                action: "promptEmailVerification"
            }
        },
        '/api-keys': {
            get: {
                controller: AccountController,
                action: "getAPIKeys"
            },
            post: {
                controller: AccountController,
                action: "createAPIKey"
            }
        },
        '/api-keys/:id': {
            patch: {
                controller: AccountController,
                action: "updateAPIKey"
            },
            delete: {
                controller: AccountController,
                action: "revokeAPIKey"
            }
        },
        '/api-keys/:id/regenerate': {
            post: {
                controller: AccountController,
                action: "regenerateAPIKey"
            },
        },
        '/push-tokens': {
            post: {
                controller: AccountController,
                action: "registerPushToken"
            },
            delete: {
                controller: AccountController,
                action: "deRegisterPushToken"
            },
        }
    }
}




