import { MigrationInterface, Query<PERSON>un<PERSON> } from "typeorm";

export class OpendashboardSiteSender1740186979584 implements MigrationInterface {
    name = 'OpendashboardSiteSender1740186979584'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_sender_email\` CHANGE \`workspaceDomainId\` \`workspaceDomainId\` VARCHAR(255) NOT NULL;`);
        await queryRunner.query(`ALTER TABLE \`workspace_domain\` CHANGE  \`id\`  \`id\` varchar(36) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_domain\` CHANGE \`id\` \`id\` int NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`workspace_sender_email\` CHAN<PERSON> \`workspaceDomainId\` \`workspaceDomainId\` INT NOT NULL;`);
    }

}
