import { Entity, PrimaryGeneratedColumn, Index, Column, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from "typeorm"

export enum SecretType {
	Variable = "variable",
	Secret = "secret"
}


@Entity()
@Index(["workspaceId", "type", "name"], { unique: true })
export class WorkspaceSecret {

	@PrimaryGeneratedColumn('increment')
	id: number

	@Index()
	@Column({ type: 'varchar', nullable: false })
	workspaceId: string

	@Index()
	@Column({ type: 'varchar', nullable: false, default: SecretType.Secret })
	type: SecretType

	@Index()
	@Column({ type: 'varchar', nullable: true })
	name: string

	@Column({ type: 'varchar', nullable: true, select: false })
	value: string

	@Column({ type: 'varchar', nullable: false })
	createdById: string

	@CreateDateColumn({ type: 'timestamp', default: 0 })
	createdAt: Date

	@UpdateDateColumn({ type: 'timestamp', default: 0 })
	updatedAt: Date

	@DeleteDateColumn({ type: 'timestamp', nullable: true })
	deletedAt: Date

}


