import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";
import {Workflow} from "../entity/workflow";

export interface  CreateNewWorkflowData extends Partial<Workflow> {
    name?: string
    workspaceId: string
    createdById: string
    updatedById: string
}
export class WorkflowService extends BaseService<Workflow> {

    initRepository = (): Repository<Workflow> => {
        return getRepository(Workflow);
    }


}
