import {NextFunction, Request, Response} from "express"
import {ApiMessage, ApiResponseBody, ApiResponseStatus, GenericApiResponseBody} from "../../../controller/interface";
import {AuthInfo, getAuthInfo} from "../../../businessLogic/authInfo";
import {GetMyWorkspace, GetMyWorkspaces, GetWorkspaceMembers, MyWorkspace, MyWorkspaceMember} from "../../../businessLogic/workspace";
import { AddRecords, DeleteRecords, GetMyDatabase, GetMyDatabases, GetProcessedRecords, GetRecords, UpdateRecordValues } from "../../../businessLogic/database";
import {Database} from "../../../entity/Database";
import {Workspace} from "../../../entity/Workspace";
import {Record} from "../../../entity/Record";
import {UploadFile} from "../../../businessLogic/upload";

export class WorkspaceController {

    async getWorkspaces(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const spaces = await GetMyWorkspaces(authInfo.userId)

        const workspaces = spaces.map(w => w.workspace)
        const responseData: ApiResponseBody<{ workspaces: Workspace[] }> = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workspaces
            },
        }
        return response.json(
            responseData
        )
    }

    async getWorkspace(request: Request, response: Response, next: NextFunction) {

        const authInfo: AuthInfo = getAuthInfo(request)
        const workspace = await GetMyWorkspace(authInfo.userId, request.params.id)

        const responseData: ApiResponseBody<{ workspace: MyWorkspace }> = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async uploadNewWorkspaceFile(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {upload} = await UploadFile(authInfo.userId, request.params.id, request, true)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                upload
            },
        }
        return response.json(
            responseData
        )
    }


    async getMembers(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const members = await GetWorkspaceMembers(authInfo.userId, request.params.id)

        const responseData: ApiResponseBody<{ members: MyWorkspaceMember[] }> = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                members
            },
        }
        return response.json(
            responseData
        )
    }

    async getDatabases(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {databases: dbs} = await GetMyDatabases(authInfo.userId, request.params.id)

        const databases = dbs.map(d => d.database)

        const responseData: ApiResponseBody<{ databases: Database[] }> = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                databases
            },
        }
        return response.json(
            responseData
        )
    }

    async getDatabase(request: Request, response: Response, next: NextFunction) {

        const authInfo: AuthInfo = getAuthInfo(request)
        const {database: db} = await GetMyDatabase(authInfo.userId, request.params.id, request.params.databaseId)
        const database = db.database
        const responseData: ApiResponseBody<{ database: Database }> = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                database
            },
        }
        return response.json(
            responseData
        )
    }

    async getRecords(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {records} = await GetRecords(authInfo.userId, request.params.id, request.params.databaseId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                records
            },
        }
        return response.json(
            responseData
        )
    }
    async getProcessedRecords(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const { processed, records } = await GetProcessedRecords(authInfo.userId, request.params.id, request.params.databaseId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                records,
                processedRecord: processed,
            },
        }
        return response.json(
            responseData
        )
    }

    async addRecords(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {records} = await AddRecords(authInfo.userId, request.params.id, request.params.databaseId, request.body)

        const responseData: ApiResponseBody<{ records: Record[] }> = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                records
            },
        }
        return response.json(
            responseData
        )
    }

    async updateRecords(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await UpdateRecordValues(authInfo.userId, request.params.id, request.params.databaseId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deleteRecords(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteRecords(authInfo.userId, request.params.id, request.params.databaseId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    // async matchRecords(request: Request, response: Response, next: NextFunction) {
    //     const authInfo: AuthInfo = getAuthInfo(request)
    //
    //     const {matchMap} = await MatchAndCreateRecords(authInfo.userId, request.params.id, request.params.databaseId, request.body, true)
    //
    //     const responseData: GenericApiResponseBody = {
    //         status: ApiResponseStatus.Ok,
    //         message: ApiMessage.ActionSuccessful,
    //         data: {matchMap},
    //     }
    //     return response.json(
    //         responseData
    //     )
    // }
}