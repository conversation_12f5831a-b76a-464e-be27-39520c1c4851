import {NextFunction, Request, Response} from "express"
import {Api<PERSON>essage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {
    AcceptWorkspaceInvitation,
    AddWorkspaceSecret,
    AddWorkspaceSenderEmail,
    cancelCurrentSubscription,
    CancelFutureSubscription,
    CancelWorkspaceInvitation,
    CompleteDomainVerification,
    CompleteWorkspaceSetup,
    CreateWorkspace,
    CreateWorkspaceViaOnboarding,
    CreateWorkspaceNotification,
    DeclineWorkspaceInvitation,
    DeleteSenderDomain,
    DeleteSenderEmail,
    DeleteWorkspace,
    DeleteWorkspaceIntegrationConnections,
    DeleteWorkspaceMember,
    DeleteWorkspaceSecret,
    ExecuteWorkspaceIntegrationAction,
    GetAllWorkspaceInvitations,
    GetAllWorkspaceSecrets,
    GetCheckoutSession,
    GetCheckoutSessionData,
    GetCustomerPortal,
    GetMyWorkspace,
    GetMyWorkspaces,
    GetNotifications,
    GetNotificationStats,
    GetSenderEmails,
    getWorkspaceActiveRiskLog,
    GetWorkspaceByDomain,
    GetWorkspaceIntegrationConnections,
    GetWorkspaceIntegrationOptions,
    GetWorkspaceInvitation,
    GetWorkspaceMembers,
    GetWorkspaceMemberSettings,
    GetWorkspaceUsage,
    InviteWorkspaceMember,
    MakeWorkspaceMemberOwner,
    ModifyAddOns,
    PurchaseWorkspaceCredit,
    ResendSenderEmailVerification,
    ResendWorkspaceInvitation,
    SaveWorkspaceIntegrationConnection,
    SearchMyWorkspaces,
    StartWorkspaceIntegrationOAuth2Redirect,
    SwitchToWorkspace,
    ToggleSupportAccess,
    UpdateNotification,
    UpdateWorkspace,
    UpdateWorkspaceLogo,
    UpdateWorkspaceMember,
    UpdateWorkspaceSecret
} from "../../businessLogic/workspace";
import {ErrorMessage, NotfoundError} from "../../errors/AppError";
import {PayPerUsePricing, WorkspaceAddOns, WorkspacePlans} from "../../businessLogic/subscription";
import {UploadFile} from "../../businessLogic/upload";
import {suggestDbImportMapping} from "../../businessLogic/database";
import {GetProfile} from "../../businessLogic/account";
import {GetWorkspaceInstalledTemplates} from "../../businessLogic/templates";
import {CreateNote, DeleteNote, GetDocumentHistory, GetDocumentHistoryParams, GetNotes, UpdateNote} from "../../businessLogic/document";
import {CreateReminder, DeleteReminder, GetReminders, MarkReminderResolved, UpdateReminder} from "../../businessLogic/reminder";
import {getUSDNGNRate} from "../../businessLogic/currencyConverter";
import {getWorkspaceAffiliateCalculateDiscountAndEarning} from "../../businessLogic/affiliate";

export class WorkspaceController {

    async searchWorkspaces(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request);
        const {workspaceId} = request.params;
        const {query, page, pageSize} = request.query as {
            query?: string;
            page?: string;
            pageSize?: string;
        };


        const pageNum = page ? parseInt(page, 10) : 1;
        const pageSizeNum = pageSize ? parseInt(pageSize, 10) : 25;

        const results = await SearchMyWorkspaces(authInfo.userId, workspaceId, query || "", pageNum, pageSizeNum);

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {results},
        };

        return response.json(
            responseData
        )
    }

    async getWorkspaces(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workspaces = await GetMyWorkspaces(authInfo.userId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                workspaces
            },
        }
        return response.json(
            responseData
        )
    }

    async createWorkspace(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const user = await GetProfile(authInfo.userId)
        const workspace = await CreateWorkspace(user, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async createWorkspaceViaOnboarding(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const user = await GetProfile(authInfo.userId)
        const res = await CreateWorkspaceViaOnboarding(user, request.body)

        const workspace = res.myWorkspace
        const page = res.gettingStartedPage

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                workspace, page
            },
        }
        return response.json(
            responseData
        )
    }

    async getWorkspaceByDomain(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workspace = await GetWorkspaceByDomain(request.params.domain)
        if (!workspace) throw new NotfoundError(ErrorMessage.EntityNotFound)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async getWorkspace(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workspace = await GetMyWorkspace(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async updateWorkspace(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workspace = await UpdateWorkspace(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async updateWorkspaceLogo(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workspace = await UpdateWorkspaceLogo(authInfo.userId, request.params.id, request)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async completeSetup(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await CompleteWorkspaceSetup(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async switchToWorkspace(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await SwitchToWorkspace(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deleteWorkspace(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteWorkspace(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async toggleSupportAccess(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await ToggleSupportAccess(authInfo.userId, request.params.id, request.body.enable)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getMembers(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const members = await GetWorkspaceMembers(authInfo.userId, request.params.id)
        const invitations = await GetAllWorkspaceInvitations(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                members, invitations
            },
        }
        return response.json(
            responseData
        )
    }

    async inviteMember(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {myInvitation} = await InviteWorkspaceMember(authInfo.userId, request.params.id, request.body)
        const {invitation} = myInvitation

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                invitation
            },
        }
        return response.json(
            responseData
        )
    }

    async updateMember(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await UpdateWorkspaceMember(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deleteMember(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteWorkspaceMember(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async makeOwner(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await MakeWorkspaceMemberOwner(authInfo.userId, request.params.id, {userId: request.params.userId})

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async resendInvitation(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await ResendWorkspaceInvitation(authInfo.userId, request.params.id, Number(request.params.inviteId))

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deleteInvitation(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await CancelWorkspaceInvitation(authInfo.userId, request.params.id, Number(request.params.inviteId))

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getInvitation(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const invitation = await GetWorkspaceInvitation(authInfo.userId, request.params.id, String(request.query.token))
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                invitation
            },
        }
        return response.json(
            responseData
        )
    }

    async acceptInvitation(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workspace = await AcceptWorkspaceInvitation(authInfo.userId, request.params.id, String(request.query.token))

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                workspace
            },
        }
        return response.json(
            responseData
        )
    }

    async declineInvitation(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeclineWorkspaceInvitation(authInfo.userId, request.params.id, String(request.query.token))


        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getSenders(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {senders, domains} = await GetSenderEmails(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                senders, domains
            },
        }
        return response.json(
            responseData
        )
    }

    async addSender(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {sender, domain} = await AddWorkspaceSenderEmail(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                sender, domain
            },
        }
        return response.json(
            responseData
        )
    }

    async verifyDomain(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await CompleteDomainVerification(authInfo.userId, request.params.id, request.params.domainId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deleteDomain(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeleteSenderDomain(authInfo.userId, request.params.id, request.params.domainId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deleteSender(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeleteSenderEmail(authInfo.userId, request.params.id, Number(request.params.senderId))
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async promptSenderVerification(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await ResendSenderEmailVerification(authInfo.userId, request.params.id, Number(request.params.senderId))
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getUsage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {stats, billingCycle, availableCreditInCents, subscription} = await GetWorkspaceUsage(authInfo.userId, request.params.id)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                stats, billingCycle, availableCreditInCents, subscription
            },
        }
        return response.json(
            responseData
        )
    }

    async getActiveRiskLog(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const risklog = await getWorkspaceActiveRiskLog(authInfo.userId, request.params.id)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                risklog
            },
        }
        return response.json(
            responseData
        )
    }

    async purchaseCredit(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {checkoutUrl} = await PurchaseWorkspaceCredit(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {checkoutUrl},
        }
        return response.json(
            responseData
        )
    }

    async modifyAddOns(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {subscription, billingCycle} = await ModifyAddOns(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                billingCycle, subscription
            },
        }
        return response.json(
            responseData
        )
    }

    async getPlans(request: Request, response: Response, next: NextFunction) {
        const plans = WorkspacePlans
        const addOns = WorkspaceAddOns
        const payPerUsePricing = PayPerUsePricing
        const USDNGNRate = (await getUSDNGNRate()).rate

        const workspaceId = request.params.id
        const {affiliate, discountMonthsRemaining} = await getWorkspaceAffiliateCalculateDiscountAndEarning(workspaceId, 0)


        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                plans,
                addOns,
                payPerUsePricing,
                USDNGNRate,
                discountPercentage: affiliate?.referralDiscountPercent || 0,
                discountMonthsRemaining
            },
        }
        return response.json(
            responseData
        )
    }

    async getCheckoutSession(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {session} = await GetCheckoutSession(authInfo.userId, request.params.id, request.query as any as GetCheckoutSessionData)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                url: session.url
            },
        }
        return response.json(
            responseData
        )
    }

    async getCustomerPortal(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {portal} = await GetCustomerPortal(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                url: portal.url
            },
        }
        return response.json(
            responseData
        )
    }

    async cancelFutureSubscription(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await CancelFutureSubscription(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async cancelSubscription(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await cancelCurrentSubscription(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getWorkspaceMemberSettings(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {settings} = await GetWorkspaceMemberSettings(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                settings
            },
        }
        return response.json(
            responseData
        )
    }

    async uploadFile(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {upload} = await UploadFile(authInfo.userId, request.params.id, request)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                upload
            },
        }
        return response.json(
            responseData
        )
    }

    async suggestImportMap(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)


        const aiContentGenerationLog = await suggestDbImportMapping(authInfo.userId, request.params.id, request.body)
        const jsonStr = aiContentGenerationLog.contentGenerated
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                jsonStr
            },
        }
        return response.json(
            responseData
        )
    }

    async getInstalledTemplates(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const installedTemplates = await GetWorkspaceInstalledTemplates(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                installedTemplates
            },
        }
        return response.json(
            responseData
        )
    }

    async getNotifications(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {notifications} = await GetNotifications(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                notifications
            },
        }
        return response.json(
            responseData
        )
    }

    async getNotificationStats(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {stats} = await GetNotificationStats(authInfo.userId, request.params.id)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                stats
            },
        }
        return response.json(
            responseData
        )
    }

    async updateNotification(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await UpdateNotification(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                // notifications
            },
        }
        return response.json(
            responseData
        )
    }

    async createNotification(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const { userId, title, message, link } = request.body

        const { notification } = await CreateWorkspaceNotification(authInfo.userId, request.params.id, {
            userId: userId || authInfo.userId,
            title,
            message,
            link
        })

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                notification
            },
        }
        return response.json(
            responseData
        )
    }

    async getNotes(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {notes} = await GetNotes(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                notes
            },
        }
        return response.json(
            responseData
        )
    }

    async createNote(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {note} = await CreateNote(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                note
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteNote(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeleteNote(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async updateNote(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {note} = await UpdateNote(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                note
            },
        }
        return response.json(
            responseData
        )
    }

    async getReminders(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {reminders} = await GetReminders(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                reminders
            },
        }
        return response.json(
            responseData
        )
    }

    async createReminder(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {reminder} = await CreateReminder(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                reminder
            },
        }
        return response.json(
            responseData
        )
    }

    async updateReminder(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {reminder} = await UpdateReminder(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                reminder
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteReminder(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeleteReminder(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async resolveReminder(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await MarkReminderResolved(authInfo.userId, request.params.id, request.params.reminderId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getDocumentHistory(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {histories} = await GetDocumentHistory(authInfo.userId, request.params.id, request.query as unknown as GetDocumentHistoryParams)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                histories
            },
        }
        return response.json(
            responseData
        )
    }

    async getSecrets(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const secrets = await GetAllWorkspaceSecrets(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                secrets
            },
        }
        return response.json(
            responseData
        )
    }

    async createSecret(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const secret = await AddWorkspaceSecret(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                secret
            },
        }
        return response.json(
            responseData
        )
    }

    async updateSecret(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const secret = await UpdateWorkspaceSecret(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                secret
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteSecret(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeleteWorkspaceSecret(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(responseData)
    }

    async getIntegrationConnections(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const connections = await GetWorkspaceIntegrationConnections(authInfo.userId, request.params.workspaceId, request.params.integration)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                connections
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteIntegrationConnection(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteWorkspaceIntegrationConnections(authInfo.userId, request.params.workspaceId, request.params.integration, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async saveIntegrationConnection(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const connection = await SaveWorkspaceIntegrationConnection(authInfo.userId, request.params.workspaceId, request.params.integration, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                connection
            },
        }
        return response.json(
            responseData
        )
    }

    async integrationOAuth2Redirect(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {url} = await StartWorkspaceIntegrationOAuth2Redirect(authInfo.userId, request.params.workspaceId, request.params.integration, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                url
            },
        }
        return response.json(
            responseData
        )
    }

    async getIntegrationPropsOptions(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {options} = await GetWorkspaceIntegrationOptions(authInfo.userId, request.params.workspaceId, request.params.integration, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                options
            },
        }
        return response.json(responseData)
    }

    async executeIntegrationAction(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {result} = await ExecuteWorkspaceIntegrationAction(authInfo.userId, request.params.workspaceId, request.params.integration, {...request.body, type: 'action'}, null)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {result}
        }
        return response.json(responseData)
    }

    async executeIntegrationTrigger(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {result} = await ExecuteWorkspaceIntegrationAction(authInfo.userId, request.params.workspaceId, request.params.integration, {...request.body, type: 'trigger'}, null)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {result}
        }
        return response.json(responseData)
    }

}