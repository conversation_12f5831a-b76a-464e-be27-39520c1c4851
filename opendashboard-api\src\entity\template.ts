import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

@Entity()
@Index(["creatorId", "slug"], {unique: true})
export class Template {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    creatorId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Column({type: 'varchar', nullable: true})
    slug: string

    @Index()
    @Column({type: 'int', nullable: true})
    marketplaceReleaseId: number

    @Index()
    @Column({type: 'int', nullable: true})
    marketplaceListingId: number

    @Index()
    @Column({type: 'bool', default: false})
    isListedInMarketplace: boolean

    @Column({type: 'timestamp', nullable: true})
    listedAt: Date

    @Column({type: 'timestamp', nullable: true})
    listingUpdatedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}