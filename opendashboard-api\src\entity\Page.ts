import {Column, CreateDateColumn, DeleteDateColumn, Index, UpdateDateColumn} from "typeorm";
import {PrimaryGeneratedColumn} from "typeorm/decorator/columns/PrimaryGeneratedColumn";
import {Entity} from "typeorm/decorator/entity/Entity";
import {Emoji, Icon} from "opendb-app-db-utils/lib/typings/common";
import {AccessLevel, Visibility} from "./common";

export type PickedIcon = Icon | Emoji;

@Entity()
@Index(["workspaceId", "slug"], {unique: true})
export class Page {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Column({type: 'json', nullable: true})
    icon: PickedIcon

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    databaseId: string

    @Column({type: 'varchar', nullable: true})
    slug: string

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateReleaseId: number

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateInstallId: number

    @Index()
    @Column({type: 'varchar', nullable: false, default: Visibility.Open})
    visibility: Visibility

    @Column({type: 'varchar', nullable: true, default: AccessLevel.Full})
    accessLevel: AccessLevel

    @Column({type: 'json', nullable: true})
    viewsOrder: string[]

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    ownerId: string

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    createdById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}