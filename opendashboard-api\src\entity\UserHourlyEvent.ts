import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"

export enum EventType {
    View = 1
}

@Entity()
@Index(['userId', "workspaceId", "pageId", "databaseId", "viewId", "event", "anchorAt"], {unique: true})
export class UserHourlyEvent {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    userId: string

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    pageId: string

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    viewId: string

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    databaseId: string

    @Index()
    @Column({type: 'int', nullable: true})
    event: EventType

    @Column({type: 'int', default: 1})
    eventCount: number

    @Index()
    @Column({type: 'timestamp', nullable: true})
    anchorAt: Date

    @Column({type: 'timestamp', nullable: true})
    eventAt: Date

    @CreateDateColumn({type: 'timestamp', nullable: true})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', nullable: true})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}


