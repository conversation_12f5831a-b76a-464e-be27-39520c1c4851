import config from "../config";
import {Workspace} from "../entity/Workspace";

import {Stripe} from 'stripe';
import {BadRequestError, ErrorMessage} from "../errors/AppError";
import {Creator} from "../entity/creator";

export const stripe = new Stripe(config.STRIPE_KEY.secret_key, {
    // apiVersion: '2023-10-16', // Optional
});

export const createCustomer = async (workspace: Workspace) => {
    const customer: Stripe.Response<Stripe.Customer> = await stripe.customers.create({
        name: workspace.name,
        metadata: {workspaceId: workspace.id},
    });
    return {
        customer
    }
}

export const createCustomerPortal = async (body: {
    customer: string,
    return_url: string
}) => {
    const portal = await stripe.billingPortal.sessions.create(body)
    return {
        portal
    }
}

export const cancelSubscription = async (id: string) => {
    const subscription = await stripe.subscriptions.cancel(
        id
    );
    return {subscription}
}

export const stopSubscriptionAutoRenew = async (id: string) => {
    const subscription = await stripe.subscriptions.update(
        id, {cancel_at_period_end: true}
    );
    return {subscription}
}

export const createSubscriptionCheckoutSession = async (body: {
    customer: string,
    return_url: string,
    cancel_url: string,
    priceId: string,
    startsAt?: Date // for starting in the future
    couponId?: string
}) => {
    // billing_cycle_anchor
    const params: Stripe.Checkout.SessionCreateParams = {
        billing_address_collection: 'auto',
        line_items: [
            {
                price: body.priceId,
                quantity: 1
            }
        ],
        customer: body.customer,
        mode: 'subscription',
        success_url: body.return_url,
        cancel_url: body.cancel_url,
    }
    if (body.couponId) {
        params.discounts = [{coupon: body.couponId}]
    }
    if (body.startsAt) {
        params.custom_text = {
            submit: {message: "This subscription will start when your current billing cycle ends"}
        };
        params.subscription_data = {
            billing_cycle_anchor: body.startsAt.getTime() / 1000,
            proration_behavior: "none"
            // trial_end: body.startsAt.getTime() / 1000
        }
    }
    const session = await stripe.checkout.sessions.create(params)
    return {
        session
    }
}

interface LineItem {
    name: string
    unit_amount_in_cents: number
    quantity: number
}


export interface StripeCheckoutSessionMetadata extends Stripe.MetadataParam {
    type: 'template_purchase'
}

export const createPaymentCheckoutSession = async (body: {
    // connectedAccountId?: string,
    success_url: string,
    cancel_url: string,
    line_items: LineItem[],
    client_reference_id: string
    customer_email: string
    application_fee_percent?: number,
    metadata?: StripeCheckoutSessionMetadata
}) => {
    let total_amount = 0
    for (const i of body.line_items) {
        total_amount += i.quantity * i.unit_amount_in_cents
    }
    // let application_fee_amount = body.application_fee_percent / 100 * total_amount
    // const payment_intent_data: Stripe.Checkout.SessionCreateParams.PaymentIntentData = {}
    // if (application_fee_amount > 0) {
    //     payment_intent_data.application_fee_amount = application_fee_amount
    // }
    // if (body.connectedAccountId) {
    //     payment_intent_data.on_behalf_of = body.connectedAccountId
    //     payment_intent_data.transfer_data = {
    //         destination: body.connectedAccountId
    //     }
    // }
    const params: Stripe.Checkout.SessionCreateParams = {
        line_items: body.line_items.map(i => ({
            quantity: i.quantity,
            price_data: {
                currency: 'usd',
                unit_amount: i.unit_amount_in_cents,
                product_data: {name: i.name}
            }
        })),
        // payment_intent_data,
        mode: 'payment',
        success_url: body.success_url,
        cancel_url: body.cancel_url,
        client_reference_id: body.client_reference_id,
        customer_email: body.customer_email,
        metadata: body.metadata
    }
    const session = await stripe.checkout.sessions.create(params);
    return {
        session
    }
}

export const listCustomerPaymentMethods = async (body: {
    customer: string
}) => {
    const paymentMethods = await stripe.customers.listPaymentMethods(
        body.customer,
        {
            limit: 3,
            type: 'card'
        }
    );
    return {paymentMethods}
}

export const chargeCustomer = async (body: {
    customer: string,
    amountInCents: number,
    description: string,
    // receiptEmail: string
}) => {
    const res = await listCustomerPaymentMethods({customer: body.customer})
    if (res.paymentMethods.data.length === 0) {
        throw new BadRequestError(ErrorMessage.SubscriptionNotActive)
    }
    const paymentIntent = await stripe.paymentIntents.create({
        amount: body.amountInCents,
        currency: 'usd',
        confirm: true,
        customer: body.customer,
        description: body.description,
        off_session: true,
        payment_method: res.paymentMethods.data[0].id
        // error_on_requires_action: true,
        // confirmation_method: 'manual',
        // receipt_email: body.receiptEmail
    });
    return {
        paymentIntent
    }
}

export const chargeCustomerViaInvoice = async (body: {
    customer: string,
    amountInCents: number,
    description: string,
    // receiptEmail: string
}) => {
    const res = await listCustomerPaymentMethods({customer: body.customer})
    if (res.paymentMethods.data.length === 0) {
        throw new BadRequestError(ErrorMessage.NoPaymentMethodAvailable)
    }
    const draftInvoice = await stripe.invoices.create({
        customer: body.customer,
        collection_method: 'charge_automatically',
        description: body.description,
        currency: 'usd',
        default_payment_method: res.paymentMethods.data[0].id,
    });

    const invoiceItem = await stripe.invoiceItems.create({
        customer: body.customer,
        amount: body.amountInCents,
        currency: 'usd',
        description: body.description,
        invoice: draftInvoice.id
    });

    const invoice = await stripe.invoices.pay(draftInvoice.id);

    return {
        invoice
    }
}

export const createConnectAccount = async (creator: Creator, businessUrl: string) => {
    // console.log({businessUrl})
    const params: Stripe.AccountCreateParams = {
        controller: {
            stripe_dashboard: {
                type: "express",
            },
            fees: {
                payer: "application"
            },
            losses: {
                payments: "application"
            },
        },
        capabilities: {
            // card_payments: {
            //     requested: true
            // },
            // transfers: {
            //     requested: true
            // },
            // legacy_payments: {
            //     requested: true
            // },
            // crypto_transfers: {
            //     requested: true
            // }
        },
        metadata: {creatorId: creator.id}
    }
    if (businessUrl) {
        params.business_profile = {
            url: businessUrl
        }
    }
    const account: Stripe.Response<Stripe.Account> = await stripe.accounts.create(params);
    return {
        account
    }
}

export const createAccountLink = async (body: {
    account: string,
    return_url: string,
    refresh_url: string,
    type: "account_onboarding",
}) => {
    const accountLink = await stripe.accountLinks.create(body)
    return {
        accountLink
    }
}

export const createLoginLink = async (account: string) => {
    const loginLink = await stripe.accounts.createLoginLink(account)
    return {
        loginLink
    }
}

