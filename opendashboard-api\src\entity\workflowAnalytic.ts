import {Column, Entity, Index, PrimaryGeneratedColumn} from "typeorm";


@Entity()
export class WorkflowAnalytic {
	@PrimaryGeneratedColumn()
	id: number;

	@Index()
	@Column({type: "int", nullable: false,})
	hour: number

	@Index()
	@Column({type: "int", nullable: false,})
	day: number

	@Index()
	@Column({type: "int", nullable: false,})
	month: number

	@Index()
	@Column({type: "int", nullable: false,})
	year: number

	@Index()
	@Column({type: 'int', nullable: false, default: 0})
	inboundId: number

	@Index()
	@Column({type: 'int', nullable: false, default: 0})
	outboundId: number

	@Index()
	@Column({type: 'int', nullable: false, default: 0})
	locationId: number

	@Index()
	@Column({type: 'varchar', nullable: true})
	workflowId: string

	@Index()
	@Column({type: 'int', nullable: false, default: 0})
	instanceId: number

	@Index()
	@Column({type: 'int', nullable: false, default: 0})
	taskId: number

	@Index()
	@Column({type: 'varchar', nullable: true})
	nodeId: string

	@Column({type: 'int', nullable: false, default: 0})
	pageViews: number

	@Column({type: 'int', nullable: false, default: 0})
	clickCount: number

	@Column({type: 'int', nullable: false, default: 0})
	unsubscribeCount: number

}