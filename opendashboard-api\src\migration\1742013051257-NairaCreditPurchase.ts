import { MigrationInterface, QueryRunner } from "typeorm";

export class NairaCreditPurchase1742013051257 implements MigrationInterface {
    name = 'NairaCreditPurchase1742013051257'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`amountInLocalCurrency\` decimal(12,4) NOT NULL DEFAULT '0.0000'`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`earningsInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000'`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`currency\` varchar(255) NOT NULL DEFAULT 'usd'`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`paymentProcessorReference\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`paymentProcessor\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`status\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`meta\` json NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_b69693c3c4ded332b2deee3478\` ON \`workspace_credit\` (\`earningsInCents\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_643953f5195ead33689b3b0ff4\` ON \`workspace_credit\` (\`currency\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_8f42c51f3e08bd4054f1fa6c94\` ON \`workspace_credit\` (\`paymentProcessorReference\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_a09d6a858762263c6dfa1e46c8\` ON \`workspace_credit\` (\`paymentProcessor\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_6dcea38cff857ed38c3ed89d0c\` ON \`workspace_credit\` (\`status\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_6dcea38cff857ed38c3ed89d0c\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_a09d6a858762263c6dfa1e46c8\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_8f42c51f3e08bd4054f1fa6c94\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_643953f5195ead33689b3b0ff4\` ON \`workspace_credit\``);
        await queryRunner.query(`DROP INDEX \`IDX_b69693c3c4ded332b2deee3478\` ON \`workspace_credit\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`meta\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`paymentProcessor\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`paymentProcessorReference\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`currency\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`earningsInCents\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`amountInLocalCurrency\``);
    }

}
