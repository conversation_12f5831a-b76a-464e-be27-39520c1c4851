import { MigrationInterface, QueryRunner } from "typeorm";

export class AffiliateMigration1741725777538 implements MigrationInterface {
    name = 'AffiliateMigration1741725777538'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`creator_payout\``);
        await queryRunner.query(`CREATE TABLE \`affiliate_earning\` (\`id\` varchar(36) NOT NULL, \`affiliateId\` varchar(255) NOT NULL, \`summary\` varchar(255) NULL, \`workspaceCreditId\` varchar(255) NULL, \`subscriptionId\` varchar(255) NULL, \`earningsInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`meta\` json NULL, \`payoutId\` int NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_d9e0f1e3f2b3349c89c6a73798\` (\`affiliateId\`), INDEX \`IDX_cbf9ea3b24eca3261319fcd215\` (\`workspaceCreditId\`), INDEX \`IDX_c55d9ee1d06e978d914978db16\` (\`subscriptionId\`), INDEX \`IDX_b6f4674329cd320a189b272bee\` (\`earningsInCents\`), INDEX \`IDX_14d228587797ee2ef82c45164a\` (\`payoutId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`affiliate\` (\`id\` varchar(36) NOT NULL, \`referralCode\` varchar(255) NOT NULL, \`userId\` varchar(255) NOT NULL, \`auditLog\` json NULL, \`referralDiscountPercent\` int NOT NULL DEFAULT '10', \`referralExpiryMonths\` int NOT NULL DEFAULT '3', \`earningPercent\` int NOT NULL DEFAULT '10', \`earningExpiryMonths\` int NOT NULL DEFAULT '3', \`isApproved\` tinyint NOT NULL DEFAULT 0, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_df9ce598cb71b529a9780c82f1\` (\`referralDiscountPercent\`), INDEX \`IDX_e2e18ffc91768460f1d5b2797f\` (\`referralExpiryMonths\`), INDEX \`IDX_d32cd3b9258f6c144800be30fb\` (\`earningPercent\`), INDEX \`IDX_97bd3b90ae11421924e5421d37\` (\`earningExpiryMonths\`), UNIQUE INDEX \`IDX_2c4457396a9b859eac63f578c1\` (\`referralCode\`), UNIQUE INDEX \`IDX_7ea0af8c910211e1b7ed0406d1\` (\`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`creator_payout_method\` ADD \`affiliateId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`creator_payout_method\` ADD UNIQUE INDEX \`IDX_5e2e1401ba70e158555b8e15e7\` (\`affiliateId\`)`);
        await queryRunner.query(`ALTER TABLE \`creator_payout\` ADD \`affiliateId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace\` ADD \`affiliateId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`creator_payout\` CHANGE \`creatorId\` \`creatorId\` varchar(255) NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`creator_payout\` (\`creatorId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c9ce31d72a490e812b3dfeeba3\` ON \`creator_payout\` (\`affiliateId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_5236030818ef875bb747ce6379\` ON \`workspace\` (\`affiliateId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_18338223126edde6a141cf3888\` ON \`domain\` (\`domain\`)`);

        // Rename the old table to the new table name
        await queryRunner.query(`RENAME TABLE creator_payout TO payout`);
        await queryRunner.query(`RENAME TABLE creator_payout_method TO payout_method`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Rename the new table to the old table name
        await queryRunner.query(`RENAME TABLE payout TO creator_payout`);
        await queryRunner.query(`RENAME TABLE payout_method TO creator_payout_method`);

        await queryRunner.query(`DROP INDEX \`IDX_18338223126edde6a141cf3888\` ON \`domain\``);
        await queryRunner.query(`DROP INDEX \`IDX_5236030818ef875bb747ce6379\` ON \`workspace\``);
        await queryRunner.query(`DROP INDEX \`IDX_c9ce31d72a490e812b3dfeeba3\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`creator_payout\``);
        await queryRunner.query(`ALTER TABLE \`creator_payout\` CHANGE \`creatorId\` \`creatorId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace\` DROP COLUMN \`affiliateId\``);
        await queryRunner.query(`ALTER TABLE \`creator_payout\` DROP COLUMN \`affiliateId\``);
        await queryRunner.query(`ALTER TABLE \`creator_payout_method\` DROP INDEX \`IDX_5e2e1401ba70e158555b8e15e7\``);
        await queryRunner.query(`ALTER TABLE \`creator_payout_method\` DROP COLUMN \`affiliateId\``);
        await queryRunner.query(`DROP INDEX \`IDX_7ea0af8c910211e1b7ed0406d1\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_2c4457396a9b859eac63f578c1\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_97bd3b90ae11421924e5421d37\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_d32cd3b9258f6c144800be30fb\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_e2e18ffc91768460f1d5b2797f\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_df9ce598cb71b529a9780c82f1\` ON \`affiliate\``);
        await queryRunner.query(`DROP TABLE \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_14d228587797ee2ef82c45164a\` ON \`affiliate_earning\``);
        await queryRunner.query(`DROP INDEX \`IDX_b6f4674329cd320a189b272bee\` ON \`affiliate_earning\``);
        await queryRunner.query(`DROP INDEX \`IDX_c55d9ee1d06e978d914978db16\` ON \`affiliate_earning\``);
        await queryRunner.query(`DROP INDEX \`IDX_cbf9ea3b24eca3261319fcd215\` ON \`affiliate_earning\``);
        await queryRunner.query(`DROP INDEX \`IDX_d9e0f1e3f2b3349c89c6a73798\` ON \`affiliate_earning\``);
        await queryRunner.query(`DROP TABLE \`affiliate_earning\``);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`creator_payout\` (\`creatorId\`)`);
    }

}
