import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";
import {WorkspaceCredit} from "../entity/WorkspaceCredit";
import {Brackets, IsNull, MoreThanOrEqual} from "typeorm";
import {KeyValueStore} from "../entity/WorkspaceMemberSettings";
import {PurchaseStatus} from "../entity/templatePurchase";


interface AddWorkspaceCreditData extends Pick<WorkspaceCredit,
    'workspaceId' | 'purchasedAt' | 'costInCents' | 'amountPaidInCents' | 'creditAmountInCents' |
    'validFrom' | 'expiresAt' | 'stripeInvoiceId' | 'creditRemainingInCents' | 'currency' | 'paymentProcessor' | 'paymentProcessorReference' | 'status' |
    'amountInLocalCurrency' | 'earningsInCents'> {
    meta?: KeyValueStore
}

export class WorkspaceCreditService extends BaseService<WorkspaceCredit> {

    initRepository = (): Repository<WorkspaceCredit> => {
        return getRepository(WorkspaceCredit);
    }

    async getActiveCredits(workspaceId: string): Promise<WorkspaceCredit[]> {
        const brackets = new Brackets(qb => {
            qb.orWhere({status: IsNull()});
            qb.orWhere({status: PurchaseStatus.Settled});
        })
        const qB = this.getRepository().createQueryBuilder('wC')
            .select()
            .where({workspaceId, expiresAt: MoreThanOrEqual(new Date())})
            .andWhere(brackets)
            .orderBy({expiresAt: 'ASC'})

        return await qB.getMany()
        // return this.find({
        //     workspaceId,
        //     expiresAt: MoreThanOrEqual(new Date()),
        // }, {
        //     expiresAt: 'ASC'
        // })
    }

    async addCredit(data: AddWorkspaceCreditData) {
        return this.insert(data)
    }

    async billCredit(id: number, costInCents: number, logMessage: string) {
        await this.getRepository().createQueryBuilder()
            .update()
            .set({
                creditRemainingInCents: () => `creditRemainingInCents - :costInCents`,
                auditLog: () => `JSON_ARRAY_APPEND(auditLog, '$', :logMessage)`
            })
            .setParameters({
                costInCents,
                logMessage
            })
            .where({id: id},)
            .execute()

        return true

    }


}

