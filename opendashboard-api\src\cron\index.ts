import {CheckAffiliateEarningsAndPreparePayout, CheckAndHandleOverQuotaWorkspaces, CheckTemplatePurchasesAndPrepareCreatorPayout, renewDueBillingCycles} from "../businessLogic/billing";
import {consoleLog} from "../businessLogic/logtail";
import {ProcessDueReminders} from "../businessLogic/reminder";
import {ExpireTemplatePurchasesAfterTimeout} from "../businessLogic/templates";
import {FindAndTriggerScheduledWorkflows} from "../businessLogic/runStarter/runStarter";

const cron = require('node-cron');

export const setupInternalCron = () => {
    cron.schedule('0 * * * *', () => {
        // Run every hour
        renewDueBillingCycles().then(r => {
            consoleLog("Billing renewal completed:", r)
        }).catch(e => {
            consoleLog("Billing renewal cron error", e)
        })
    });
    cron.schedule('0 0 * * *', () => {
        // Run every day
        CheckAndHandleOverQuotaWorkspaces().then(r => {
            consoleLog("Check and handling over quota cron completed:", r)
        }).catch(e => {
            consoleLog("Check and handling over quota cron error", e)
        })
    });
    cron.schedule('0 0 1,15 * *', () => {
        // Run every day
        CheckTemplatePurchasesAndPrepareCreatorPayout().then(r => {
            consoleLog("Check template purchases and prepare creator payout completed:", {eligiblePayouts: r.eligiblePayouts})
        }).catch(e => {
            consoleLog("Check template purchases and prepare creator payout cron error", e)
        })
        CheckAffiliateEarningsAndPreparePayout().then(r => {
            consoleLog("Check affiliate earnings and prepare payout completed:", {eligiblePayouts: r.eligiblePayouts})
        }).catch(e => {
            consoleLog("Check affiliate earnings and prepare payout cron error", e)
        })
    });
    cron.schedule('*/30 * * * *', () => {
        // Run every 30 mins
        ProcessDueReminders().then(r => {
            consoleLog("Process due reminder cron completed:", r)
        }).catch(e => {
            consoleLog("Process due reminder cron error", e)
        })
    });
    cron.schedule('*/10 * * * *', () => {
        // Run every 10 mins
        ExpireTemplatePurchasesAfterTimeout().then(r => {
            consoleLog("Mark abandoned purchases cron completed:", r)
        }).catch(e => {
            consoleLog("Mark abandoned purchases cron error", e)
        })
    });
    cron.schedule('*/10 * * * *', () => {
        // Run every 10 mins
        FindAndTriggerScheduledWorkflows().then(r => {
            consoleLog("Find and trigger scheduled workflows cron completed:", r)
        }).catch(e => {
            consoleLog("Find and trigger scheduled workflows cron error", e)
        })
    });
    consoleLog("Internal Cron jobs setup")
}