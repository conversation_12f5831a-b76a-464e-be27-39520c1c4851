import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";
import {View} from "../entity/View";


export interface CreateViewData extends Pick<View, 'pageId' | 'name' | 'type' | 'definition' | 'createdById'> {
    id?: string
}

export class ViewService extends BaseService<View> {

    initRepository = (): Repository<View> => {
        return getRepository(View);
    }

}
