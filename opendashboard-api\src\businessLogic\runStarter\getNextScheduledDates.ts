const dayjs = require('dayjs')
// dayjs().format()
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone')
import {ScheduleDayOfWeek, ScheduleFrequency, ScheduleTriggerWorkflowNode_Configs, ScheduleType} from "opendb-app-db-utils/lib/typings/workflow"; // ES 2015// dependent on utc plugin

dayjs.extend(utc);
dayjs.extend(timezone);


/**
 * Returns the next `count` scheduled dates (as Date objects) based on the provided recurring configuration.
 */
export function getNextScheduledDates(
    config: ScheduleTriggerWorkflowNode_Configs,
    currentDate: Date,
    count: number
): Date[] {
    if (config.type !== ScheduleType.Recurring) {
        throw new Error("Only recurring schedules are supported in this implementation.");
    }
    switch (config.frequency) {
        case ScheduleFrequency.Daily:
            return getNextDailyDates(config, currentDate, count);
        case ScheduleFrequency.Weekly:
            return getNextWeeklyDates(config, currentDate, count);
        case ScheduleFrequency.Monthly:
            return getNextMonthlyDates(config, currentDate, count);
        default:
            throw new Error("Unsupported schedule frequency");
    }
}

/* ---------------- Daily ---------------- */
function getNextDailyDates(
    config: ScheduleTriggerWorkflowNode_Configs,
    currentDate: Date,
    count: number
): Date[] {
    const results: Date[] = [];
    const interval = config.interval ?? 1;
    const tz = config.timezone;
    const now = dayjs.tz(currentDate, tz);

    // Create candidate from current day with the desired timeOfDay (if provided)
    let candidate = now;
    if (config.timeOfDay) {
        const [hourStr, minuteStr] = config.timeOfDay.split(":");
        candidate = candidate.set("hour", parseInt(hourStr, 10))
            .set("minute", parseInt(minuteStr, 10))
            .set("second", 0)
            .set("millisecond", 0);
    }
    // If candidate is not in the future, move it forward by the daily interval.
    if (!candidate.isAfter(now)) {
        candidate = candidate.add(interval, "day");
    }

    while (results.length < count) {
        if (config.includeWeekends === false) {
            // dayjs: Sunday=0, Saturday=6
            const dayOfWeek = candidate.day();
            if (dayOfWeek === 0) { // Sunday → move to Monday
                candidate = candidate.add(1, "day");
                continue;
            }
            if (dayOfWeek === 6) { // Saturday → move to Monday (2 days ahead)
                candidate = candidate.add(2, "day");
                continue;
            }
        }
        results.push(candidate.toDate());
        candidate = candidate.add(interval, "day");
    }
    return results;
}

/* ---------------- Weekly ---------------- */
function getNextWeeklyDates(
    config: ScheduleTriggerWorkflowNode_Configs,
    currentDate: Date,
    count: number
): Date[] {
    if (!config.daysOfWeek || config.daysOfWeek.length === 0) {
        throw new Error("Weekly schedule requires at least one dayOfWeek.");
    }
    const results: Date[] = [];
    const intervalWeeks = config.interval ?? 1;
    const tz = config.timezone;
    const now = dayjs.tz(currentDate, tz);

    // Map our enum values to dayjs day numbers: Sunday=0, Monday=1, …, Saturday=6.
    const weekdayMapping: { [key in ScheduleDayOfWeek]: number } = {
        Sun: 0,
        Mon: 1,
        Tue: 2,
        Wed: 3,
        Thu: 4,
        Fri: 5,
        Sat: 6,
    };

    // Sort the days in ascending order.
    const sortedDays = [...config.daysOfWeek].sort((a, b) => weekdayMapping[a] - weekdayMapping[b]);

    let weekOffset = 0;
    while (results.length < count) {
        // Calculate the start of the week (using dayjs's startOf('week') which defaults to Sunday)
        const baseWeek = now.startOf("week").add(weekOffset * intervalWeeks, "week");
        for (const day of sortedDays) {
            let occurrence = baseWeek.day(weekdayMapping[day]);
            // Apply timeOfDay if provided.
            if (config.timeOfDay) {
                const [hourStr, minuteStr] = config.timeOfDay.split(":");
                occurrence = occurrence.set("hour", parseInt(hourStr, 10))
                    .set("minute", parseInt(minuteStr, 10))
                    .set("second", 0)
                    .set("millisecond", 0);
            }
            // Only add occurrences in the future.
            if (occurrence.isAfter(now)) {
                results.push(occurrence.toDate());
                if (results.length === count) break;
            }
        }
        weekOffset++;
    }
    return results;
}

/* ---------------- Monthly ---------------- */
function getNextMonthlyDates(
    config: ScheduleTriggerWorkflowNode_Configs,
    currentDate: Date,
    count: number
): Date[] {
    if (!config.datesOfMonth || config.datesOfMonth.length === 0) {
        throw new Error("Monthly schedule requires at least one date in datesOfMonth.");
    }
    const results: Date[] = [];
    const intervalMonths = config.interval ?? 1;
    const tz = config.timezone;
    const now = dayjs.tz(currentDate, tz);

    // Sort the dates of the month in ascending order.
    const sortedDates = [...config.datesOfMonth].sort((a, b) => a - b);
    let monthOffset = 0;
    while (results.length < count) {
        const baseMonth = now.startOf("month").add(monthOffset * intervalMonths, "month");
        for (const dayNum of sortedDates) {
            let occurrence = baseMonth.set("date", dayNum);
            // Skip invalid dates (e.g., February 30)
            if (occurrence.month() !== baseMonth.month()) continue;
            if (config.timeOfDay) {
                const [hourStr, minuteStr] = config.timeOfDay.split(":");
                occurrence = occurrence.set("hour", parseInt(hourStr, 10))
                    .set("minute", parseInt(minuteStr, 10))
                    .set("second", 0)
                    .set("millisecond", 0);
            }
            if (occurrence.isAfter(now)) {
                results.push(occurrence.toDate());
                if (results.length === count) break;
            }
        }
        monthOffset++;
    }
    return results;
}