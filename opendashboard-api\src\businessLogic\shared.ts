import {View} from "../entity/View";
import {Page} from "../entity/Page";
import {Database} from "../entity/Database";
import {AddRecordsNoAuth, AdjacentDatabases, CreateRecordsRequestData} from "./database";
import {Workspace} from "../entity/Workspace";
import {GetWorkspaceById, getWorkspaceMemberNoCacheNoAuth, MyWorkspaceMember} from "./workspace";
import {Document} from "../entity/Document";
import {ViewService} from "../service/view";
import {PageService} from "../service/page";
import {In, IsNull, Not} from "typeorm";
import {ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError} from "../errors/AppError";
import {BoardViewDefinition, DashboardElementType, DashboardViewDefinition, FormViewDefinition, InfoboxElement, LineChartElement, PieChartElement, SummaryViewDefinition, TableViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {DocumentService} from "../service/document";
import {DatabaseService} from "../service/database";
import {OnDuplicateAction, RecordService} from "../service/record";
import {arrayDeDuplicate} from "opendb-app-db-utils/lib";
import {DatabaseFieldDataType, LinkedColumn} from "opendb-app-db-utils/lib/typings/db";
import {redisGet, redisSet} from "../connection/redis";
import {UploadFile} from "./upload";
import {isLocal} from "../config";
import {Request} from "express"


export const AnonymousUserId = 'anonymous'

export interface SharedViewResponse {
    view: View
    page: Page
    database?: Database
    databaseMap: AdjacentDatabases
    workspace: Workspace
    members: MyWorkspaceMember[]
    documents: Document[]
}

const CacheKey = (id: string) => `published-view-${id}`

export const getSharedView = async (id: string) => {
    const cacheKey = CacheKey(id)
    if (!isLocal()) {
        const cacheRes = await redisGet(cacheKey)
        if (cacheRes) {
            try {
                return JSON.parse(cacheRes) as SharedViewResponse
            } catch (e) {
                // do nothing
            }
        }
    }

    // get view + page
    // get workspace + members
    // get databases + records
    // get documents
    const vS = new ViewService()
    const pS = new PageService()
    const dS = new DatabaseService()
    const rS = new RecordService()

    const view = await vS.findOne({id, isPublished: true, publishedAt: Not(IsNull())})
    if (!view) throw new NotfoundError(ErrorMessage.EntityNotFound)
    const page = await pS.findOne({id: view.pageId})
    if (!page) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)

    const workspace = await GetWorkspaceById(page.workspaceId)
    if (!workspace) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)
    const members = await getWorkspaceMemberNoCacheNoAuth(workspace.id)

    let documents: Document[] = []
    let databaseMap: AdjacentDatabases = {}

    let databaseIds: string[] = []

    if (view.type === ViewType.Document) {
        documents = await new DocumentService().find({viewId: id})
    } else if (view.type === ViewType.Dashboard) {
        // load multiple databases and records
        const definition = view.definition as DashboardViewDefinition
        if (!definition.definition.elementMap) definition.definition.elementMap = {}
        if (!definition.definition.rowsMap) definition.definition.rowsMap = {}
        if (!definition.definition.children || !Array.isArray(definition.definition.children)) definition.definition.children = []
        for (let element of Object.values(definition.definition.elementMap)) {
            if (element.type === DashboardElementType.Infobox) {
                const e = element as InfoboxElement
                if (e?.valueResolve?.databaseId) databaseIds.push(e?.valueResolve?.databaseId)
            } else if (element.type === DashboardElementType.PieChart || element.type === DashboardElementType.LineChart) {
                const e = element as PieChartElement | LineChartElement
                if (e?.recordsResolve?.databaseId) databaseIds.push(e?.recordsResolve?.databaseId)
            }
        }
    } else if (view.type === ViewType.Board) {
        const definition = view.definition as BoardViewDefinition
        if (definition.databaseId) databaseIds.push(definition.databaseId)
    } else if (view.type === ViewType.Table) {
        const definition = view.definition as TableViewDefinition
        if (definition.databaseId) databaseIds.push(definition.databaseId)
    } else if (view.type === ViewType.SummaryTable) {
        const definition = view.definition as SummaryViewDefinition
        if (definition.databaseId) databaseIds.push(definition.databaseId)
    } else if (view.type === ViewType.Form) {
        const definition = view.definition as FormViewDefinition
        if (definition.databaseId) databaseIds.push(definition.databaseId)
    }
    databaseIds = arrayDeDuplicate(databaseIds)
    if (databaseIds.length > 0) {
        const databases = await dS.find({id: In(databaseIds)})
        const records = await rS.find({databaseId: In(databaseIds)})


        for (const database of databases) {
            databaseMap[database.id] = {
                database,
                recordsMap: {}
            }
        }
        for (let record of records) {
            if (databaseMap[record.databaseId]) {
                databaseMap[record.databaseId].recordsMap[record.id] = {record}
            }
        }

        // load adjacent databases
        let linkedDatabaseIds: string[] = []
        let linkedRecordIds: string[] = []
        for (const database of databases) {
            const columns = Object.values(database.definition.columnsMap).filter(c => c.type === DatabaseFieldDataType.Linked && c.databaseId) as LinkedColumn[]

            const linkedColIds: string[] = []
            for (const column of columns) {
                if (column.databaseId && !databaseMap[column.databaseId]) {
                    linkedDatabaseIds.push(column.databaseId)
                    linkedColIds.push(column.id)
                }
            }
            const records = Object.values(databaseMap[database.id].recordsMap).map(rM => rM.record)
            for (let record of records) {
                for (let linkedColId of linkedColIds) {
                    const rawValue = record.recordValues[linkedColId]
                    if (rawValue && Array.isArray(rawValue) && rawValue.length > 0 && typeof rawValue[0] === 'string') {
                        linkedRecordIds.push(...rawValue as string[])
                    }
                }
            }
        }
        linkedDatabaseIds = arrayDeDuplicate(linkedDatabaseIds)
        linkedRecordIds = arrayDeDuplicate(linkedRecordIds)

        if (linkedDatabaseIds.length > 0) {
            const databases = await dS.find({id: In(linkedDatabaseIds)})

            for (let database of databases) {
                databaseMap[database.id] = {
                    database,
                    recordsMap: {}
                }
            }
        }
        if (linkedRecordIds.length > 0) {
            const records = await rS.find({id: In(linkedRecordIds)})
            for (let record of records) {
                if (databaseMap[record.databaseId]) {
                    databaseMap[record.databaseId].recordsMap[record.id] = {record}
                }
            }
        }
    }

    const response: SharedViewResponse = {
        view,
        page,
        workspace,
        members,
        documents,
        databaseMap
    }

    if (!isLocal()) {
        await redisSet(cacheKey, JSON.stringify(response), 30)
    }

    return response
}

export const processFormResponse = async (id: string, reqData: CreateRecordsRequestData) => {
    let {valuesList: values, onDuplicate} = reqData
    if (!values) {
        throw new RequiredParameterError("values")
    }
    if (!Array.isArray(values)) {
        throw new InvalidParameterError("values is invalid")
    }
    if (!onDuplicate) {
        onDuplicate = OnDuplicateAction.Update
    }

    const vS = new ViewService()
    const dS = new DatabaseService()

    const view = await vS.findOne({id, isPublished: true, publishedAt: Not(IsNull())})

    if (!view && view.type !== ViewType.Form) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const viewDefinition = view.definition as FormViewDefinition
    if (!viewDefinition.databaseId) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)

    const database = await dS.findOne({id: viewDefinition.databaseId})
    if (!database) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)

    const {records} = await AddRecordsNoAuth(AnonymousUserId, database, values, onDuplicate)
    return {records}
}

export const processFormUpload = async (id: string, request: Request) => {

    const vS = new ViewService()
    const pS = new PageService()

    const view = await vS.findOne({id, isPublished: true, publishedAt: Not(IsNull())})
    if (!view && view.type !== ViewType.Form) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const page = await pS.findOne({id: view.pageId})
    if (!page) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)

    const {upload} = await UploadFile(AnonymousUserId, page.workspaceId, request, true)

    return {upload}
}


