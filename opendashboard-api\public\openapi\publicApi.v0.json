{"info": {"title": "Opendashboard API", "version": "0.0.1"}, "openapi": "3.0.3", "paths": {"/workspaces": {"get": {"operationId": "ApiV1Spec_get_/workspaces", "tags": [], "summary": "Get Workspaces", "security": [{"apiKey": []}], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspaces_Workspace_____"}}}}}}}, "/workspaces/{id}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}", "tags": [], "summary": "Get Workspace", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace___"}}}}}}}, "/workspaces/{id}/uploads": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/uploads", "tags": [], "summary": "Upload Workspace File", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {}}, "additionalProperties": false, "required": ["file"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__upload_WorkspaceUpload___"}}}}}}}, "/workspaces/{id}/members": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/members", "tags": [], "summary": "Get Workspace Members", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__members_MyWorkspaceMember_____"}}}}}}}, "/workspaces/{id}/databases": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/databases", "tags": [], "summary": "Get Workspace Databases", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__databases_Database_____"}}}}}}}, "/workspaces/{id}/databases/{databaseId}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/databases/{databaseId}", "tags": [], "summary": "Get Workspace Database", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__database_Database___"}}}}}}}, "/workspaces/{id}/databases/{databaseId}/records": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Get Database Records", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__records_Record_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Create Database Records", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"valuesList": {"type": "array", "items": {"$ref": "#/components/schemas/RecordValues"}}, "onDuplicate": {"$ref": "#/components/schemas/OnDuplicateAction"}}, "additionalProperties": false, "required": ["onDuplicate", "valuesList"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__records_Record______1"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Update Database Records", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "values": {"$ref": "#/components/schemas/RecordValues"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["ids", "values"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Delete Database Records", "security": [{"apiKey": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["ids"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/webhooks/{id}/dispatch": {"get": {"operationId": "ApiV1Spec_get_/webhooks/{id}/dispatch", "tags": [], "summary": "Dispatch Get Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/webhooks/{id}/dispatch", "tags": [], "summary": "Dispatch Post Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "put": {"operationId": "ApiV1Spec_put_/webhooks/{id}/dispatch", "tags": [], "summary": "Dispatch Put Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/webhooks/{id}/dispatch", "tags": [], "summary": "Dispatch Patch Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/webhooks/{id}/dispatch", "tags": [], "summary": "Dispatch Delete Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/webhooks/{id}/dispatch/test": {"get": {"operationId": "ApiV1Spec_get_/webhooks/{id}/dispatch/test", "tags": [], "summary": "Dispatch Get Test Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/webhooks/{id}/dispatch/test", "tags": [], "summary": "Dispatch Post Test Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "put": {"operationId": "ApiV1Spec_put_/webhooks/{id}/dispatch/test", "tags": [], "summary": "Dispatch Put Test Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/webhooks/{id}/dispatch/test", "tags": [], "summary": "Dispatch Patch Test Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/webhooks/{id}/dispatch/test", "tags": [], "summary": "Dispatch Delete Test Webhook", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}}, "components": {"schemas": {"ApiResponseBody__workspaces_Workspace_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspaces": {"type": "array", "items": {"$ref": "#/components/schemas/Workspace"}}}, "additionalProperties": false, "required": ["workspaces"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseStatus": {"type": "string", "enum": ["ok", "error"]}, "Workspace": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "domain": {"type": "string"}, "isSetupCompleted": {"type": "boolean"}, "logo": {"type": "string"}, "createdById": {"type": "string"}, "ownerId": {"type": "string"}, "stripeCustomerId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "orgUseCase": {"type": "string"}, "orgType": {"type": "string"}, "orgSize": {"type": "string"}, "orgPhoneNumber": {"type": "string"}, "deletionReason": {"type": "string"}, "timezone": {"type": "string"}, "isSupportAccessEnabled": {"type": "boolean"}, "isFunctionalityLimited": {"type": "boolean"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["createdAt", "createdById", "deletedAt", "deletionReason", "domain", "id", "isFunctionalityLimited", "isSetupCompleted", "isSupportAccessEnabled", "logo", "meta", "name", "orgPhoneNumber", "orgSize", "orgType", "orgUseCase", "ownerId", "stripeCustomerId", "timezone", "updatedAt"]}, "KeyValueStore": {"type": "object", "additionalProperties": {"type": "object", "properties": {}, "additionalProperties": true}}, "ApiResponseBody__workspace_MyWorkspace___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "MyWorkspace": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/Workspace"}, "workspaceMember": {"$ref": "#/components/schemas/WorkspaceMember"}, "membersCount": {"type": "number"}, "planId": {"type": "string"}, "priceId": {"type": "string"}, "billingCycle": {"$ref": "#/components/schemas/BillingCycle"}}, "additionalProperties": false, "required": ["billingCycle", "membersCount", "planId", "priceId", "workspace", "workspaceMember"]}, "WorkspaceMember": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "userId": {"type": "string"}, "role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}, "invitedById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "deletedAt", "id", "invitedById", "role", "updatedAt", "userId", "workspaceId"]}, "WorkspaceMemberRole": {"type": "string", "enum": ["owner", "admin", "member", "collaborator", "supportUser"]}, "BillingCycle": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "stripeSubscriptionId": {"type": "string"}, "futureStripeSubscriptionId": {"type": "string"}, "planId": {"type": "string"}, "priceId": {"type": "string"}, "futurePriceId": {"type": "string"}, "anchorDay": {"type": "number"}, "startsAt": {"type": "string", "format": "date-time"}, "endsAt": {"type": "string", "format": "date-time"}, "endedAt": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "isPaid": {"type": "boolean"}, "isRenewing": {"type": "boolean"}, "costInCents": {"type": "number"}, "amountPaidInCents": {"type": "number"}, "paidAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "cyclePlanQuota": {"$ref": "#/components/schemas/UsageLimits"}, "cycleUsage": {"$ref": "#/components/schemas/UsageLimits"}, "addOnsQuota": {"$ref": "#/components/schemas/AddOnsQuota"}, "usersQuota": {"type": "number"}, "collaboratorsQuota": {"type": "number"}, "recordsQuota": {"type": "number"}, "auditLog": {"type": "array", "items": {"type": "string"}}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["addOnsQuota", "amountPaidInCents", "anchorDay", "auditLog", "<PERSON><PERSON><PERSON><PERSON>", "costInCents", "createdAt", "cyclePlanQuota", "cycleUsage", "deletedAt", "endedAt", "endsAt", "futurePriceId", "futureStripeSubscriptionId", "id", "isActive", "isPaid", "isRenewing", "meta", "paidAt", "planId", "priceId", "recordsQuota", "startsAt", "stripeSubscriptionId", "updatedAt", "usersQuota", "workspaceId"]}, "UsageLimits": {"type": "object", "properties": {"users": {"type": "number"}, "collaborators": {"type": "number"}, "records": {"type": "number"}, "emails": {"type": "number"}, "senderDomains": {"type": "number"}, "senderEmails": {"type": "number"}, "enrichment": {"type": "number"}, "aiGeneration": {"type": "number"}, "dataHistory": {"type": "number"}}, "additionalProperties": false, "required": ["aiGeneration", "collaborators", "dataHistory", "emails", "enrichment", "records", "senderDomains", "senderEmails", "users"]}, "AddOnsQuota": {"type": "object", "properties": {"users": {"type": "number"}, "collaborators": {"type": "number"}, "records": {"type": "number"}, "sendingDomains": {"type": "number"}, "sendingEmails": {"type": "number"}, "aiGeneration": {"type": "number"}, "enrichment": {"type": "number"}}, "additionalProperties": false, "required": ["aiGeneration", "collaborators", "enrichment", "records", "sendingDomains", "sendingEmails", "users"]}, "ApiResponseBody__upload_WorkspaceUpload___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"upload": {"$ref": "#/components/schemas/WorkspaceUpload"}}, "additionalProperties": false, "required": ["upload"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "WorkspaceUpload": {"type": "object", "properties": {"id": {"type": "number"}, "userId": {"type": "string"}, "workspaceId": {"type": "string"}, "creatorId": {"type": "string"}, "name": {"type": "string"}, "mimeType": {"type": "string"}, "type": {"type": "string"}, "finalUrl": {"type": "string"}, "doSpaceKey": {"type": "string"}, "thumbnailUrl": {"type": "string"}, "size": {"type": "number"}, "width": {"type": "number"}, "height": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["createdAt", "creatorId", "deletedAt", "doSpaceKey", "finalUrl", "height", "id", "meta", "mimeType", "name", "size", "thumbnailUrl", "type", "updatedAt", "userId", "width", "workspaceId"]}, "ApiResponseBody__members_MyWorkspaceMember_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"members": {"type": "array", "items": {"$ref": "#/components/schemas/MyWorkspaceMember"}}}, "additionalProperties": false, "required": ["members"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "MyWorkspaceMember": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "workspaceMember": {"$ref": "#/components/schemas/WorkspaceMember"}}, "additionalProperties": false, "required": ["user", "workspaceMember"]}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "profilePhoto": {"type": "string"}, "isEmailVerified": {"type": "boolean"}, "isSetupCompleted": {"type": "boolean"}, "isSupportAccount": {"type": "boolean"}, "activeWorkspaceId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "auditLog": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["activeWorkspaceId", "auditLog", "createdAt", "deletedAt", "email", "firstName", "id", "isEmailVerified", "isSetupCompleted", "isSupportAccount", "lastName", "profilePhoto", "updatedAt"]}, "ApiResponseBody__databases_Database_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"databases": {"type": "array", "items": {"$ref": "#/components/schemas/Database"}}}, "additionalProperties": false, "required": ["databases"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "Database": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "definition": {"$ref": "#/components/schemas/DatabaseDefinition"}, "workspaceId": {"type": "string"}, "srcPackageName": {"type": "string"}, "srcVersionNumber": {"type": "number"}, "srcVersionName": {"type": "string"}, "packageName": {"type": "string"}, "templateReleaseId": {"type": "number"}, "templateInstallId": {"type": "number"}, "versionNumber": {"type": "number"}, "versionName": {"type": "string"}, "createdById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "createdById", "definition", "deletedAt", "id", "name", "packageName", "srcPackageName", "srcVersionName", "srcVersionNumber", "templateInstallId", "templateReleaseId", "updatedAt", "versionName", "versionNumber", "workspaceId"]}, "DatabaseDefinition": {"type": "object", "properties": {"uniqueColumnId": {"type": "string"}, "titleColumnId": {"type": "string"}, "titleColumnIds": {"type": "array", "items": {"type": "string"}}, "columnIds": {"type": "array", "items": {"type": "string"}}, "columnsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DatabaseColumn"}}}, "additionalProperties": false, "required": ["columnIds", "columnsMap"]}, "DatabaseColumn": {"anyOf": [{"$ref": "#/components/schemas/TextColumn"}, {"$ref": "#/components/schemas/AIColumn"}, {"$ref": "#/components/schemas/UUIDColumn"}, {"$ref": "#/components/schemas/NumberColumn"}, {"$ref": "#/components/schemas/CheckboxColumn"}, {"$ref": "#/components/schemas/SelectColumn"}, {"$ref": "#/components/schemas/PersonColumn"}, {"$ref": "#/components/schemas/CreatedByColumn"}, {"$ref": "#/components/schemas/UpdatedByColumn"}, {"$ref": "#/components/schemas/LinkedColumn"}, {"$ref": "#/components/schemas/SummarizeColumn"}, {"$ref": "#/components/schemas/DerivedColumn"}, {"$ref": "#/components/schemas/DateColumn"}, {"$ref": "#/components/schemas/CreatedAtColumn"}, {"$ref": "#/components/schemas/UpdatedAtColumn"}, {"$ref": "#/components/schemas/FilesColumn"}]}, "TextColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["text"]}, "format": {"$ref": "#/components/schemas/TextColumnFormat"}, "isLong": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "TextColumnFormat": {"type": "string", "enum": ["text", "email", "phone", "url", "location"]}, "AIColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ai"]}, "prompt": {"type": "string"}, "maxWordOutput": {"type": "number"}, "attachmentColumnIds": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "prompt", "title", "type"]}, "UUIDColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["uuid"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "NumberColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["number"]}, "format": {"$ref": "#/components/schemas/NumberColumnFormat"}, "currency": {"type": "string"}, "divideBy": {"type": "number"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "NumberColumnFormat": {"type": "string", "enum": ["Number", "percentage", "currency"]}, "CheckboxColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["checkbox"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "SelectColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["select"]}, "optionIds": {"type": "array", "items": {"type": "string"}}, "optionsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/SelectOption"}}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "optionIds", "optionsMap", "title", "type"]}, "SelectOption": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "color": {"$ref": "#/components/schemas/Color"}}, "additionalProperties": false, "required": ["color", "id", "title"]}, "Color": {"enum": ["AtomicTangerine", "Beige", "Black", "<PERSON>", "<PERSON><PERSON>", "DarkSlateGray", "<PERSON><PERSON>", "Emerald", "EnglishViolet", "IndianRed", "Jet", "LapisLazuli", "LightGray", "LightGreen", "<PERSON><PERSON><PERSON>", "Old<PERSON>ose", "OxfordBlue", "Red", "<PERSON><PERSON><PERSON><PERSON>", "SpaceBlue", "<PERSON><PERSON>", "Turquoise", "UltraViolet", "Vanilla", "Yellow"], "type": "string"}, "PersonColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["person"]}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "CreatedByColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["created-by"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "UpdatedByColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["updated-by"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "LinkedColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["linked"]}, "databaseId": {"type": "string"}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["databaseId", "id", "title", "type"]}, "SummarizeColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["summarize"]}, "targetColumnId": {"type": "string"}, "linkedDisplayColumnId": {"type": "string"}, "aggregateBy": {"$ref": "#/components/schemas/AggregateFunction"}, "title": {"type": "string"}, "format": {"enum": ["Number", "absolute", "currency", "email", "location", "percentage", "phone", "relative", "text", "url"], "type": "string"}, "withTime": {"type": "boolean"}, "currency": {"type": "string"}, "divideBy": {"type": "number"}, "id": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["aggregateBy", "id", "linkedDisplayColumnId", "targetColumnId", "title", "type"]}, "AggregateFunction": {"enum": ["average", "count_all", "count_checked", "count_empty", "count_not_empty", "count_unchecked", "count_unique", "count_values", "date_range", "earliest_date", "latest_date", "max", "min", "percent_checked", "percent_empty", "percent_not_checked", "percent_not_empty", "range", "show_original", "show_unique", "sum"], "type": "string"}, "DerivedColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["derived"]}, "derivation": {"type": "string"}, "returnType": {"enum": ["checkbox", "number", "text"], "type": "string"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "DateColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["date"]}, "format": {"$ref": "#/components/schemas/DateColumnFormat"}, "withTime": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "DateColumnFormat": {"type": "string", "enum": ["relative", "absolute"]}, "CreatedAtColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["created-at"]}, "title": {"type": "string"}, "description": {"type": "string"}, "format": {"$ref": "#/components/schemas/DateColumnFormat"}, "id": {"type": "string"}, "withTime": {"type": "boolean"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "UpdatedAtColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["updated-at"]}, "title": {"type": "string"}, "description": {"type": "string"}, "format": {"$ref": "#/components/schemas/DateColumnFormat"}, "id": {"type": "string"}, "withTime": {"type": "boolean"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "FilesColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["files"]}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "ApiResponseBody__database_Database___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"database": {"$ref": "#/components/schemas/Database"}}, "additionalProperties": false, "required": ["database"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__records_Record_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/Record"}}}, "additionalProperties": false, "required": ["records"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "Record": {"type": "object", "properties": {"id": {"type": "string"}, "databaseId": {"type": "string"}, "createdById": {"type": "string"}, "updatedById": {"type": "string"}, "recordValues": {"$ref": "#/components/schemas/RecordValues"}, "uniqueValue": {"type": "string"}, "summaryJSON": {"type": "object", "properties": {}, "additionalProperties": true}, "summaryText": {"type": "string"}, "templateReleaseId": {"type": "number"}, "templateInstallId": {"type": "number"}, "title": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["createdAt", "createdById", "databaseId", "deletedAt", "id", "meta", "recordValues", "summaryJSON", "summaryText", "templateInstallId", "templateReleaseId", "title", "uniqueValue", "updatedAt", "updatedById"]}, "RecordValues": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DatabaseColumnDbValue"}}, "DatabaseColumnDbValue": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/FileItem"}}, {"type": "array", "items": {"type": "string"}}, {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}]}, "FileItem": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "link": {"type": "string"}, "name": {"type": "string"}, "size": {"type": "number"}}, "additionalProperties": false, "required": ["id", "link", "name", "type"]}, "ApiResponseBody__records_Record______1": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/Record"}}}, "additionalProperties": false, "required": ["records"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "CreateRecordsRequestData": {"type": "object", "properties": {"valuesList": {"type": "array", "items": {"$ref": "#/components/schemas/RecordValues"}}, "onDuplicate": {"$ref": "#/components/schemas/OnDuplicateAction"}}, "additionalProperties": false, "required": ["onDuplicate", "valuesList"]}, "OnDuplicateAction": {"type": "string", "enum": ["ignore", "update", "reject"]}, "GenericApiResponseBody": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "UpdateRecordRequestData": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "values": {"$ref": "#/components/schemas/RecordValues"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["ids", "values"]}, "DeleteRecordsRequestData": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["ids"]}}, "securitySchemes": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "apikey"}, "query": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "body": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "As Post body", "in": "body", "name": "<PERSON><PERSON><PERSON><PERSON>"}}}, "servers": [{"url": "http://localhost:3030/v0", "description": "Localhost"}, {"url": "https://api.opendashboard.app/v0", "description": "Prod"}, {"url": "https://api.stage.opendashboard.app/v0", "description": "Stage"}]}