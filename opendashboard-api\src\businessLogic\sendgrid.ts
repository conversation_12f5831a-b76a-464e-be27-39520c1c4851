import {sendHTTPRequest, TypedUpstreamApiResponse, UpstreamApiResponse} from "../utility/http";
import config from "../config";


interface Domain {
    id: number
    user_id: number
    subdomain: string
    domain: string
    username: string
    ips: Array<any>
    custom_spf: boolean
    default: boolean
    legacy: boolean
    automatic_security: boolean
    valid: boolean
    dns: {
        mail_cname: {
            valid: boolean
            type: string
            host: string
            data: string
        }
        dkim1: {
            valid: boolean
            type: string
            host: string
            data: string
        }
        dkim2: {
            valid: boolean
            type: string
            host: string
            data: string
        }
    }
}

interface ValidationResult {
    id: number
    valid: boolean
    validation_results: {
        mail_cname: {
            valid: boolean
            reason: any
        }
        dkim1: {
            valid: boolean
            reason: any
        }
        dkim2: {
            valid: boolean
            reason: any
        }
    }
}


const baseUrl = (path = '') => `https://api.sendgrid.com/v3/whitelabel${path || ''}`
const apiKey = config.SENDGRID.api_key;
const addDomain = async (domain: string) => {
    const data = {
        domain,
        subdomain: "opendbe",
        automatic_security: true
    }
    const endpoint = baseUrl('/domains')
    const headers = {
        Authorization: `Bearer ${apiKey}`,
    }
    const response: TypedUpstreamApiResponse<Domain> = processError(await sendHTTPRequest('post', endpoint, headers, data))

    return response
}

export const fullAuthorizedDomain = (domain: string) => `opendbe.${domain}`

const validateDomain = async (domainId: string) => {
    const endpoint = baseUrl(`/domains/${domainId}/validate`)
    const headers = {
        Authorization: `Bearer ${apiKey}`,
    }
    const response: TypedUpstreamApiResponse<ValidationResult> = processError(await sendHTTPRequest('post', endpoint, headers, {}))

    return response
}

const processError = (response: UpstreamApiResponse) => {
    // console.log({response})
    // if (!response.isSuccess) {
    //     response.error = (response.data as any).errors[0] || "Error occurred while sending request"
    //     // if (response.status)
    // }
    // console.log({response})

    return response
}

export const SendGrid = {
    addDomain, validateDomain
}