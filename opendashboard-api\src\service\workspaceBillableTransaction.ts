import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";
import {WorkspaceUpload} from "../entity/WorkspaceUpload";
import {WorkspaceBillableTransaction} from "../entity/WorkspaceBillableTransaction";

export class WorkspaceBillableTransactionService extends BaseService<WorkspaceBillableTransaction> {

    initRepository = (): Repository<WorkspaceBillableTransaction> => {
        return getRepository(WorkspaceBillableTransaction);
    }

}
