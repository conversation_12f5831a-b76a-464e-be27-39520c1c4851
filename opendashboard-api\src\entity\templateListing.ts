import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

@Entity()
@Index(["templateId", "versionNumber"], {unique: true})
export class TemplateListing {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    templateId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Index()
    @Column({type: 'varchar', length: 300, nullable: true})
    shortDescription: string

    @Column({type: 'text', nullable: true})
    fullDescription: string

    @Column({type: 'json', nullable: true})
    images: string[];

    @Column({type: 'int', default: 0, unsigned: true})
    versionNumber: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    categoryId: string

    @Column({type: 'json', nullable: true})
    tagIds: (string | number)[]

    @Index()
    @Column({type: 'bool', default: false})
    isFeaturedInMarketplace: boolean

    @Index()
    @Column({type: 'bool', default: false})
    isPaid: boolean

    @Index()
    @Column({type: 'bool', default: false})
    isMultiLicensePricing: boolean

    @Column({type: 'int', default: 0, unsigned: true})
    singleLicensePrice: number

    @Column({type: 'int', default: 0, unsigned: true})
    multiLicensePrice: number

    @Column({type: 'varchar', nullable: true})
    tagsText: string

    @Column({type: 'timestamp', nullable: true})
    featuredAt: Date

    @Column({type: 'timestamp', nullable: true})
    replacedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}