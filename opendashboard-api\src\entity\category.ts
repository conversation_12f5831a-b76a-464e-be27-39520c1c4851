import {Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"


export enum CategoryType {
    All = "all",
    Template = "template",
}

@Entity()
export class Category {

    @PrimaryGeneratedColumn()
    id: number

    @Column({type: "varchar"})
    name: string

    @Column({type: "varchar", nullable: true})
    description: string

    @Column({type: "varchar", unique: true})
    slug: string

    @Column({type: "varchar", default: CategoryType.All})
    type: CategoryType

    @Column({type: "varchar", nullable: true})
    coverImage: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: string

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: string


}
