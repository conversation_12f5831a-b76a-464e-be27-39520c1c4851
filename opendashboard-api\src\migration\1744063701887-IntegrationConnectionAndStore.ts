import { MigrationInterface, QueryRunner } from "typeorm";

export class IntegrationConnectionAndStore1744063701887 implements MigrationInterface {
    name = 'IntegrationConnectionAndStore1744063701887'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`workspace_integration_connection\` (\`id\` varchar(36) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`integration\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`credentials\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_695ee2d053bb2c5b54c53fbbec\` (\`workspaceId\`, \`integration\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workspace_integration_store\` (\`id\` varchar(36) NOT NULL, \`workspaceId\` varchar(50) NULL, \`integration\` varchar(50) NOT NULL, \`key\` varchar(255) NOT NULL, \`scope\` varchar(50) NOT NULL, \`value\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, UNIQUE INDEX \`IDX_ed70918a866a0d5ec1a6329381\` (\`workspaceId\`, \`integration\`, \`scope\`, \`key\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`workspace_billable_transaction\` CHANGE \`debitAmountInCents\` \`debitAmountInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_ed70918a866a0d5ec1a6329381\` ON \`workspace_integration_store\``);
        await queryRunner.query(`DROP TABLE \`workspace_integration_store\``);
        await queryRunner.query(`DROP INDEX \`IDX_695ee2d053bb2c5b54c53fbbec\` ON \`workspace_integration_connection\``);
        await queryRunner.query(`DROP TABLE \`workspace_integration_connection\``);
    }

}
