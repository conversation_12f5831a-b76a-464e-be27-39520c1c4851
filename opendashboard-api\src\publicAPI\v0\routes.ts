import {homeRoutes} from "./home/<USER>";
import {addRoutes} from "../../routes";
import {Router} from 'express'
import {workspaceRoutes} from "./workspace/routes";
import {webhooksRoutes} from "./webhook/routes";


const router = Router();

addRoutes(router, workspaceRoutes)
addRoutes(router, homeRoutes)
addRoutes(router, webhooksRoutes)

// routes.forEach(subRoute => configureRouteGroup(router, subRoute));

export const v0Router = router;