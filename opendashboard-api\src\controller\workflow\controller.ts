import {NextFunction, Request, Response} from "express";
import {ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApproveWorkflowInstance, CreateWorkflow, CreateWorkflowInstance, DeleteWorkflow, DeleteWorkflowInstance, DiscardDraftWorkflow, GetWorkflow, GetWorkflowInstances, GetWorkflowInstanceTasks, GetWorkflows, PublishWorkflow, TestWorkflowStep, UpdateWorkflow, UpdateWorkflowInstancesStatus} from "../../businessLogic/workflow";


export class WorkflowController {

    async getWorkflows(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workflows = await GetWorkflows(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workflows
            },
        }
        return response.json(
            responseData
        )
    }

    async getWorkflow(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workflow = await GetWorkflow(authInfo.userId, request.params.id, request.params.workflowId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workflow
            },
        }
        return response.json(
            responseData
        )
    }

    async createWorkflow(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workflow = await CreateWorkflow(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workflow
            },
        }
        return response.json(
            responseData
        )
    }

    async updateWorkflow(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workflow = await UpdateWorkflow(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workflow
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteWorkflow(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteWorkflow(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {},
        }
        return response.json(responseData)
    }

    async publishWorkflow(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workflow = await PublishWorkflow(authInfo.userId, request.params.id, request.params.workflowId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workflow
            },
        }
        return response.json(responseData)
    }

    async testStep(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const testResponse = await TestWorkflowStep(authInfo.userId, request.params.id, request.params.workflowId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                testResponse
            },
        }
        return response.json(responseData)
    }

    async discardDraftWorkflow(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const workflow = await DiscardDraftWorkflow(authInfo.userId, request.params.id, request.params.workflowId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                workflow
            },
        }
        return response.json(responseData)
    }

    async getInstances(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const instances = await GetWorkflowInstances(authInfo.userId, request.params.id, request.params.workflowId, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                instances
            },
        }
        return response.json(responseData)
    }

    async createInstance(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const instance = await CreateWorkflowInstance(authInfo.userId, request.params.id, request.params.workflowId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                instance
            },
        }
        return response.json(responseData)
    }

    async deleteInstance(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteWorkflowInstance(authInfo.userId, request.params.id, request.params.workflowId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {},
        }
        return response.json(responseData)
    }

    async updateInstancesStatus(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await UpdateWorkflowInstancesStatus(authInfo.userId, request.params.id, request.params.workflowId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {},
        }
        return response.json(responseData)
    }

    async getInstanceTasks(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const tasks = await GetWorkflowInstanceTasks(authInfo.userId, request.params.id, request.params.workflowId, request.params.instanceId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                tasks
            },
        }
        return response.json(responseData)
    }

    async approvePendingInstance(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await ApproveWorkflowInstance(authInfo.userId, request.params.id, request.params.workflowId, request.params.instanceId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
            },
        }
        return response.json(responseData)
    }

}











