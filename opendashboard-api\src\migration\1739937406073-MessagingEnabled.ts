import { MigrationInterface, QueryRunner } from "typeorm";

export class MessagingEnabled1739937406073 implements MigrationInterface {
    name = 'MessagingEnabled1739937406073'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`database\` ADD \`isMessagingEnabled\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`CREATE INDEX \`IDX_2fa0ac692df58c2b8537b126cd\` ON \`database\` (\`isMessagingEnabled\`)`);
        // await queryRunner.query(`UPDATE \`database\` SET \`isMessagingEnabled\` = 1 WHERE srcPackageName IN ('opendb.companies', 'opendb.contacts') OR packageName IN ('opendb.companies', 'opendb.contacts')`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_2fa0ac692df58c2b8537b126cd\` ON \`database\``);
        await queryRunner.query(`ALTER TABLE \`database\` DROP COLUMN \`isMessagingEnabled\``);
    }

}
