import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {createCampaign, createSequence, deleteCampaign, getCampaign, getCampaigns, publishCampaign, removeCampaignReview, reviewCampaign, updateCampaign, updateCampaignEmail} from "../../businessLogic/campaign";


export class CampaignController {

    async create(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {campaign} = await createCampaign(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                campaign
            },
        }
        return response.json(
            responseData
        )
    }

    async getAllCampaigns(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {campaigns} = await getCampaigns(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                campaigns
            },
        }
        return response.json(
            responseData
        )
    }

    async get(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const res = await getCampaign(authInfo.userId, request.params.id, request.params.campaignId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                ...res
            },
        }
        return response.json(
            responseData
        )
    }

    async update(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const res = await updateCampaign(authInfo.userId, request.params.id, request.params.campaignId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async doDelete(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await deleteCampaign(authInfo.userId, request.params.id, request.params.campaignId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async review(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {emails,} = await reviewCampaign(authInfo.userId, request.params.id, request.params.campaignId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {emails},
        }
        return response.json(
            responseData
        )
    }

    async removeReview(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {} = await removeCampaignReview(authInfo.userId, request.params.id, request.params.campaignId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async updateEmail(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {} = await updateCampaignEmail(authInfo.userId, request.params.id, request.params.campaignId, request.params.emailId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async publish(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {campaign} = await publishCampaign(authInfo.userId, request.params.id, request.params.campaignId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {campaign},
        }
        return response.json(
            responseData
        )
    }

    async createSeq(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {campaign} = await createSequence(authInfo.userId, request.params.id, request.params.campaignId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                campaign
            },
        }
        return response.json(
            responseData
        )
    }

}


