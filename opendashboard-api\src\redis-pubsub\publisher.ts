import {ServerProcessingError} from "../errors/AppError";

const redis = require("redis");

// const subscriber = redis.createClient();
const publisher = redis.createClient();

let isConnected = false

export const initRedisPublisher = async () => {
    await publisher.connect()

    isConnected = true
}

export const redisPubSubPublish = async (channel: string, message: string) => {
    if (!isConnected) throw new ServerProcessingError("Publish client not connected")

    await publisher.publish(channel, message);

}