"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stringify-entities";
exports.ids = ["vendor-chunks/stringify-entities"];
exports.modules = {

/***/ "(ssr)/./node_modules/stringify-entities/lib/constant/dangerous.js":
/*!*******************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/constant/dangerous.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dangerous: () => (/* binding */ dangerous)\n/* harmony export */ });\n/**\n * List of legacy (that don’t need a trailing `;`) named references which could,\n * depending on what follows them, turn into a different meaning\n *\n * @type {Array<string>}\n */\nconst dangerous = [\n  'cent',\n  'copy',\n  'divide',\n  'gt',\n  'lt',\n  'not',\n  'para',\n  'times'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi9jb25zdGFudC9kYW5nZXJvdXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsZ0RBQWdEO0FBQ2hEO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi9jb25zdGFudC9kYW5nZXJvdXMuanM/MDI3NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExpc3Qgb2YgbGVnYWN5ICh0aGF0IGRvbuKAmXQgbmVlZCBhIHRyYWlsaW5nIGA7YCkgbmFtZWQgcmVmZXJlbmNlcyB3aGljaCBjb3VsZCxcbiAqIGRlcGVuZGluZyBvbiB3aGF0IGZvbGxvd3MgdGhlbSwgdHVybiBpbnRvIGEgZGlmZmVyZW50IG1lYW5pbmdcbiAqXG4gKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn1cbiAqL1xuZXhwb3J0IGNvbnN0IGRhbmdlcm91cyA9IFtcbiAgJ2NlbnQnLFxuICAnY29weScsXG4gICdkaXZpZGUnLFxuICAnZ3QnLFxuICAnbHQnLFxuICAnbm90JyxcbiAgJ3BhcmEnLFxuICAndGltZXMnXG5dXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/constant/dangerous.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/core.js":
/*!*****************************************************!*\
  !*** ./node_modules/stringify-entities/lib/core.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   core: () => (/* binding */ core)\n/* harmony export */ });\n/**\n * @typedef CoreOptions\n * @property {ReadonlyArray<string>} [subset=[]]\n *   Whether to only escape the given subset of characters.\n * @property {boolean} [escapeOnly=false]\n *   Whether to only escape possibly dangerous characters.\n *   Those characters are `\"`, `&`, `'`, `<`, `>`, and `` ` ``.\n *\n * @typedef FormatOptions\n * @property {(code: number, next: number, options: CoreWithFormatOptions) => string} format\n *   Format strategy.\n *\n * @typedef {CoreOptions & FormatOptions & import('./util/format-smart.js').FormatSmartOptions} CoreWithFormatOptions\n */\n\nconst defaultSubsetRegex = /[\"&'<>`]/g\nconst surrogatePairsRegex = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g\nconst controlCharactersRegex =\n  // eslint-disable-next-line no-control-regex, unicorn/no-hex-escape\n  /[\\x01-\\t\\v\\f\\x0E-\\x1F\\x7F\\x81\\x8D\\x8F\\x90\\x9D\\xA0-\\uFFFF]/g\nconst regexEscapeRegex = /[|\\\\{}()[\\]^$+*?.]/g\n\n/** @type {WeakMap<ReadonlyArray<string>, RegExp>} */\nconst subsetToRegexCache = new WeakMap()\n\n/**\n * Encode certain characters in `value`.\n *\n * @param {string} value\n * @param {CoreWithFormatOptions} options\n * @returns {string}\n */\nfunction core(value, options) {\n  value = value.replace(\n    options.subset\n      ? charactersToExpressionCached(options.subset)\n      : defaultSubsetRegex,\n    basic\n  )\n\n  if (options.subset || options.escapeOnly) {\n    return value\n  }\n\n  return (\n    value\n      // Surrogate pairs.\n      .replace(surrogatePairsRegex, surrogate)\n      // BMP control characters (C0 except for LF, CR, SP; DEL; and some more\n      // non-ASCII ones).\n      .replace(controlCharactersRegex, basic)\n  )\n\n  /**\n   * @param {string} pair\n   * @param {number} index\n   * @param {string} all\n   */\n  function surrogate(pair, index, all) {\n    return options.format(\n      (pair.charCodeAt(0) - 0xd800) * 0x400 +\n        pair.charCodeAt(1) -\n        0xdc00 +\n        0x10000,\n      all.charCodeAt(index + 2),\n      options\n    )\n  }\n\n  /**\n   * @param {string} character\n   * @param {number} index\n   * @param {string} all\n   */\n  function basic(character, index, all) {\n    return options.format(\n      character.charCodeAt(0),\n      all.charCodeAt(index + 1),\n      options\n    )\n  }\n}\n\n/**\n * A wrapper function that caches the result of `charactersToExpression` with a WeakMap.\n * This can improve performance when tooling calls `charactersToExpression` repeatedly\n * with the same subset.\n *\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpressionCached(subset) {\n  let cached = subsetToRegexCache.get(subset)\n\n  if (!cached) {\n    cached = charactersToExpression(subset)\n    subsetToRegexCache.set(subset, cached)\n  }\n\n  return cached\n}\n\n/**\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpression(subset) {\n  /** @type {Array<string>} */\n  const groups = []\n  let index = -1\n\n  while (++index < subset.length) {\n    groups.push(subset[index].replace(regexEscapeRegex, '\\\\$&'))\n  }\n\n  return new RegExp('(?:' + groups.join('|') + ')', 'g')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/stringify-entities/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringifyEntities: () => (/* binding */ stringifyEntities),\n/* harmony export */   stringifyEntitiesLight: () => (/* binding */ stringifyEntitiesLight)\n/* harmony export */ });\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/stringify-entities/lib/core.js\");\n/* harmony import */ var _util_format_smart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/format-smart.js */ \"(ssr)/./node_modules/stringify-entities/lib/util/format-smart.js\");\n/* harmony import */ var _util_format_basic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/format-basic.js */ \"(ssr)/./node_modules/stringify-entities/lib/util/format-basic.js\");\n/**\n * @typedef {import('./core.js').CoreOptions & import('./util/format-smart.js').FormatSmartOptions} Options\n * @typedef {import('./core.js').CoreOptions} LightOptions\n */\n\n\n\n\n\n/**\n * Encode special characters in `value`.\n *\n * @param {string} value\n *   Value to encode.\n * @param {Options} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntities(value, options) {\n  return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(value, Object.assign({format: _util_format_smart_js__WEBPACK_IMPORTED_MODULE_1__.formatSmart}, options))\n}\n\n/**\n * Encode special characters in `value` as hexadecimals.\n *\n * @param {string} value\n *   Value to encode.\n * @param {LightOptions} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntitiesLight(value, options) {\n  return (0,_core_js__WEBPACK_IMPORTED_MODULE_0__.core)(value, Object.assign({format: _util_format_basic_js__WEBPACK_IMPORTED_MODULE_2__.formatBasic}, options))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/util/format-basic.js":
/*!******************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/format-basic.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatBasic: () => (/* binding */ formatBasic)\n/* harmony export */ });\n/**\n * The smallest way to encode a character.\n *\n * @param {number} code\n * @returns {string}\n */\nfunction formatBasic(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL2Zvcm1hdC1iYXNpYy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWE7QUFDYjtBQUNPO0FBQ1AscURBQXFEO0FBQ3JEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL3V0aWwvZm9ybWF0LWJhc2ljLmpzP2Y4NGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaGUgc21hbGxlc3Qgd2F5IHRvIGVuY29kZSBhIGNoYXJhY3Rlci5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gY29kZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEJhc2ljKGNvZGUpIHtcbiAgcmV0dXJuICcmI3gnICsgY29kZS50b1N0cmluZygxNikudG9VcHBlckNhc2UoKSArICc7J1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/util/format-basic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/util/format-smart.js":
/*!******************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/format-smart.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatSmart: () => (/* binding */ formatSmart)\n/* harmony export */ });\n/* harmony import */ var _to_hexadecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./to-hexadecimal.js */ \"(ssr)/./node_modules/stringify-entities/lib/util/to-hexadecimal.js\");\n/* harmony import */ var _to_decimal_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./to-decimal.js */ \"(ssr)/./node_modules/stringify-entities/lib/util/to-decimal.js\");\n/* harmony import */ var _to_named_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./to-named.js */ \"(ssr)/./node_modules/stringify-entities/lib/util/to-named.js\");\n/**\n * @typedef FormatSmartOptions\n * @property {boolean} [useNamedReferences=false]\n *   Prefer named character references (`&amp;`) where possible.\n * @property {boolean} [useShortestReferences=false]\n *   Prefer the shortest possible reference, if that results in less bytes.\n *   **Note**: `useNamedReferences` can be omitted when using `useShortestReferences`.\n * @property {boolean} [omitOptionalSemicolons=false]\n *   Whether to omit semicolons when possible.\n *   **Note**: This creates what HTML calls “parse errors” but is otherwise still valid HTML — don’t use this except when building a minifier.\n *   Omitting semicolons is possible for certain named and numeric references in some cases.\n * @property {boolean} [attribute=false]\n *   Create character references which don’t fail in attributes.\n *   **Note**: `attribute` only applies when operating dangerously with\n *   `omitOptionalSemicolons: true`.\n */\n\n\n\n\n\n/**\n * Configurable ways to encode a character yielding pretty or small results.\n *\n * @param {number} code\n * @param {number} next\n * @param {FormatSmartOptions} options\n * @returns {string}\n */\nfunction formatSmart(code, next, options) {\n  let numeric = (0,_to_hexadecimal_js__WEBPACK_IMPORTED_MODULE_0__.toHexadecimal)(code, next, options.omitOptionalSemicolons)\n  /** @type {string|undefined} */\n  let named\n\n  if (options.useNamedReferences || options.useShortestReferences) {\n    named = (0,_to_named_js__WEBPACK_IMPORTED_MODULE_1__.toNamed)(\n      code,\n      next,\n      options.omitOptionalSemicolons,\n      options.attribute\n    )\n  }\n\n  // Use the shortest numeric reference when requested.\n  // A simple algorithm would use decimal for all code points under 100, as\n  // those are shorter than hexadecimal:\n  //\n  // * `&#99;` vs `&#x63;` (decimal shorter)\n  // * `&#100;` vs `&#x64;` (equal)\n  //\n  // However, because we take `next` into consideration when `omit` is used,\n  // And it would be possible that decimals are shorter on bigger values as\n  // well if `next` is hexadecimal but not decimal, we instead compare both.\n  if (\n    (options.useShortestReferences || !named) &&\n    options.useShortestReferences\n  ) {\n    const decimal = (0,_to_decimal_js__WEBPACK_IMPORTED_MODULE_2__.toDecimal)(code, next, options.omitOptionalSemicolons)\n\n    if (decimal.length < numeric.length) {\n      numeric = decimal\n    }\n  }\n\n  return named &&\n    (!options.useShortestReferences || named.length < numeric.length)\n    ? named\n    : numeric\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL2Zvcm1hdC1zbWFydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNBLGNBQWMsU0FBUztBQUN2Qiw4Q0FBOEM7QUFDOUMsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0E7QUFDQSxjQUFjLFNBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBOztBQUVpRDtBQUNSO0FBQ0o7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxvQkFBb0I7QUFDL0IsYUFBYTtBQUNiO0FBQ087QUFDUCxnQkFBZ0IsaUVBQWE7QUFDN0IsYUFBYSxrQkFBa0I7QUFDL0I7O0FBRUE7QUFDQSxZQUFZLHFEQUFPO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsWUFBWTtBQUN6QixjQUFjLFlBQVk7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix5REFBUzs7QUFFN0I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL2Zvcm1hdC1zbWFydC5qcz8xNzg5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYgRm9ybWF0U21hcnRPcHRpb25zXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IFt1c2VOYW1lZFJlZmVyZW5jZXM9ZmFsc2VdXG4gKiAgIFByZWZlciBuYW1lZCBjaGFyYWN0ZXIgcmVmZXJlbmNlcyAoYCZhbXA7YCkgd2hlcmUgcG9zc2libGUuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IFt1c2VTaG9ydGVzdFJlZmVyZW5jZXM9ZmFsc2VdXG4gKiAgIFByZWZlciB0aGUgc2hvcnRlc3QgcG9zc2libGUgcmVmZXJlbmNlLCBpZiB0aGF0IHJlc3VsdHMgaW4gbGVzcyBieXRlcy5cbiAqICAgKipOb3RlKio6IGB1c2VOYW1lZFJlZmVyZW5jZXNgIGNhbiBiZSBvbWl0dGVkIHdoZW4gdXNpbmcgYHVzZVNob3J0ZXN0UmVmZXJlbmNlc2AuXG4gKiBAcHJvcGVydHkge2Jvb2xlYW59IFtvbWl0T3B0aW9uYWxTZW1pY29sb25zPWZhbHNlXVxuICogICBXaGV0aGVyIHRvIG9taXQgc2VtaWNvbG9ucyB3aGVuIHBvc3NpYmxlLlxuICogICAqKk5vdGUqKjogVGhpcyBjcmVhdGVzIHdoYXQgSFRNTCBjYWxscyDigJxwYXJzZSBlcnJvcnPigJ0gYnV0IGlzIG90aGVyd2lzZSBzdGlsbCB2YWxpZCBIVE1MIOKAlCBkb27igJl0IHVzZSB0aGlzIGV4Y2VwdCB3aGVuIGJ1aWxkaW5nIGEgbWluaWZpZXIuXG4gKiAgIE9taXR0aW5nIHNlbWljb2xvbnMgaXMgcG9zc2libGUgZm9yIGNlcnRhaW4gbmFtZWQgYW5kIG51bWVyaWMgcmVmZXJlbmNlcyBpbiBzb21lIGNhc2VzLlxuICogQHByb3BlcnR5IHtib29sZWFufSBbYXR0cmlidXRlPWZhbHNlXVxuICogICBDcmVhdGUgY2hhcmFjdGVyIHJlZmVyZW5jZXMgd2hpY2ggZG9u4oCZdCBmYWlsIGluIGF0dHJpYnV0ZXMuXG4gKiAgICoqTm90ZSoqOiBgYXR0cmlidXRlYCBvbmx5IGFwcGxpZXMgd2hlbiBvcGVyYXRpbmcgZGFuZ2Vyb3VzbHkgd2l0aFxuICogICBgb21pdE9wdGlvbmFsU2VtaWNvbG9uczogdHJ1ZWAuXG4gKi9cblxuaW1wb3J0IHt0b0hleGFkZWNpbWFsfSBmcm9tICcuL3RvLWhleGFkZWNpbWFsLmpzJ1xuaW1wb3J0IHt0b0RlY2ltYWx9IGZyb20gJy4vdG8tZGVjaW1hbC5qcydcbmltcG9ydCB7dG9OYW1lZH0gZnJvbSAnLi90by1uYW1lZC5qcydcblxuLyoqXG4gKiBDb25maWd1cmFibGUgd2F5cyB0byBlbmNvZGUgYSBjaGFyYWN0ZXIgeWllbGRpbmcgcHJldHR5IG9yIHNtYWxsIHJlc3VsdHMuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IGNvZGVcbiAqIEBwYXJhbSB7bnVtYmVyfSBuZXh0XG4gKiBAcGFyYW0ge0Zvcm1hdFNtYXJ0T3B0aW9uc30gb3B0aW9uc1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFNtYXJ0KGNvZGUsIG5leHQsIG9wdGlvbnMpIHtcbiAgbGV0IG51bWVyaWMgPSB0b0hleGFkZWNpbWFsKGNvZGUsIG5leHQsIG9wdGlvbnMub21pdE9wdGlvbmFsU2VtaWNvbG9ucylcbiAgLyoqIEB0eXBlIHtzdHJpbmd8dW5kZWZpbmVkfSAqL1xuICBsZXQgbmFtZWRcblxuICBpZiAob3B0aW9ucy51c2VOYW1lZFJlZmVyZW5jZXMgfHwgb3B0aW9ucy51c2VTaG9ydGVzdFJlZmVyZW5jZXMpIHtcbiAgICBuYW1lZCA9IHRvTmFtZWQoXG4gICAgICBjb2RlLFxuICAgICAgbmV4dCxcbiAgICAgIG9wdGlvbnMub21pdE9wdGlvbmFsU2VtaWNvbG9ucyxcbiAgICAgIG9wdGlvbnMuYXR0cmlidXRlXG4gICAgKVxuICB9XG5cbiAgLy8gVXNlIHRoZSBzaG9ydGVzdCBudW1lcmljIHJlZmVyZW5jZSB3aGVuIHJlcXVlc3RlZC5cbiAgLy8gQSBzaW1wbGUgYWxnb3JpdGhtIHdvdWxkIHVzZSBkZWNpbWFsIGZvciBhbGwgY29kZSBwb2ludHMgdW5kZXIgMTAwLCBhc1xuICAvLyB0aG9zZSBhcmUgc2hvcnRlciB0aGFuIGhleGFkZWNpbWFsOlxuICAvL1xuICAvLyAqIGAmIzk5O2AgdnMgYCYjeDYzO2AgKGRlY2ltYWwgc2hvcnRlcilcbiAgLy8gKiBgJiMxMDA7YCB2cyBgJiN4NjQ7YCAoZXF1YWwpXG4gIC8vXG4gIC8vIEhvd2V2ZXIsIGJlY2F1c2Ugd2UgdGFrZSBgbmV4dGAgaW50byBjb25zaWRlcmF0aW9uIHdoZW4gYG9taXRgIGlzIHVzZWQsXG4gIC8vIEFuZCBpdCB3b3VsZCBiZSBwb3NzaWJsZSB0aGF0IGRlY2ltYWxzIGFyZSBzaG9ydGVyIG9uIGJpZ2dlciB2YWx1ZXMgYXNcbiAgLy8gd2VsbCBpZiBgbmV4dGAgaXMgaGV4YWRlY2ltYWwgYnV0IG5vdCBkZWNpbWFsLCB3ZSBpbnN0ZWFkIGNvbXBhcmUgYm90aC5cbiAgaWYgKFxuICAgIChvcHRpb25zLnVzZVNob3J0ZXN0UmVmZXJlbmNlcyB8fCAhbmFtZWQpICYmXG4gICAgb3B0aW9ucy51c2VTaG9ydGVzdFJlZmVyZW5jZXNcbiAgKSB7XG4gICAgY29uc3QgZGVjaW1hbCA9IHRvRGVjaW1hbChjb2RlLCBuZXh0LCBvcHRpb25zLm9taXRPcHRpb25hbFNlbWljb2xvbnMpXG5cbiAgICBpZiAoZGVjaW1hbC5sZW5ndGggPCBudW1lcmljLmxlbmd0aCkge1xuICAgICAgbnVtZXJpYyA9IGRlY2ltYWxcbiAgICB9XG4gIH1cblxuICByZXR1cm4gbmFtZWQgJiZcbiAgICAoIW9wdGlvbnMudXNlU2hvcnRlc3RSZWZlcmVuY2VzIHx8IG5hbWVkLmxlbmd0aCA8IG51bWVyaWMubGVuZ3RoKVxuICAgID8gbmFtZWRcbiAgICA6IG51bWVyaWNcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/util/format-smart.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/util/to-decimal.js":
/*!****************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/to-decimal.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toDecimal: () => (/* binding */ toDecimal)\n/* harmony export */ });\nconst decimalRegex = /\\d/\n\n/**\n * Configurable ways to encode characters as decimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toDecimal(code, next, omit) {\n  const value = '&#' + String(code)\n  return omit && next && !decimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL3RvLWRlY2ltYWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxtQkFBbUI7QUFDOUIsYUFBYTtBQUNiO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3N0cmluZ2lmeS1lbnRpdGllcy9saWIvdXRpbC90by1kZWNpbWFsLmpzPzQ4YjciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZGVjaW1hbFJlZ2V4ID0gL1xcZC9cblxuLyoqXG4gKiBDb25maWd1cmFibGUgd2F5cyB0byBlbmNvZGUgY2hhcmFjdGVycyBhcyBkZWNpbWFsIHJlZmVyZW5jZXMuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IGNvZGVcbiAqIEBwYXJhbSB7bnVtYmVyfSBuZXh0XG4gKiBAcGFyYW0ge2Jvb2xlYW58dW5kZWZpbmVkfSBvbWl0XG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdG9EZWNpbWFsKGNvZGUsIG5leHQsIG9taXQpIHtcbiAgY29uc3QgdmFsdWUgPSAnJiMnICsgU3RyaW5nKGNvZGUpXG4gIHJldHVybiBvbWl0ICYmIG5leHQgJiYgIWRlY2ltYWxSZWdleC50ZXN0KFN0cmluZy5mcm9tQ2hhckNvZGUobmV4dCkpXG4gICAgPyB2YWx1ZVxuICAgIDogdmFsdWUgKyAnOydcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/util/to-decimal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/util/to-hexadecimal.js":
/*!********************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/to-hexadecimal.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHexadecimal: () => (/* binding */ toHexadecimal)\n/* harmony export */ });\nconst hexadecimalRegex = /[\\dA-Fa-f]/\n\n/**\n * Configurable ways to encode characters as hexadecimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toHexadecimal(code, next, omit) {\n  const value = '&#x' + code.toString(16).toUpperCase()\n  return omit && next && !hexadecimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3RyaW5naWZ5LWVudGl0aWVzL2xpYi91dGlsL3RvLWhleGFkZWNpbWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsbUJBQW1CO0FBQzlCLGFBQWE7QUFDYjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zdHJpbmdpZnktZW50aXRpZXMvbGliL3V0aWwvdG8taGV4YWRlY2ltYWwuanM/NGQwZiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBoZXhhZGVjaW1hbFJlZ2V4ID0gL1tcXGRBLUZhLWZdL1xuXG4vKipcbiAqIENvbmZpZ3VyYWJsZSB3YXlzIHRvIGVuY29kZSBjaGFyYWN0ZXJzIGFzIGhleGFkZWNpbWFsIHJlZmVyZW5jZXMuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IGNvZGVcbiAqIEBwYXJhbSB7bnVtYmVyfSBuZXh0XG4gKiBAcGFyYW0ge2Jvb2xlYW58dW5kZWZpbmVkfSBvbWl0XG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdG9IZXhhZGVjaW1hbChjb2RlLCBuZXh0LCBvbWl0KSB7XG4gIGNvbnN0IHZhbHVlID0gJyYjeCcgKyBjb2RlLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpXG4gIHJldHVybiBvbWl0ICYmIG5leHQgJiYgIWhleGFkZWNpbWFsUmVnZXgudGVzdChTdHJpbmcuZnJvbUNoYXJDb2RlKG5leHQpKVxuICAgID8gdmFsdWVcbiAgICA6IHZhbHVlICsgJzsnXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/util/to-hexadecimal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/stringify-entities/lib/util/to-named.js":
/*!**************************************************************!*\
  !*** ./node_modules/stringify-entities/lib/util/to-named.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toNamed: () => (/* binding */ toNamed)\n/* harmony export */ });\n/* harmony import */ var character_entities_legacy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! character-entities-legacy */ \"(ssr)/./node_modules/character-entities-legacy/index.js\");\n/* harmony import */ var character_entities_html4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! character-entities-html4 */ \"(ssr)/./node_modules/character-entities-html4/index.js\");\n/* harmony import */ var _constant_dangerous_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constant/dangerous.js */ \"(ssr)/./node_modules/stringify-entities/lib/constant/dangerous.js\");\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * `characterEntitiesHtml4` but inverted.\n *\n * @type {Record<string, string>}\n */\nconst characters = {}\n\n/** @type {string} */\nlet key\n\nfor (key in character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4) {\n  if (own.call(character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4, key)) {\n    characters[character_entities_html4__WEBPACK_IMPORTED_MODULE_0__.characterEntitiesHtml4[key]] = key\n  }\n}\n\nconst notAlphanumericRegex = /[^\\dA-Za-z]/\n\n/**\n * Configurable ways to encode characters as named references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @param {boolean|undefined} attribute\n * @returns {string}\n */\nfunction toNamed(code, next, omit, attribute) {\n  const character = String.fromCharCode(code)\n\n  if (own.call(characters, character)) {\n    const name = characters[character]\n    const value = '&' + name\n\n    if (\n      omit &&\n      character_entities_legacy__WEBPACK_IMPORTED_MODULE_1__.characterEntitiesLegacy.includes(name) &&\n      !_constant_dangerous_js__WEBPACK_IMPORTED_MODULE_2__.dangerous.includes(name) &&\n      (!attribute ||\n        (next &&\n          next !== 61 /* `=` */ &&\n          notAlphanumericRegex.test(String.fromCharCode(next))))\n    ) {\n      return value\n    }\n\n    return value + ';'\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stringify-entities/lib/util/to-named.js\n");

/***/ })

};
;