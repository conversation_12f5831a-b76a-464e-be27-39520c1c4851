import {getRepository} from '../connection/db';
import {BaseService} from './service';
import {Repository} from 'typeorm/repository/Repository';
import {ServerProcessingError, UniqueConstraintError} from "../errors/AppError";
import {WorkspaceMember} from "../entity/WorkspaceMember";

export interface CreateWorkspaceMember extends Pick<WorkspaceMember, 'invitedById' | 'userId' | 'role' | 'workspaceId'> {
}

export class WorkspaceMemberService extends BaseService<WorkspaceMember> {

    initRepository = (): Repository<WorkspaceMember> => {
        return getRepository(WorkspaceMember);
    }

    createWorkspaceMember = async (data: CreateWorkspaceMember) => {
        try {
            return await this.insert(data);
        } catch (err) {
            if (err.message.includes("Duplicate entry")) throw new UniqueConstraintError('userId')
            throw new ServerProcessingError(err.message)
        }
    }


}




