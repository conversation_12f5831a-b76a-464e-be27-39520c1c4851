import {
    Column,
    CreateDateColumn,
    Entity, Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"


@Entity()
export class Tag {

    @PrimaryGeneratedColumn()
    id: number

    @Column({type: "varchar"})
    name: string

    @Column({type: "varchar", nullable: true})
    description: string

    @Column({type: "varchar", unique: true})
    slug: string

    @Column({type: "varchar", nullable: true})
    coverImage: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: string

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: string

    @Index()
    @Column({type: "int", default: 1})
    hits: number


}
