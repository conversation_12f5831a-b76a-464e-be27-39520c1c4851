import {GetMyWorkspace, getWorkspaceMemberNoCacheNoAuth} from "./workspace";
import {ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, ServerProcessingError, UnauthorizedError} from "../errors/AppError";
import {Record} from "../entity/Record";
import {Database} from "../entity/Database";
import {DatabaseService} from "../service/database";
import {RecordService} from "../service/record";
import {Brackets, In, LessThanOrEqual} from "typeorm";
import {ReminderService} from "../service/reminder";
import {arrayDeDuplicate, isDateObjValid} from "opendb-app-db-utils/lib";
import {Reminder} from "../entity/reminder";
import {PaginationParams, resolvePaginationParams} from "./document";
import {redisAcquireLock, redisReleaseLock} from "../connection/redis";
import {CRON_LOCK_KEYS} from "./billing";
import {broadcastWorkspaceDeletedReminder, broadcastWorkspaceReminder} from "../socketio/workspace";
import {CreateNotificationData, CreateNotifications} from "./notification";
import {Notification} from "../entity/notification";
import {WorkspaceService} from "../service/workspace";
import {Workspace} from "../entity/Workspace";
import {appUrl} from "../config";

export enum NotifyTime {
    _2Hours = "2_hours",
    _1Day = "1_day",
    _1Week = "1_week",
    _1Month = "1_month",
    _6Months = "6_months",
    _1Year = "1_year",
}

export interface CreateReminderData {
    title: string
    description?: string
    databaseId?: string
    recordId?: string
    // notifyTimes: (string | NotifyTime)[]
    notifyDates: string[]
    assignedToUserIds?: string[]
}

export interface WorkspaceReminder {
    reminder: Reminder
    record?: Record
    database?: Database
}

export const CreateReminder = async (userId: string, workspaceId: string, data: CreateReminderData) => {
    const {notifyDates, nextNotifyAt, assignedToUserIds, database, record} = await validateReminderData(userId, workspaceId, data)

    const s = new ReminderService()
    const reminder1 = await s.insert({
        title: data.title,
        description: data.description,
        workspaceId,
        createdById: userId,
        updatedById: userId,
        databaseId: data.databaseId,
        recordId: data.recordId,
        // notifyTimes: data.notifyTimes,
        notifyDates,
        assignedToUserIds: assignedToUserIds.join(','),
        nextNotifyAt: nextNotifyAt || notifyDates[0]
    })

    const reminder: WorkspaceReminder = {
        record, reminder: reminder1, database
    }
    broadcastWorkspaceReminder(workspaceId, reminder)
    return {
        reminder
    }
}

export interface UpdateReminderData extends CreateReminderData {
    id: string
}

const validateReminderData = async (userId: string, workspaceId: string, data: CreateReminderData) => {
    if (!data.title) throw new RequiredParameterError("title")
    // if (!data.notifyTimes) throw new RequiredParameterError("notifyTimes")
    if (!data.notifyDates) throw new RequiredParameterError("notifyDates")
    // if (!Array.isArray(data.notifyTimes) || data.notifyTimes.length === 0) throw new InvalidParameterError("notifyTimes should be an array of times")
    if (!Array.isArray(data.notifyDates) || data.notifyDates.length === 0) throw new InvalidParameterError("notifyDates should be atleast 1")
    if (!Array.isArray(data.assignedToUserIds) || data.assignedToUserIds.length === 0) throw new InvalidParameterError("notifyDates should be an atleast 1")

    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    let record: Record = undefined
    let database: Database = undefined
    if (data.databaseId || data.recordId) {
        if (!data.databaseId || !data.recordId) {
            throw new RequiredParameterError("databaseId|recordId")
        }
        const dS = new DatabaseService()
        database = await dS.findOne({id: data.databaseId, workspaceId})
        if (!database) throw new NotfoundError(ErrorMessage.EntityNotFound)

        const rS = new RecordService()
        record = await rS.findOne({id: data.recordId, databaseId: data.databaseId})
        if (!record) throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId);
    const memberIds = members.map(m => m.user.id)

    const assignedToUserIds = arrayDeDuplicate(data.assignedToUserIds.filter(id => memberIds.includes(id)))
    const notifyDates: Date[] = data.notifyDates
        .map(d => new Date(d))
        .filter(d => !!isDateObjValid(d))
        .sort((a, b) => a.getTime() - b.getTime()); // Sort dates in ascending order

    let nextNotifyAt: Date | null = null;
    const now = new Date().getTime(); // Get current time in milliseconds

    for (let notifyDate of notifyDates) {
        if (notifyDate.getTime() > now) { // Compare using getTime()
            nextNotifyAt = notifyDate; // Set the next notify date
            break; // Exit the loop once the next date is found
        }
    }


    return {
        notifyDates, assignedToUserIds, database, record, nextNotifyAt
    }
}

export const UpdateReminder = async (userId: string, workspaceId: string, data: UpdateReminderData) => {
    const s = new ReminderService()

    const reminder1 = await s.findOne({workspaceId, id: data.id})
    if (!reminder1) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const oldAssignedToUserIds = reminder1.assignedToUserIds.split(',')
    if (reminder1.createdById !== userId && !oldAssignedToUserIds.includes(userId)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {notifyDates, nextNotifyAt, assignedToUserIds, database, record} = await validateReminderData(userId, workspaceId, data)

    const update: Partial<Reminder> = {
        title: data.title,
        description: data.description,
        workspaceId,
        createdById: userId,
        updatedById: userId,
        databaseId: data.databaseId,
        recordId: data.recordId,
        // notifyTimes: data.notifyTimes,
        notifyDates,
        assignedToUserIds: assignedToUserIds.join(','),
        nextNotifyAt
    }
    await s.update({id: data.id}, update)

    const reminder: WorkspaceReminder = {
        record, reminder: {...reminder1, ...update}, database
    }
    broadcastWorkspaceReminder(workspaceId, reminder)
    return {
        reminder
    }
}

export interface MarkReminderResolvedData {
    resolved?: boolean
}

export const MarkReminderResolved = async (userId: string, workspaceId: string, id: string, data: MarkReminderResolvedData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    const s = new ReminderService()
    const {reminders} = await GetReminders(userId, workspaceId, {id})
    const reminder1 = reminders[0]
    if (!reminder1) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    // if (reminder1.reminder.createdById !== userId && !reminder1.reminder.assignedToUserIds.includes(userId)) {
    //     throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    // }
    const update: Partial<Reminder> = {
        isResolved: !!data.resolved
    }
    if (data.resolved) {
        update.resolvedAt = new Date()
        update.resolvedById = userId
    }
    await s.update({id}, update)

    reminder1.reminder = {...reminder1.reminder, ...update}
    broadcastWorkspaceReminder(workspaceId, reminder1)
    return {reminder: reminder1}
}

export interface GetReminderParams extends PaginationParams {
    id?: string
    databaseId?: string
    recordId?: string
    type?: 'record' | 'user'
    filter?: 'open' | 'resolved'
}

export const GetReminders = async (userId: string, workspaceId: string, params: GetReminderParams) => {
    const member = userId ? await GetMyWorkspace(userId, workspaceId) : null
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const {offset, page, limit} = resolvePaginationParams(params)

    const brackets = new Brackets(qb => {
        qb.where("rM.createdById=:userId", {userId});
        qb.orWhere("rM.updatedById=:userId", {userId});
        qb.orWhere("rM.assignedToUserIds LIKE :likeUserId", {likeUserId: `%${userId}%`});
    })
    const qB = new ReminderService()
        .getRepository()
        .createQueryBuilder("rM")
        .select()
        .addSelect("r")
        .where({workspaceId})
        .leftJoin(Record, "r", "rM.recordId=r.id AND rM.databaseId=r.databaseId")
        .limit(limit)
        .offset(offset)
        .orderBy("rM.createdAt", "DESC")

    if (params.id) {
        qB.andWhere("rM.id=:id", {id: params.id})
    } else if (params.type === 'record' && params.databaseId && params.recordId) {
        qB.andWhere("rM.databaseId=:databaseId AND rM.recordId=:recordId", {databaseId: params.databaseId, recordId: params.recordId})
    } else if (params.type === 'user') {
        qB.andWhere(brackets)
    } else return {
        reminders: []
    }
    if (params.query && !!params.query.trim()) {
        qB.andWhere("rM.name LIKE :query", {query: `%${params.query.trim()}%`})
    }
    if (params.filter) {
        qB.andWhere("rM.isResolved=:isResolved", {isResolved: params.filter === 'resolved'})
    }

    const rawResults = await qB.getRawMany()
    const reminders: WorkspaceReminder[] = []
    for (const r of rawResults) {
        const note: WorkspaceReminder = {
            reminder: {} as Reminder
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "rM") {
                note.reminder[field] = r[key]
            } else if (pre === "r") {
                if (!note.record) note.record = {} as Record
                note.record[field] = r[key]
            }
        }
        reminders.push(note)
    }
    const databaseIds = reminders.map(n => n.reminder.databaseId).filter(i => !!i)
    const databases = databaseIds.length > 0 ? await new DatabaseService().find({workspaceId, id: In(databaseIds)}) : []
    const databasesMap: { [id: string]: Database } = {}
    for (let database of databases) {
        databasesMap[database.id] = database
    }
    for (let note of reminders) {
        if (note.reminder.databaseId && databasesMap[note.reminder.databaseId]) {
            note.database = databasesMap[note.reminder.databaseId]
        }
    }
    return {reminders}
}

export interface DeleteReminderParams {
    id: string
}

export const DeleteReminder = async (userId: string, workspaceId: string, params: DeleteReminderParams) => {
    const member = userId ? await GetMyWorkspace(userId, workspaceId) : null
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    const s2 = new ReminderService()
    const reminder = await s2.findOne({id: params.id, workspaceId})
    if (!reminder) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (reminder.createdById !== userId) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    await s2.remove({id: reminder.id})

    broadcastWorkspaceDeletedReminder(workspaceId, {reminder})

    return true
}

export const GetDueReminders = async () => {
    const s = new ReminderService()
    const reminders = await s.find({nextNotifyAt: LessThanOrEqual(new Date()), isResolved: false})
    return {reminders}
}

export const ProcessDueReminders = async () => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.PROCESS_DUE_REMINDERS)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }
    const {reminders} = await GetDueReminders()

    const workspaceIds = reminders.map(r => r.workspaceId)
    const workspaces = await new WorkspaceService().find({id: In(workspaceIds)})
    const workspaceMap: { [id: string]: Workspace } = {}

    for (let workspace of workspaces) {
        workspaceMap[workspace.id] = workspace
    }

    const s = new ReminderService()
    const notificationsData: CreateNotificationData[] = []
    for (let reminder of reminders) {
        let nextNotifyAt: Date | null = null;
        const now = new Date().getTime(); // Get current time in milliseconds

        for (let notifyDate of reminder.notifyDates) {
            if (new Date(notifyDate).getTime() > now) { // Compare using getTime()
                nextNotifyAt = notifyDate; // Set the next notify date
                break; // Exit the loop once the next date is found
            }
        }
        const workspace = workspaceMap[reminder.workspaceId]

        const assignedToUserIds = reminder.assignedToUserIds.split(',')
        for (const userId of assignedToUserIds) {
            const notification: CreateNotificationData = {
                userId, workspaceId: reminder.workspaceId,
                options: {
                    title: reminder.title,
                    message: reminder.description,
                    link: appUrl(`/${workspace.domain}/reminders`)
                }
            }
            notificationsData.push(notification)
        }
        await s.update({id: reminder.id}, {nextNotifyAt})
    }
    const {result} = await CreateNotifications(notificationsData)

    const notificationsCreated: Notification[] = result.filter(r => !(r instanceof Error) && !!r.id).map(n => n as Notification)

    await redisReleaseLock(CRON_LOCK_KEYS.PROCESS_DUE_REMINDERS)

    return {
        remindersCount: reminders.length,
        notificationsTriggeredCount: notificationsData.length,
        notificationsCreatedCount: notificationsCreated.length,
        reminderIds: reminders.map(r => r.id),
        notificationIds: notificationsCreated.map(r => r.id),
    }

}



