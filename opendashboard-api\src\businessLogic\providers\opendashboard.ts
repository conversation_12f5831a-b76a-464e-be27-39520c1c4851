import {datePlusDays, dateToMySQL} from "opendb-app-db-utils/lib/methods/date";
import {UserHourlyEventService} from "../../service/userHourlyEvent";
import {DatabaseService} from "../../service/database";
import {Brackets, LessThanOrEqual, MoreThanOrEqual} from "typeorm";
import {UserService} from "../../service/user";
import {PageService} from "../../service/page";
import {WorkspaceService} from "../../service/workspace";
import {CampaignService} from "../../service/campaign";
import {CampaignEmailService} from "../../service/campaignEmailService";
import {CampaignAnalyticService} from "../../service/campaignAnalyticService";
import config, {isProduction} from "../../config";
import {RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {sendHTTPRequest} from "../../utility/http";
import {ErrorMessage} from "../../errors/AppError";
import {logInfo} from "../logtail";
import {User} from "../../entity/User";
import {Workspace} from "../../entity/Workspace";
import * as fs from "fs";
import {Parser} from "json2csv";
import {EmailUser, SendEmailWithContent} from "../email";

export interface SiteAnalyticData {
    env: string
    dateStr: string,
    fromDate: Date,
    toDate: Date,
    newUser: number
    activeUser: number
    usersCompletedSetup: number
    newWorkspace: number
    activeWorkspace: number
    newDatabase: number
    activeDatabase: number
    newPage: number
    activePage: number
    newCampaigns: number
    sentCampaigns: number
    emailSends: number
    emailOpens: number
    emailClicks: number
    emailBounces: number
    emailUnsubscribes: number
}

export const GenerateAnalytics = async (allTime = false) => {
    let fromDate = new Date("2024-01-01")
    if (!allTime) {
        fromDate = datePlusDays(new Date(), -1)
        fromDate.setHours(0, 0, 0)
    }
    let toDate = datePlusDays(new Date(), -1)
    toDate.setHours(23, 59, 59, 999)

    const analytics: SiteAnalyticData = {
        env: config.ENVIRONMENT,
        newDatabase: 0,
        activeDatabase: 0,
        newUser: 0,
        activeUser: 0,
        usersCompletedSetup: 0,
        newPage: 0,
        activePage: 0,
        newWorkspace: 0,
        activeWorkspace: 0,
        newCampaigns: 0,
        sentCampaigns: 0,
        emailBounces: 0,
        emailClicks: 0,
        emailOpens: 0,
        emailSends: 0,
        emailUnsubscribes: 0,
        fromDate,
        toDate,
        dateStr: dateToMySQL(new Date())
    }
    const uES = new UserHourlyEventService()
    const dS = new DatabaseService()
    const uS = new UserService()
    const pS = new PageService()
    const wS = new WorkspaceService()
    const cS = new CampaignService()
    const cES = new CampaignEmailService()
    const cAS = new CampaignAnalyticService()

    const brackets = new Brackets(qb => {
        qb.where({createdAt: MoreThanOrEqual(fromDate)});
        qb.andWhere({createdAt: LessThanOrEqual(toDate)})
    })

    {
        const qB = dS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets)
        const res = await qB.getRawMany()
        analytics.newDatabase = Number(res[0]['count'])

        const qB2 = uES.getRepository()
            .createQueryBuilder()
            .select("COUNT(DISTINCT(databaseId))", 'count')
            .where(brackets)
        const res2 = await qB2.getRawMany()
        analytics.activeDatabase = Number(res2[0]['count'])
    }

    {
        const qB = uS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets)
        const res = await qB.getRawMany()
        analytics.newUser = Number(res[0]['count'])

        const qB2 = uES.getRepository()
            .createQueryBuilder()
            .select("COUNT(DISTINCT(userId))", 'count')
            .where(brackets)

        const res2 = await qB2.getRawMany()
        analytics.activeUser = Number(res2[0]['count'])
    }
    {
        const qB = uS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets)
            .andWhere({isSetupCompleted: 1})
        const res = await qB.getRawMany()
        analytics.usersCompletedSetup = Number(res[0]['count'])
    }
    {
        const qB = pS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets)
        const res = await qB.getRawMany()
        analytics.newPage = Number(res[0]['count'])

        const qB2 = uES.getRepository()
            .createQueryBuilder()
            .select("COUNT(DISTINCT(pageId))", 'count')
            .where(brackets)

        const res2 = await qB2.getRawMany()
        analytics.activePage = Number(res2[0]['count'])
    }
    {
        const qB = wS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets)
        const res = await qB.getRawMany()
        analytics.newWorkspace = Number(res[0]['count'])

        const qB2 = uES.getRepository()
            .createQueryBuilder()
            .select("COUNT(DISTINCT(workspaceId))", 'count')
            .where(brackets)

        const res2 = await qB2.getRawMany()
        analytics.activeWorkspace = Number(res2[0]['count'])
    }
    {
        const qB = cS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets)
        const res = await qB.getRawMany()
        analytics.newCampaigns = Number(res[0]['count'])

        const brackets2 = new Brackets(qb => {
            qb.where({deliveredAt: MoreThanOrEqual(fromDate)});
            qb.andWhere({deliveredAt: LessThanOrEqual(toDate)})
        })

        const qB2 = cS.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(brackets2)
        const res2 = await qB2.getRawMany()
        analytics.sentCampaigns = Number(res2[0]['count'])
    }
    {
        const sentQB = cES.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(new Brackets(qb => {
                qb.where({sentAt: MoreThanOrEqual(fromDate)});
                qb.andWhere({sentAt: LessThanOrEqual(toDate)})
            }))
        analytics.emailSends = Number((await sentQB.getRawMany())[0]['count'])

        const bounceQB = cES.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(new Brackets(qb => {
                qb.where({bouncedAt: MoreThanOrEqual(fromDate)});
                qb.andWhere({bouncedAt: LessThanOrEqual(toDate)})
            }))
        analytics.emailBounces = Number((await bounceQB.getRawMany())[0]['count'])

        const unsubQB = cES.getRepository()
            .createQueryBuilder()
            .select('COUNT(id)', 'count')
            .where(new Brackets(qb => {
                qb.where({unsubscribedAt: MoreThanOrEqual(fromDate)});
                qb.andWhere({unsubscribedAt: LessThanOrEqual(toDate)})
            }))
        analytics.emailUnsubscribes = Number((await unsubQB.getRawMany())[0]['count'])

        const caQB = cAS.getRepository()
            .createQueryBuilder()
            .select('SUM(openCount)', 'opens')
            .addSelect('SUM(clickCount)', 'clicks')
            .where(new Brackets(qb => {
                qb.where({eventAt: MoreThanOrEqual(fromDate)});
                qb.andWhere({eventAt: LessThanOrEqual(toDate)})
            }))
        const res = await caQB.getRawMany()

        analytics.emailOpens = Number(res[0]['opens'])
        analytics.emailClicks = Number(res[0]['clicks'])
    }

    return {analytics}
}

export type SiteAnalyticDataColumnMapping = {
    [K in keyof SiteAnalyticData]: string;
};

const MetricsPush = Object.freeze({
    workspaceId: '4b338775-09ae-462e-b217-272da5a9f639',
    databaseId: '61992629-3859-4e3f-a099-dbe7e0ddcb2b',
    apiURL: 'https://api.opendashboard.app/v0'
})

const MetricsDestDataColMapping: SiteAnalyticDataColumnMapping = Object.freeze({
    env: '3b1b05f4-1c86-4697-ada9-83b42d240b7e',
    activeDatabase: "7c0059be-4489-442c-82a7-9a5f0ae04062",
    activePage: "612d2fcd-7687-40c3-a40e-df8c27bbfa76",
    activeUser: "f87af7ad-b7ce-4ad0-b556-cce52e3070d7",
    usersCompletedSetup: "fb2d4d90-9fbb-4a81-9440-9021de8b6be6",
    activeWorkspace: "d9043267-b606-44ed-8cfd-7f4689b73bb4",
    dateStr: "7c13c543-1441-46a9-acd1-9901696e4baa",
    emailBounces: "5054b9c7-0840-40c8-86f8-5b86370b00d2",
    emailClicks: "0d6f9faf-8a81-4139-86b6-1b5f74880cbd",
    emailOpens: "d068349e-5eeb-459f-9e32-460ac44cf2dc",
    emailSends: "a020850c-54db-4a40-8ab8-c404ab674d87",
    emailUnsubscribes: "c51ab4c8-5822-4305-906e-26886b4aedbb",
    fromDate: "51ad3641-e26a-42b8-b2af-e02175fce99e",
    newCampaigns: "4d3cb33f-84dd-4674-a72f-325c3dee6b3c",
    newDatabase: "b5052e94-fd6a-4ec3-a626-9651d826345c",
    newPage: "5f847570-3358-4b22-8283-432e0d9f9dea",
    newUser: "8c330b59-db22-4b39-9ca2-751181689afc",
    newWorkspace: "77ef7a35-bab5-404f-8b87-3c01bf1dce2b",
    sentCampaigns: "fa147a60-93a9-4433-8785-1f51c91429ae",
    toDate: "b498f595-2de4-4775-b42e-8dda8b82d4d1"
})

export const PushAnalytics = async (allTime = false, data: SiteAnalyticData) => {
    if (!config.OPENDASHBOARD.api_key) return

    const values: RecordValues = {}

    const keys = Object.keys(MetricsDestDataColMapping)
    for (const key of keys) {
        const colId = MetricsDestDataColMapping[key]
        const colValue = data[key]

        if (colId) values[colId] = Array.isArray(colValue) ? colValue : String(colValue)
    }
    const url = `${MetricsPush.apiURL}/workspaces/${MetricsPush.workspaceId}/databases/${MetricsPush.databaseId}/records`
    const headers = {
        apikey: config.OPENDASHBOARD.api_key
    }
    const body = {
        valuesList: [values],
        onDuplicate: "update",
    }

    const res = await sendHTTPRequest("post", url, headers, body)

    let error = ''
    if (!res.isSuccess) {
        error = res.data?.error || ErrorMessage.UnableToProcessRequest
    }

    console.log(body)
    // console.log(res)

    logInfo("Metrics push", {allTime, data, error, isSuccess: res.isSuccess})

    return {allTime, data, error, isSuccess: res.isSuccess}
}

export const GenerateAndPushAnalytics = async (allTime = false) => {
    const {analytics} = await GenerateAnalytics(allTime)

    const pushResponse = await PushAnalytics(allTime, analytics)

    await PushReport(allTime, analytics)
    return {pushResponse}
}


interface PushUserData {
    appId: string
    firstName: string,
    lastName: string,
    email: string,
    env: string[],
    source: string[]
    tags: string[]

}

export type PushUserDataColumnMapping = {
    [K in keyof PushUserData]: string;
};
const PushUsersDataColumnMapping: PushUserDataColumnMapping = {
    appId: "0ad159e4-1338-4a6c-9e20-7188b3702723",
    email: "email",
    firstName: "firstName",
    lastName: "lastName",
    source: "86aa6ade-5093-4b85-a94b-b2aa9dc7595c",
    env: '5de685b3-b237-46eb-9e3b-73ba6fbee317',
    tags: '54ceedac-681e-4fdf-aebe-21404cc8421f'
}

const UsersPush = Object.freeze({
    workspaceId: '4b338775-09ae-462e-b217-272da5a9f639',
    databaseId: 'e5651371-a52c-4263-b851-105f65537def',
    apiURL: 'https://api.opendashboard.app/v0'
})


export const PushNewUserToDatabase = async (users: User[]) => {
    if (!config.OPENDASHBOARD.api_key) return

    const envId = isProduction() ? '18716518-0039-4c6b-8819-53eb30197c03' : 'ecf6f12a-b71c-450c-a542-a88061c969fb';

    const pushData: PushUserData[] = users.map(u => {
        const data: PushUserData = {
            appId: u.id,
            email: u.email,
            env: [envId],
            firstName: u.firstName || '',
            lastName: u.lastName || '',
            source: ['5a4310fc-e62a-4a28-a266-7ef735ad6374'], // Api Trigger
            tags: ['29f7ca55-7117-42a5-ba41-7927f6df4436'] // Opendashboard App Signup
        }
        return data
    })

    const valuesList: RecordValues[] = []

    const keys = Object.keys(PushUsersDataColumnMapping)
    for (let data of pushData) {
        const values: RecordValues = {}
        for (const key of keys) {
            const colId = PushUsersDataColumnMapping[key]
            const colValue = data[key]

            if (colId) values[colId] = Array.isArray(colValue) ? colValue : String(colValue)
        }
        valuesList.push(values)
    }


    const url = `${UsersPush.apiURL}/workspaces/${UsersPush.workspaceId}/databases/${UsersPush.databaseId}/records`
    const headers = {
        apikey: config.OPENDASHBOARD.api_key
    }
    const body = {
        valuesList: valuesList,
        onDuplicate: "update",
    }

    const res = await sendHTTPRequest("post", url, headers, body)

    let error = ''
    if (!res.isSuccess) {
        error = res.data?.error || ErrorMessage.UnableToProcessRequest
    }
    logInfo(`Opendashboard: PushNewUserToDatabase ${error ? 'with error ' + error : ''}`, {body, error, isSuccess: res.isSuccess})
}

export const PushExistingUsersToDatabase = async () => {
    const uS = new UserService()
    const users = await uS.find({})

    await PushNewUserToDatabase(users)
}


export const PushReport = async (allTime = false, analytics: SiteAnalyticData, purgeFiles = true) => {
    // get top 50 workspaces, get their name and domain
    const uES = new UserHourlyEventService()
    const s = new UserService()
    const wS = new WorkspaceService()

    const fromDate = datePlusDays(new Date(), -1)
    let toDate = new Date()

    const dailyActiveUsers = analytics.activeUser
    const dailyNewUsers = analytics.newUser
    const dailyActiveWorkspaces = analytics.activeWorkspace
    const dailyNewWorkspaces = analytics.newWorkspace


    const weeklyActiveUsers = Number((await uES.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(userId))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -7))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        })).getRawMany())[0]['count'])

    const weeklyNewUsers = Number((await s.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(id))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -7))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        }))
        .andWhere({isSetupCompleted: 1})
        .getRawMany())[0]['count'])

    const weeklyActiveWorkspaces = Number((await uES.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(workspaceId))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -7))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        })).getRawMany())[0]['count'])

    const weeklyNewWorkspaces = Number((await wS.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(id))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -7))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        }))
        .andWhere({isSetupCompleted: 1})
        .getRawMany())[0]['count'])


    const monthlyActiveUsers = Number((await uES.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(userId))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -30))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        })).getRawMany())[0]['count'])

    const monthlyNewUsers = Number((await s.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(id))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -30))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        }))
        .andWhere({isSetupCompleted: 1})
        .getRawMany())[0]['count'])

    const monthlyActiveWorkspaces = Number((await uES.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(workspaceId))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -30))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        })).getRawMany())[0]['count'])

    const monthlyNewWorkspaces = Number((await wS.getRepository()
        .createQueryBuilder()
        .select("COUNT(DISTINCT(id))", 'count')
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -30))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        }))
        .andWhere({isSetupCompleted: 1})
        .getRawMany())[0]['count'])


    // const activeWorkspaces
    const qB = uES.getRepository().createQueryBuilder('uHE')
        .select("uHE.workspaceId", "workspaceId")
        .addSelect("w.name", "name")
        .addSelect("w.domain", "domain")
        .addSelect("COUNT(DISTINCT DATE(uHE.createdAt))", "activeDays")
        .addSelect("MIN(uHE.createdAt)", "firstActiveAt")
        .addSelect("MAX(uHE.createdAt)", "lastActiveAt")
        .leftJoin(Workspace, "w", "w.id = uHE.workspaceId")
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -30))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        }))
        .groupBy("uHE.workspaceId")
        .orderBy("activeDays", "DESC")
        .limit(50)

    const res = await qB.getRawMany()
    const activeWorkspaces = res.map(r => {
        return {
            workspaceId: r.workspaceId,
            name: r.name,
            domain: r.domain,
            activeDays: Number(r.activeDays),
            firstActiveAt: r.firstActiveAt,
            lastActiveAt: r.lastActiveAt
        }
    })

    // const activeUsers
    const qB2 = uES.getRepository().createQueryBuilder('uHE')
        .select("uHE.userId", "userId")
        .addSelect("u.firstName", "firstName")
        .addSelect("u.lastName", "lastName")
        .addSelect("u.email", "email")
        .addSelect("COUNT(DISTINCT DATE(uHE.createdAt))", "activeDays")
        .addSelect("MIN(uHE.createdAt)", "firstActiveAt")
        .addSelect("MAX(uHE.createdAt)", "lastActiveAt")
        // .addSelect("COUNT(1)/24", "activeDays")
        .leftJoin(User, "u", "u.id = uHE.userId")
        .where(allTime ? {} : new Brackets(qb => {
            qb.where({createdAt: MoreThanOrEqual(datePlusDays(toDate, -30))});
            qb.andWhere({createdAt: LessThanOrEqual(toDate)})
        }))
        .groupBy("uHE.userId")
        .orderBy("activeDays", "DESC")
        .limit(100)

    const res2 = await qB2.getRawMany()
    const activeUsers = res2.map(r => {
        return {
            userId: r.userId,
            firstName: r.firstName,
            lastName: r.lastName,
            email: r.email,
            activeDays: Number(r.activeDays),
            firstActiveAt: r.firstActiveAt,
            lastActiveAt: r.lastActiveAt
        }
    })

    const date = new Date()
    const dir = config.TEMP_FOLDER + `report-${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}/`
    fs.mkdirSync(dir, {recursive: true});

    const statsCSVPath = dir + 'stats.csv'
    writeToCSV([
            {value: 'date', label: "Date"},
            {value: 'fromDate', label: "From Date"},
            {value: 'toDate', label: "To Date"},
            {value: 'dailyActiveUsers', label: "Daily Active Users"},
            {value: 'dailyNewUsers', label: "Daily New Users"},
            {value: 'dailyActiveWorkspaces', label: "Daily Active Workspaces"},
            {value: 'dailyNewWorkspaces', label: "Daily New Workspaces"},
            {value: 'weeklyActiveUsers', label: "Weekly Active Users"},
            {value: 'weeklyNewUsers', label: "Weekly New Users"},
            {value: 'weeklyActiveWorkspaces', label: "Weekly Active Workspaces"},
            {value: 'weeklyNewWorkspaces', label: "Weekly New Workspaces"},
            {value: 'monthlyActiveUsers', label: "Monthly Active Users"},
            {value: 'monthlyNewUsers', label: "Monthly New Users"},
            {value: 'monthlyActiveWorkspaces', label: "Monthly Active Workspaces"},
            {value: 'monthlyNewWorkspaces', label: "Monthly New Workspaces"}
        ],
        [{
            date,
            fromDate,
            toDate,
            dailyActiveUsers,
            dailyNewUsers,
            dailyActiveWorkspaces,
            dailyNewWorkspaces,
            weeklyActiveUsers,
            weeklyNewUsers,
            weeklyActiveWorkspaces,
            weeklyNewWorkspaces,
            monthlyActiveUsers,
            monthlyNewUsers,
            monthlyActiveWorkspaces,
            monthlyNewWorkspaces
        }], statsCSVPath)

    const activeWorkspacesCSVPath = dir + 'activeWorkspaces.csv'
    writeToCSV([
            {value: 'workspaceId', label: "Workspace Id"},
            {value: 'name', label: "Name"},
            {value: 'domain', label: "Domain"},
            {value: 'activeDays', label: "Active Days"},
            {value: 'firstActiveAt', label: "First Active At"},
            {value: 'lastActiveAt', label: "Last Active At"}
        ],
        activeWorkspaces, activeWorkspacesCSVPath)

    const activeUsersCSVPath = dir + 'activeUsers.csv'
    writeToCSV([
            {value: 'userId', label: "User Id"},
            {value: 'firstName', label: "First Name"},
            {value: 'lastName', label: "Last Name"},
            {value: 'email', label: "Email"},
            {value: 'activeDays', label: "Active Days"},
            {value: 'firstActiveAt', label: "First Active At"},
            {value: 'lastActiveAt', label: "Last Active At"}
        ],
        activeUsers, activeUsersCSVPath)

    const subject = `Opendashboard Analytics between ${dateToMySQL(fromDate)} to ${dateToMySQL(date)}`
    const message = `
    <p>${subject}</p>
    <p>Env: ${config.ENVIRONMENT}</p>
    <p>All time: ${allTime ? 'Yes' : 'No'}</p>
    <p>From Date: ${dateToMySQL(fromDate)}</p>
    <p>To Date: ${dateToMySQL(date)}</p>
    <p>Active users and workspace is ${allTime ? 'All time' : 'Last 30 days'}</p>
    `
    const to: EmailUser = {
        email: "<EMAIL>", name: "Opendashboard Analytics"
    }
    const attachments = [statsCSVPath, activeWorkspacesCSVPath, activeUsersCSVPath]

    const messageId = await SendEmailWithContent(to, subject, message, undefined, " ", true, undefined, undefined, undefined, attachments)

    if (purgeFiles) {
        fs.unlinkSync(statsCSVPath)
        fs.unlinkSync(activeWorkspacesCSVPath)
        fs.unlinkSync(activeUsersCSVPath)
        fs.rmSync(dir, {recursive: true})
    }

    // const tmpDir = config.T
}


const writeToCSV = (fields: { value: string, label: string }[], csvData: any[], destinationPath: string) => {
    const json2csv = new Parser({fields: fields})
    const csv = json2csv.parse(csvData)
    fs.writeFileSync(destinationPath, csv)
}
