import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOnUser1735124307437 implements MigrationInterface {
    name = 'AddOnUser1735124307437'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription\` ADD \`addOnUsers\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription\` DROP COLUMN \`addOnUsers\``);
    }

}
