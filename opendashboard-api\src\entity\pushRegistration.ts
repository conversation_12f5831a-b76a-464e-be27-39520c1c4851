import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

export enum PushProvider {
    FCM = 'FCM'
}

@Entity()
@Index(["userId", "sessionId"], {unique: true})
export class PushRegistration {

    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    sessionId: string

    @Column({type: 'text', nullable: false})
    providerToken: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    provider: PushProvider

    @Index()
    @Column({type: 'timestamp', nullable: true})
    refreshedAt: Date;

    @Index()
    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @Index()
    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

}