import {Entity} from "typeorm/decorator/entity/Entity";
import {PrimaryGeneratedColumn} from "typeorm/decorator/columns/PrimaryGeneratedColumn";
import {Column, CreateDateColumn, DeleteDateColumn, Index, UpdateDateColumn} from "typeorm";
import {DashboardViewDefinition, TableViewDefinition, ViewDefinition,CalendarViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";

@Entity()
@Index(["pageId", "slug"], {unique: true})
export class View {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Column({ type: 'varchar', nullable: true })
    description: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    pageId: string

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateReleaseId: number

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateInstallId: number

    @Column({type: 'varchar', nullable: true})
    slug: string

    @Column({type: 'varchar', nullable: false})
    type: ViewType

    @Column({type: 'json', nullable: true})
    definition: ViewDefinition | TableViewDefinition | DashboardViewDefinition | CalendarViewDefinition

    @Column({default: false, type: 'boolean'})
    isPublished: boolean

    @Column({default: false, type: 'boolean'})
    allowSearchEngineIndex: boolean

    @Column({type: 'timestamp', nullable: true})
    publishedAt: Date

    @Index()
    @Column({type: 'varchar', nullable: true, length: 36})
    createdById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}
