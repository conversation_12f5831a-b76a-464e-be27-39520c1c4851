import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {getSharedView, processFormResponse, processFormUpload} from "../../businessLogic/shared";

export class SharedController {

    async getView(request: Request, response: Response, next: NextFunction) {
        const res = await getSharedView(request.params.viewId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                ...res
            },
        }
        return response.json(
            responseData
        )
    }

    async submitFormResponses(request: Request, response: Response, next: NextFunction) {
        const res = await processFormResponse(request.params.viewId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async uploadFormFile(request: Request, response: Response, next: NextFunction) {
        const {upload} = await processFormUpload(request.params.viewId, request)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                upload
            },
        }
        return response.json(
            responseData
        )
    }


}