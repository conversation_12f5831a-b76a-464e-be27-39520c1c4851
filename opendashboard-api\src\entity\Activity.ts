import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm";
import {DatabaseColumn, DatabaseFieldDataType, RecordValues} from "opendb-app-db-utils/lib/typings/db";


export enum ActivityObjectType {
    Database = "database",
    Record = "record",
    Page = "page"
}

export enum ActivityType {
    ItemCreated = "item_created",
    ItemUpdated = "item_updated",
    ItemPublished = "item_published",
    ItemDeleted = "item_deleted",
    DatabaseColumnCreated = "db_column_created",
    DatabaseColumnUpdated = "db_column_updated",
    DatabaseColumnDeleted = "db_column_deleted",
    DatabaseColumnMadeUnique = "db_column_made_unique",
    DatabaseColumnRemovedUnique = "db_column_removed_unique",
    DatabaseColumnHidden = "db_column_hidden",
    DatabaseColumnShown = "db_column_shown",
    DatabaseColumnsReOrdered = "db_columns_reordered",
    SelectOptionsReOrdered = "select_options_reordered",
    CommentAdded = "comment_added",
    ValuesUpdated = "values_updated",
}

export enum ChangeType {
    NoChange = "no_change",
    FullProps = "full_props",
    ListOrder = "list_order",
    RecordValues = "record_values",
    CommentBody = "comment_body",
}

export interface DatabaseColumnChangeValue {
    element: DatabaseColumn,
    afterId: string
    beforeId: string
}

export interface ChangeData {
    changeType: ChangeType
    oldValue: boolean | number | string | object | DatabaseColumnChangeValue | Partial<DatabaseColumnChangeValue>
    newValue: boolean | number | string | object | DatabaseColumnChangeValue | Partial<DatabaseColumnChangeValue>
    meta?: object
}

export interface DatabaseColumnChangeData extends ChangeData {
    changeType: ChangeType.FullProps
    oldValue: { element: DatabaseColumn, afterId?: string, beforeId?: string } | null
    newValue: { element: DatabaseColumn, afterId?: string, beforeId?: string } | null
}

export interface ListOrderChangeData extends ChangeData {
    changeType: ChangeType.ListOrder
    oldValue: string[]
    newValue: string[]
}

export interface RecordValuesChangeData extends ChangeData {
    changeType: ChangeType.RecordValues
    oldValue: RecordValues
    newValue: RecordValues
    meta: {
        fields: {
            [key:string]: DatabaseFieldDataType
        }
    }
}

export interface CommentChangeData extends ChangeData {
    changeType: ChangeType.CommentBody
    oldValue: null
    newValue: {
        text: string
        attachments: Attachment[]
    }
}

export interface Attachment {
    id: string
    url: string
}


/*
*
* Scenario 1: Create database: {objectType: "database", activityType: "item_created", databaseId:"", changeData: {changeType: full_props, oldValue: null, newValue: } }
* Scenario 2: Update database: {objectType: "database", activityType: "item_updated", databaseId:"", changeData: {changeType: partial_props, oldValue: , newValue}}
* Scenario 3: Delete database: {objectType: "database", activityType: "item_deleted", databaseId:"", changeData: {changeType: full_props, oldValue: , newValue: null}}
* Scenario 4: Add database column: {objectType: "database_column", activityType: "item_created", databaseId:"", changeData: {changeType: full_props, oldValue: null, newValue: {}}}
* Scenario 5: Update database column: {objectType: "database_column", activityType: "item_updated", databaseId:"", changeData: {changeType: full_props, oldValue: {}, newValue: {}}}
* Scenario 6: Delete database column: {objectType: "database_column", activityType: "item_deleted", databaseId:"", changeData: {changeType: full_props, oldValue: {}, newValue: null}}
* Scenario 7: Reorder database columns: {objectType: "database_column", activityType: "db_columns_reordered", databaseId:"", changeData: {changeType: list_order, oldValue: null, newValue: {}}}
* Scenario 8: Create record: {objectType: "record", activityType: "item_created", databaseId:"", pageId:"", changeData: {changeType: record_values, oldValue: null, newValue: }}
* Scenario 9: Update record: {objectType: "record", activityType: "item_updated", databaseId:"", pageId:"", changeData: {changeType: record_values, oldValue: null, newValue: }}
* Scenario 10: Update record field: {objectType: "record", activityType: "item_updated", databaseId:"", pageId:"", changeData: {changeType: record_field_value, oldValue: {}, newValue: {} , meta: {fieldDataType: select}}
* Scenario 11: Add comment: {objectType: "record", activityType: "comment_added", databaseId:"", pageId:"", changeData: {changeType: comment_body, oldValue: null, newValue: {}}
*
*
 */

@Entity()
export class Activity {

    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    databaseId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    pageId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    recordId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    objectType: ActivityObjectType

    @Index()
    @Column({type: 'varchar', nullable: true})
    objectId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    parentId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    activityType: ActivityType

    @Column({type: 'json', nullable: true})
    changeData: ChangeData

    @Index()
    @Column({type: 'bool', default: false})
    isResolved: boolean

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @Index()
    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}