import {MigrationInterface, QueryRunner} from "typeorm";

export class PushRegistration1736900902398 implements MigrationInterface {
    name = 'PushRegistration1736900902398'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`template_release\` ADD \`meta\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`template_install\` ADD \`meta\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_23d38652d429bcebf84487bcf5\` ON \`push_registration\` (\`userId\`, \`sessionId\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_23d38652d429bcebf84487bcf5\` ON \`push_registration\``);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`template_install\` DROP COLUMN \`meta\``);
        await queryRunner.query(`ALTER TABLE \`template_release\` DROP COLUMN \`meta\``);
    }

}
