import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"

export enum TokenType {
    EmailLogin = 1,
    JWT = 2,
    EmailVerification = 3,
    EmailSenderVerification = 4,
    ApiToken = 5,
    WorkflowInstanceApproval = 5
}

export enum TokenStatus {
    Valid = 1, Expired = 2
}

@Entity()
export class Token {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: "varchar", nullable: true})
    name: string

    @Index()
    @Column({type: 'int'})
    purpose: TokenType

    @Index()
    @Column({type: 'tinyint', default: TokenStatus.Valid})
    status: TokenStatus

    @Index()
    @Column({type: "varchar"})
    userId: string

    @Index()
    @Column({type: "varchar", nullable: true})
    workspaceId: string

    @Index()
    @Column({type: "varchar", select: false})
    token: string

    @Column({type: "varchar", nullable: true})
    clientName: string

    @Column({type: 'timestamp', nullable: true})
    lastActiveAt: Date

    @Column({type: 'timestamp', nullable: true})
    expiresAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true})
    meta: object

}

