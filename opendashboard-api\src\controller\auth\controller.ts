import {NextFunction, Request, Response} from "express"
import {ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {AuthProvider, ExchangeMagicTokenForJWT, FinalizeProviderLogin, InitializeSignIn, ProviderAuthState, validProviders} from "../../businessLogic/auth";
import {CompleteEmailVerification} from "../../businessLogic/account";
import {appUrl} from "../../config";
import {CompleteSenderEmailVerification} from "../../businessLogic/workspace";
import {BadRequestError} from "../../errors/AppError";
import * as passport from "passport";
import {Profile} from "passport-facebook";

export class AuthController {

    async signIn(request: Request, response: Response, next: NextFunction) {
        const {tokenId} = await InitializeSignIn(request.body.email)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {tokenId}
        }
        return response.json(
            responseData
        )
    }

    async exchangeToken(request: Request, response: Response, next: NextFunction) {
        const {token, user} = await ExchangeMagicTokenForJWT(request.body.hash)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {token, user}
        }
        return response.json(
            responseData
        )
    }

    async verifyEmail(request: Request, response: Response, next: NextFunction) {
        const rawHash: string = <string>request.query.hash
        if (!rawHash) {
            return response.redirect(appUrl());
        }
        const hash = decodeURIComponent(rawHash)

        try {
            await CompleteEmailVerification(hash)
        } catch (e) {
            console.log("Complete Email Verification failed:", e)
        }
        return response.redirect(appUrl());

    }

    async verifySender(request: Request, response: Response, next: NextFunction) {
        const rawHash: string = <string>request.query.hash
        if (!rawHash) {
            return response.redirect(appUrl());
        }
        const hash = decodeURIComponent(rawHash)

        try {
            await CompleteSenderEmailVerification(hash)
        } catch (e) {
            console.log("Complete Email Verification failed:", e)
        }
        return response.redirect(appUrl());

    }

    async providerAuthenticate(request: Request, response: Response, next: NextFunction) {
        const provider = request.params.provider as AuthProvider;

        if (!validProviders.includes(provider)) {
            throw new BadRequestError(`Invalid provider '${provider}'`)
        }
        const stateParams: ProviderAuthState = {}
        const state = JSON.stringify(stateParams)
        if (provider == AuthProvider.Google) {
            passport.authenticate(
                provider,
                {
                    scope: ['email', 'profile'],
                    state
                },
                (err, user, info) => {
                    return next(err);
                })(request, response, next)
            return;
        }
        throw new BadRequestError(`Unavailable provider: ${provider}`)
    }

    async providerCallback(request: Request, response: Response, next: NextFunction) {
        const provider = request.params.provider;

        if (!validProviders.includes(provider as AuthProvider)) {
            throw new BadRequestError(`Invalid provider '${provider}'`)
        }
        const stateQs = <string>request.query.state
        let stateParams: ProviderAuthState
        try {
            stateParams = JSON.parse(stateQs)
        } catch (e) {

        }
        if (provider == 'google') {
            passport.authenticate(provider, async (err, profile: Profile, info) => {
                const email = profile?.emails?.[0]?.value;

                const nextUrl = await FinalizeProviderLogin(provider as AuthProvider, email);

                response.redirect(nextUrl);
            })(request, response, next);
            return;
        }
        throw new BadRequestError(`Unavailable provider: ${provider}`)
    }
}



