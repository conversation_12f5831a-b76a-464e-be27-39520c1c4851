import {Templates<PERSON><PERSON>roller} from "./controller";
import {Routes} from "../../routes";
import {verifyJWT, verifyJWTIfPossible} from "../../middleware/verifyJWT";

export const templatesRoutes: Routes = {
    basePath: '/templates',
    // middleware: [verifyApiKey],
    routes: {
        '/': {
            get: {
                controller: TemplatesController,
                action: "getTemplates"
            }
        },
        '/categories': {
            get: {
                controller: TemplatesController,
                action: "getCategories"
            }
        },
        '/purchases': {
            get: {
                controller: TemplatesController,
                action: "getPurchases",
                middleware: [verifyJWT]
            },
        },
        '/creators/:domain': {
            get: {
                controller: Templates<PERSON>ontroller,
                action: "getCreator"
            }
        },
        '/:id': {
            get: {
                controller: TemplatesController,
                action: "getTemplate",
                middleware: [verifyJWTIfPossible]
            }
        },
        '/:id/releases/:releaseId/resources': {
            get: {
                controller: TemplatesController,
                action: "getTemplateReleaseResources"
            }
        },
        '/:id/discussions': {
            get: {
                controller: Templates<PERSON>ontroller,
                action: "getDiscussions",
                middleware: [verifyJWTIfPossible]
            },
            post: {
                controller: TemplatesController,
                action: "postDiscussion",
                middleware: [verifyJWT]
            },
        },
        '/:id/purchases': {
            post: {
                controller: TemplatesController,
                action: "purchaseTemplate",
                middleware: [verifyJWT]
            }
        },
        '/:id/purchases/apply-discount': {
            post: {
                controller: TemplatesController,
                action: "applyDiscount"
            }
        },
        '/:id/purchases/:purchaseId/verify': {
            get: {
                controller: TemplatesController,
                action: "verifyNGNTemplatePurchase"
            },
        },
        '/:id/install/options': {
            get: {
                controller: TemplatesController,
                action: "installOptions",
                middleware: [verifyJWT]
            },
        },
        '/:id/install': {
            post: {
                controller: TemplatesController,
                action: "startInstall",
                middleware: [verifyJWT]
            },
        },
        '/:id/install-via-code': {
            post: {
                controller: TemplatesController,
                action: "startInstallViaCode",
                middleware: [verifyJWT]
            },
        },

    }
};


