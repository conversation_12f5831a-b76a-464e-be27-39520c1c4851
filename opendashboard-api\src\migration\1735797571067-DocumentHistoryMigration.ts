import { MigrationInterface, QueryRunner } from "typeorm";

export class DocumentHistoryMigration1735797571067 implements MigrationInterface {
    name = 'DocumentHistoryMigration1735797571067'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`document_history\` (\`id\` varchar(36) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`documentId\` varchar(255) NULL, \`recordId\` varchar(255) NULL, \`name\` varchar(255) NULL, \`contentJSON\` json NULL, \`contentText\` text NULL, \`createdById\` varchar(255) NULL, \`updatedById\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_ecec70bcdc43d46c6da9f34acf\` (\`workspaceId\`), INDEX \`IDX_4a9486398505240b92812660f7\` (\`documentId\`), INDEX \`IDX_25a1553e389e600b3edf03dc6e\` (\`recordId\`), INDEX \`IDX_f806eb0baf863b01a9bbcdc531\` (\`name\`), FULLTEXT INDEX \`IDX_1367daa451eeb1636049c627ad\` (\`contentText\`), INDEX \`IDX_7bdf4d13ba8a6d691471e51992\` (\`createdById\`), INDEX \`IDX_b26bb39813462a79bebe6d1d58\` (\`updatedById\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`DROP INDEX \`IDX_b26bb39813462a79bebe6d1d58\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_7bdf4d13ba8a6d691471e51992\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_1367daa451eeb1636049c627ad\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_f806eb0baf863b01a9bbcdc531\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_25a1553e389e600b3edf03dc6e\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_4a9486398505240b92812660f7\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_ecec70bcdc43d46c6da9f34acf\` ON \`document_history\``);
        await queryRunner.query(`DROP TABLE \`document_history\``);
    }

}
