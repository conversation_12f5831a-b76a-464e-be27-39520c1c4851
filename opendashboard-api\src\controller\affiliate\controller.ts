import {NextFunction, Request, Response} from "express";
import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {CreateAffiliate, GetAffiliate, GetAffiliatePayouts, GetAffiliateStats, UpdateAffiliate} from "../../businessLogic/affiliate";


export class AffiliateController {

    async create(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {affiliate} = await CreateAffiliate(authInfo.userId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                affiliate
            },
        }
        return response.json(responseData)
    }

    async get(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {affiliates} = await GetAffiliate(authInfo.userId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                affiliates
            },
        }
        return response.json(responseData)
    }

    async patch(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {affiliate} = await UpdateAffiliate(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                affiliate
            },
        }
        return response.json(responseData)
    }
    async getPayouts(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {payouts} = await GetAffiliatePayouts(authInfo.userId, request.query)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                payouts
            },
        }
        return response.json(responseData)
    }

    async getStats(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {stats} = await GetAffiliateStats(authInfo.userId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                stats
            },
        }
        return response.json(responseData)
    }


}