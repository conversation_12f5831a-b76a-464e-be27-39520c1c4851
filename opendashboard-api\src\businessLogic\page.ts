import {Page} from "../entity/Page";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, UnauthorizedError} from "../errors/AppError";
import {broadcastNewWorkspaceMembers, GetMyWorkspace, HasPermission, MyWorkspace, WorkspacePermission} from "./workspace";
import {CreatePageData, PageService} from "../service/page";
import {View} from "../entity/View";
import {AccessLevel, Visibility} from "../entity/common";
import {CreatePermission, PagePermissionService} from "../service/pagePermission";
import {WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {CreateViewData, ViewService} from "../service/view";
import {In, IsNull, Not} from "typeorm";
import {PagePermission} from "../entity/PagePermission";
import {arrayDeDuplicate, arrayDiff, generateUUID, removeArrayItem} from "opendb-app-db-utils/lib";
import {validateEmail} from "../utility/validator";
import {CreateUser, UserService} from "../service/user";
import {CreateWorkspaceMember, WorkspaceMemberService} from "../service/workspaceMember";
import {Workspace} from "../entity/Workspace";
import {appUrl} from "../config";
import {EmailUser, SendEmailWithContent} from "./email";
import {DashboardElementType, DashboardViewDefinition, DocumentViewDefinition, InfoboxElement, LineChartElement, TableViewDefinition, ViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {DatabaseService} from "../service/database";

import {RecalculateWorkspaceStats} from "./stats";
import {broadcastDatabaseCreated, broadcastPageCreated, broadcastPageDeleted, broadcastPagePermissionAdded, broadcastPagePermissionDeleted, broadcastPagePermissionUpdated, broadcastPageUpdated, broadcastPageViewAdded} from "../socketio/workspace";
import {RecordService} from "../service/record";
import {Database} from "../entity/Database";
import {DocumentService} from "../service/document";
import {PermissibleDatabaseWithPermissions} from "./database";
import {GettingStartedDoc} from "./data/starter";
import {datePlusHours} from "opendb-app-db-utils/lib/methods/date";


export interface PageRequestData extends Pick<Page, "name" | 'icon'> {
    visibility?: Visibility
}

export interface PermissiblePage {
    page: Page
    views: View[]
    accessLevel: AccessLevel
}

export const CreatePage = async (userId: string, workspaceId: string, data: PageRequestData) => {
    const name = data.name
    if (!name) {
        throw new RequiredParameterError("name")
    }
    const visibility = data.visibility || Visibility.Open
    if (![Visibility.Open, Visibility.Private].includes(visibility)) {
        throw new InvalidParameterError("visibility")
    }
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.CreatePage)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const pageData: CreatePageData = {
        name, workspaceId,
        createdById: userId,
        ownerId: userId,
        icon: data.icon || null,
        visibility
    }
    const service = new PageService()
    const page = await service.createPage(pageData)

    const pg: PermissiblePageWithPermissions = {
        page,
        views: [],
        accessLevel: AccessLevel.Full,
        permissions: []
    }

    broadcastPageCreated(workspaceId, pg)

    return {
        page: pg,
    }
}

export interface UpdatePageRequestData extends Pick<Page, "id" | "name" | "visibility" | "accessLevel" | 'icon'> {
}

export const UpdatePage = async (userId: string, workspaceId: string, data: UpdatePageRequestData) => {
    const name = (data.name || "").trim()
    const {visibility, accessLevel, id} = data

    if (!name) {
        throw new RequiredParameterError("name")
    }
    if (!visibility) {
        throw new RequiredParameterError("visibility")
    }
    if (!accessLevel) {
        throw new RequiredParameterError("accessLevel")
    }
    if (![AccessLevel.Edit, AccessLevel.Full, AccessLevel.View].includes(accessLevel)) {
        throw new InvalidParameterError("accessLevel")
    }
    if (![Visibility.Open, Visibility.Private].includes(visibility)) {
        throw new InvalidParameterError("visibility")
    }
    const resolve = await resolvePageAccessLevel(userId, workspaceId, id)

    if (!resolve || (resolve.accessLevel !== AccessLevel.Full && resolve.accessLevel !== AccessLevel.Edit)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const update: Partial<Page> = {
        name,
        visibility,
        accessLevel,
        icon: data.icon || null
    }
    const service = new PageService()
    await service.update({id}, update)
    resolve.page = {...resolve.page, ...update}

    if (resolve.page.databaseId) {
        const dS = new DatabaseService()
        await dS.update({id: resolve.page.databaseId, workspaceId}, {name: name})
    }
    broadcastPageUpdated(workspaceId, id, resolve.page.databaseId, update)

    return resolve
}

export interface DeletePageRequestData {
    id: string
}

export const DeletePage = async (userId: string, workspaceId: string, data: DeletePageRequestData) => {
    const {id} = data
    const resolve = await resolvePageAccessLevel(userId, workspaceId, id)

    if (!resolve || (resolve.accessLevel !== AccessLevel.Full)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new PageService()
    await service.remove({id})

    const viewService = new ViewService()
    await viewService.remove({pageId: id})

    if (resolve.page.databaseId) {
        const dS = new DatabaseService()
        await dS.remove({id: resolve.page.databaseId, workspaceId})
    }
    broadcastPageDeleted(workspaceId, resolve.page.id, resolve.page.databaseId)
    return true
}

export const GetMyPages = async (userId: string, workspaceId: string) => {
    return getMyPages(userId, workspaceId, "page")
}

export const getMyPages = async (userId: string, workspaceId: string, fetchFor: "database" | "page", permission: 'shared' | 'mine' = 'mine') => {
    return fetchWorkspacePages(userId, workspaceId, fetchFor, permission)
}

const fetchWorkspacePages = async (userId: string, workspaceId: string, fetchFor: "database" | "page", permission: 'shared' | 'mine') => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const isCollaborator = member.workspaceMember.role === WorkspaceMemberRole.Collaborator
    if (isCollaborator && permission === 'shared') throw new UnauthorizedError(ErrorMessage.UnableToFetchEntity)

    // get pages that are 1. open(if I'm not a collaborator)  2. owned by me 3. I have access to
    const service = new PageService()
    const s2 = new PagePermissionService();
    const myPermissions = await s2.find({
        userId, workspaceId
    })
    const pagePermissionMap: {
        [key: string]: PagePermission;
    } = {}
    for (const permission of myPermissions) {
        pagePermissionMap[permission.pageId] = permission
    }
    const givenIds = Object.keys(pagePermissionMap)

    const accessiblePages = permission === 'mine' ? await service.findUserAccessiblePages(userId, workspaceId, givenIds, isCollaborator, fetchFor === 'database') :
                            await service.find({workspaceId, visibility: Visibility.Open, databaseId: fetchFor === 'database' ? Not(IsNull()) : IsNull()}, {createdAt: "ASC"})

    const pageIdsMap: { [key: string]: Page } = {}
    for (const page of accessiblePages) {
        pageIdsMap[page.id] = page
    }
    const pageIds = Object.keys(pageIdsMap)

    // load the views
    const s3 = new ViewService()
    const views = await s3.find({
        pageId: In(pageIds)
    })
    const pageIdViewsMap: {
        [key: string]: View[];
    } = {}
    for (const view of views) {
        pageIdViewsMap[view.pageId] = pageIdViewsMap[view.pageId] || []
        pageIdViewsMap[view.pageId].push(view)
    }
    // load the permissions for the pages
    const pagePermissions = await s2.find({
        workspaceId, pageId: In(pageIds)
    })
    const pagePermissionsMap: {
        [key: string]: PagePermission[];
    } = {}
    for (const pp of pagePermissions) {
        pagePermissionsMap[pp.pageId] = pagePermissionsMap[pp.pageId] || []
        pagePermissionsMap[pp.pageId].push(pp)
    }

    const pages: PermissiblePageWithPermissions[] = []
    // put it all together
    for (const id of pageIds) {
        const page = pageIdsMap[id]
        const views = pageIdViewsMap[id] || []
        const accessLevels = []
        if (page.ownerId === userId) accessLevels.push(AccessLevel.Full)
        else if (!isCollaborator && page.visibility === Visibility.Open) accessLevels.push(page.accessLevel)
        if (pagePermissionMap[id]) accessLevels.push(pagePermissionMap[id].accessLevel)
        const accessLevel = computeBestAccessLevel(accessLevels)
        const permissions = pagePermissionsMap[id] || []

        const p: PermissiblePageWithPermissions = {
            page,
            views,
            accessLevel,
            permissions
        }
        pages.push(p)
    }

    return {
        pages
    }

}

export interface PermissiblePageWithPermissions extends PermissiblePage {
    permissions: PagePermission[]
}

export const GetMyPage = async (userId: string, workspaceId: string, pageId: string) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)

    if (!resolve) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s3 = new ViewService()
    const views = await s3.find({
        pageId: pageId
    })

    const page: PermissiblePageWithPermissions = {
        page: resolve.page,
        views: views,
        accessLevel: resolve.accessLevel,
        permissions: resolve.permissions
    }
    return {
        page,
    }
}

export const DuplicateMyPage = async (userId: string, workspaceId: string, pageId: string) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)

    if (!resolve) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const pS = new PageService()
    const pS2 = new PagePermissionService()

    // duplicate database, records(database id will be the new one), page, permissions, views(database id of the views), documents
    // resolve.page.databaseId
    const s3 = new ViewService()
    const views = await s3.find({
        pageId: pageId
    })
    let newDbId = ''
    let newDatabase;
    if (resolve.page.databaseId) {
        // duplicate database
        const dS = new DatabaseService()
        const rS = new RecordService()

        const database = await dS.findOne({workspaceId, id: resolve.page.databaseId})

        const db2Data: Database = {...database}
        db2Data.name = database + ' copy'
        db2Data.id = generateUUID()
        db2Data.createdById = userId
        db2Data.createdAt = db2Data.updatedAt = new Date()

        await dS.insert(db2Data)
        newDbId = db2Data.id
        newDatabase = db2Data

        // duplicate records
        await rS.duplicateRecords(database.id, {
            createdAt: new Date(),
            updatedAt: new Date(),
            createdById: userId,
            updatedById: userId,
            databaseId: newDbId
        })

        console.log("Record duplicated")
    }
    // duplicate page
    const newPage = {...resolve.page}
    newPage.databaseId = newDbId
    newPage.id = generateUUID()
    newPage.name += ' copy'
    await pS.insert(newPage)

    // duplicate permissions
    await pS2.insertViaSelect({pageId: resolve.page.id}, {pageId: newPage.id})

    // duplicate views
    const dupViews: View[] = []
    const viewIdOldToNew: { [id: string]: string } = {}

    const docViewIds: string[] = []

    for (let view of views) {
        if (view.type === ViewType.Document) docViewIds.push(view.id)

        const dupView = await cloneView(newPage, view, newPage.id, newDbId, false, false)
        viewIdOldToNew[view.id] = dupView.id
        dupViews.push(dupView)

    }
    await s3.batchAdd(dupViews)

    // duplicate document
    if (docViewIds.length > 0) {
        const dS = new DocumentService()
        for (let viewId of docViewIds) {
            const newId = viewIdOldToNew[viewId]
            await dS.insertViaSelect({viewId}, {viewId: newId})
        }
    }

    const duplicate: PermissiblePageWithPermissions = {
        page: newPage,
        views: dupViews,
        accessLevel: AccessLevel.Full,
        permissions: resolve.permissions.map(p => ({...p, pageId: newPage.id}))
    }

    if (newDatabase) {
        const database = newDatabase
        const page = resolve.page

        const db: PermissibleDatabaseWithPermissions = {
            database,
            page,
            views: dupViews,
            accessLevel: AccessLevel.Full,
            permissions: duplicate.permissions
        }
        broadcastDatabaseCreated(workspaceId, db)
    } else {
        broadcastPageCreated(workspaceId, duplicate)
    }
    return {
        page: duplicate
    }
}

const cloneView = async (page: Page, view: View, newPageId = '', newDbId = '', rename = false, autoSave = true) => {

    const dupView = {...view}
    dupView.id = generateUUID()

    if (newPageId) dupView.pageId = newPageId

    if (rename) {
        dupView.name += ' copy'
    }

    switch (dupView.type) {
        case ViewType.Table:
        case ViewType.Board:
        case ViewType.SummaryTable:
        case ViewType.ListView:
        case ViewType.Form:
            const vDef = dupView.definition as TableViewDefinition
            if (newDbId && vDef.databaseId) vDef.databaseId = newDbId

            break;
        case ViewType.Document:
            break;
        case ViewType.Dashboard:
            const dDef = dupView.definition as DashboardViewDefinition
            if (newDbId) {
                for (let ele of Object.values(dDef.definition.elementMap)) {
                    switch (ele.type) {
                        case DashboardElementType.BarChart:
                        case DashboardElementType.LineChart:
                        case DashboardElementType.PieChart:
                            const chartDef = ele as LineChartElement
                            chartDef.recordsResolve.databaseId = newDbId
                            break
                        case DashboardElementType.Infobox:
                            const infoDef = ele as InfoboxElement
                            infoDef.valueResolve.databaseId = newDbId
                            break;
                    }
                }
            }
            break;
    }

    if (autoSave) {
        const s3 = new ViewService()
        await s3.insert(dupView)

        broadcastPageViewAdded(page.workspaceId, page, dupView)
    }
    return dupView
}

export const DuplicateView = async (userId: string, workspaceId: string, pageId: string, viewId: string) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const newView = await cloneView(resolve.page, view, '', '', true, true)

    return {
        page: resolve.page, view: newView
    }
}

export interface AddPermissionRequestBody {
    emails: string[]
    accessLevel: AccessLevel
}

export const InviteToPage = async (userId: string, workspaceId: string, pageId: string, data: AddPermissionRequestBody) => {
    if (!data.emails) {
        throw new RequiredParameterError("emails");
    }
    if (!Array.isArray(data.emails)) {
        throw new InvalidParameterError("emails is invalid");
    }
    const emails = arrayDeDuplicate(data.emails.map(e => e.trim()))

    for (const email of emails) {
        if (!validateEmail(email)) throw new InvalidParameterError(`Email "${email}" is invalid`);
    }

    const accessLevel = data.accessLevel
    if (!accessLevel) {
        throw new RequiredParameterError("accessLevel")
    }
    if (![AccessLevel.Edit, AccessLevel.Full, AccessLevel.View].includes(accessLevel)) {
        throw new InvalidParameterError("accessLevel")
    }
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const userService = new UserService();
    const users = await userService.find({
        email: In(emails)
    })
    const registeredEmails = users.map(u => u.email)
    const unregistered = arrayDiff(emails, registeredEmails)

    // create the users that don't exist
    const toCreate: CreateUser[] = []
    for (const email of unregistered) {
        const uData: CreateUser = {
            firstName: "",
            lastName: "",
            email: email,
        }
        toCreate.push(uData)
    }
    const createdUsers = await userService.batchCreate(toCreate)

    users.push(...createdUsers)

    const usersMap = {}
    for (const user of users) {
        usersMap[user.id] = user
    }

    const memberService = new WorkspaceMemberService()
    const members = await memberService.find({workspaceId})

    // for users that are not members of the workspace, add them as collaborators
    const userIds: string[] = Object.keys(usersMap)
    const nonMemberIds: string[] = arrayDiff(userIds, members.map(m => m.userId))

    let collaboratorsCount = 0;
    for (const member of members) {
        if (member.role === WorkspaceMemberRole.Collaborator) collaboratorsCount++
    }
    collaboratorsCount += nonMemberIds.length
    if (collaboratorsCount > resolve.member.billingCycle.collaboratorsQuota) {
        throw new BadRequestError(ErrorMessage.MaximumCollaboratorLimitReached)
    }

    const toAdd: CreateWorkspaceMember[] = []
    for (const id of nonMemberIds) {
        toAdd.push({
            workspaceId,
            userId: id,
            role: WorkspaceMemberRole.Collaborator,
            invitedById: userId
        })
    }
    const addedMembers = await memberService.batchAdd(toAdd)
    members.push(...addedMembers)

    const service = new PagePermissionService()
    const currentPermissions = await service.find({workspaceId, pageId: pageId})

    // give people that don't have permission accessLevel permission and email them
    const withPermUserIds: string[] = currentPermissions.map(p => p.userId)
    const noPermUserIds: string[] = arrayDiff(userIds, withPermUserIds)

    const permsToAdd: CreatePermission[] = []
    for (const id of noPermUserIds) {
        permsToAdd.push({
            accessLevel,
            workspaceId,
            pageId,
            userId: id
        })
    }
    const addedPermissions = await service.batchAdd(permsToAdd)

    currentPermissions.push(...addedPermissions)

    // send email to permsToAdd
    const emailsToNotify: string[] = []

    for (const id of noPermUserIds) {
        emailsToNotify.push(usersMap[id].email)
    }

    const messageId = await sendWorkspacePageInvitedEmail(resolve.member.workspace, resolve.page, emailsToNotify)

    RecalculateWorkspaceStats(workspaceId).then()

    await broadcastNewWorkspaceMembers(workspaceId, nonMemberIds)
    broadcastPagePermissionAdded(workspaceId, resolve.page.id, resolve.page.databaseId, addedPermissions)

    return {
        permissions: currentPermissions, messageId
    }
}

export interface UpdatePermissionRequestBody {
    userId: string
    accessLevel: AccessLevel
}

export const UpdateAccessToPage = async (userId: string, workspaceId: string, pageId: string, data: UpdatePermissionRequestBody) => {
    if (!data.userId) {
        throw new RequiredParameterError("userId");
    }
    const accessLevel = (data.accessLevel || "").trim() as AccessLevel
    if (!accessLevel) {
        throw new RequiredParameterError("accessLevel")
    }
    if (![AccessLevel.Edit, AccessLevel.Full, AccessLevel.View].includes(accessLevel)) {
        throw new InvalidParameterError("accessLevel")
    }

    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const service = new PagePermissionService()
    await service.update({
        userId: data.userId,
        pageId,
        workspaceId
    }, {
        accessLevel: data.accessLevel
    })

    broadcastPagePermissionUpdated(workspaceId, resolve.page.id, resolve.page.databaseId, data)


    return true
}

export interface RemovePermissionRequestBody {
    userIds: string[]
}

export const RemoveAccessToPage = async (userId: string, workspaceId: string, pageId: string, data: RemovePermissionRequestBody) => {
    if (!data.userIds) {
        throw new RequiredParameterError("userIds");
    }
    if (!Array.isArray(data.userIds)) {
        throw new InvalidParameterError("userIds is invalid");
    }

    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const service = new PagePermissionService()
    await service.hardRemove({
        userId: In(data.userIds),
        pageId,
        workspaceId: workspaceId
    })

    broadcastPagePermissionDeleted(workspaceId, resolve.page.id, resolve.page.databaseId, data)


    return true
}

export interface AddViewRequestData {
    name: string
    type: ViewType
    definition: ViewDefinition
}

export const AddPageView = async (userId: string, workspaceId: string, pageId: string, reqData: AddViewRequestData) => {
    const {name, type, definition} = reqData

    if (!name) {
        throw new RequiredParameterError("name")
    }
    if (!type) {
        throw new RequiredParameterError("type")
    }
    if (!definition) {
        throw new RequiredParameterError("definition")
    }
    if (!Object.values(ViewType).includes(type)) {
        throw new InvalidParameterError("type")
    }
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const data: CreateViewData = {
        pageId,
        name,
        type,
        definition,
        createdById: userId
    }
    const view = await s.insert(data)

    const s2 = new PageService()
    const viewsOrder = resolve.page.viewsOrder || []
    viewsOrder.push(view.id)

    await s2.update({id: pageId}, {viewsOrder})
    resolve.page.viewsOrder = viewsOrder

    broadcastPageViewAdded(workspaceId, resolve.page, view)
    return {
        page: resolve.page, view
    }
}

export const DeletePageView = async (userId: string, workspaceId: string, pageId: string, data: { viewId: string }) => {
    const {viewId} = data
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    await s.remove({pageId, id: viewId})

    const s2 = new PageService()
    const viewsOrder = removeArrayItem(resolve.page.viewsOrder || [], viewId)
    await s2.update({id: pageId}, {viewsOrder})
    resolve.page.viewsOrder = viewsOrder

    return {
        page: resolve.page
    }
}

export const PublishPageView = async (userId: string, workspaceId: string, pageId: string, data: {
    viewId: string
}) => {
    const {viewId} = data
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (!view.isPublished) {
        const update: Partial<View> = {
            isPublished: true, publishedAt: new Date()
        }
        await s.update({pageId, id: viewId}, update)
        view.isPublished = true
        view.publishedAt = new Date()
    }
    return {
        page: resolve.page, view
    }
}

export const UnPublishPageView = async (userId: string, workspaceId: string, pageId: string, data: {
    viewId: string
}) => {
    const {viewId} = data
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (view.isPublished) {
        const update: Partial<View> = {
            isPublished: false, publishedAt: null
        }
        await s.update({pageId, id: viewId}, update)
        view.isPublished = false
        view.publishedAt = null
    }
    return {
        page: resolve.page, view
    }
}

const sendWorkspacePageInvitedEmail = async (workspace: Workspace, page: Page, emails: string[]) => {

    const inviteLink = page.databaseId ?
                       appUrl(`/${workspace.domain}/databases/${page.databaseId}`) :
                       appUrl(`/${workspace.domain}/${page.id}`);

    const to: EmailUser = {
        email: emails[0],
        name: ''
    }
    const subject = `${page.name} shared with you on Opendashboard`
    const message = `
    <p style="font-size: 24px; font-weight: 600;">${page.name} shared with you on Opendashboard</p>
    <p style="color:#313539;">Click on the button below to view the page.</p>
	`
    const button = {
        label: 'View page →',
        url: inviteLink
    }
    return await SendEmailWithContent(to, subject, message, button, null, true, null, null, null, null, null, emails)
}

export const resolvePageAccessLevel = async (userId: string, workspaceId: string, pageId: string) => {
    return resolvePageObjectAccessLevel(userId, workspaceId, "page", pageId)
}

const computeBestAccessLevel = (accessLevels: AccessLevel[]) => {
    if (accessLevels.length === 0) {
        return null
    }
    if (accessLevels.includes(AccessLevel.Full)) return AccessLevel.Full
    if (accessLevels.includes(AccessLevel.Edit)) return AccessLevel.Edit
    if (accessLevels.includes(AccessLevel.View)) return AccessLevel.View
    return null
}

export interface ResolvedPage {
    page: Page
    permissions: PagePermission[]
    accessLevel?: AccessLevel
    member: MyWorkspace
}

export const resolvePageObjectAccessLevel = async (userId: string, workspaceId: string, object: "page" | "database", objectId: string): Promise<ResolvedPage | null> => {
    const member = userId ? await GetMyWorkspace(userId, workspaceId) : null
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const service = new PageService();
    const permissionService = new PagePermissionService();

    let page: Page;
    if (object === "page") {
        page = await service.findOne({id: objectId, workspaceId});
    } else if (object === "database") {
        page = await service.findOne({databaseId: objectId, workspaceId});
    } else {
        throw new BadRequestError(ErrorMessage.UnableToProcessRequest)
    }
    if (!page) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const permissions = await permissionService.find({pageId: page.id, workspaceId})

    const accessLevels: AccessLevel[] = []
    if (member.workspaceMember.role !== WorkspaceMemberRole.Collaborator && page.visibility === Visibility.Open) {
        accessLevels.push(page.accessLevel)
    }
    for (const permission of permissions) {
        if (permission.userId === userId) {
            accessLevels.push(permission.accessLevel)
            break
        }
    }
    if (page.ownerId === userId) {
        accessLevels.push(AccessLevel.Full)
    }
    const accessLevel = computeBestAccessLevel(accessLevels)
    // console.log('Resolving access: ', {
    //     member, page, perissions: pageWithPermissions.permissions, accessLevels, accessLevel
    // })
    return {
        page,
        permissions,
        member,
        accessLevel
    }
}


const createGettingStartedPage = async (userId: string, workspaceId: string) => {
    const s2 = new PageService()
    const s3 = new ViewService()
    const s4 = new DocumentService()

    const getStarted = GettingStartedDoc()
    const document = await s4.insert({
        name: getStarted.docTitle,
        workspaceId,
        updatedById: userId,
        createdById: userId,
        contentJSON: getStarted.contentJSON,
        contentText: getStarted.contentText,
        viewId: generateUUID(),
        id: generateUUID()
    })

    const pageData: CreatePageData = {
        name: "Getting Started",
        workspaceId,
        createdById: userId,
        ownerId: userId,
        icon: null,
        visibility: Visibility.Open,
        viewsOrder: [document.viewId],
        createdAt: datePlusHours(new Date(), -1)
    }
    const page = await s2.createPage(pageData)
    const viewDef: DocumentViewDefinition = {
        type: ViewType.Document,
        lockContent: false,
        itemsOrder: [document.id],
    }
    const viewData: CreateViewData = {
        pageId: page.id,
        name: `Setup Guide`,
        type: ViewType.Document,
        definition: viewDef,
        createdById: userId,
        id: document.viewId
    }
    const view = await s3.insert(viewData)

    return {page, view, document}
}

export const SetupGettingStartedPage = async (workspaceId: string, userId: string) => {

    const {page: gettingStartedPage} = await createGettingStartedPage(userId, workspaceId)

    return { gettingStartedPage}
}
