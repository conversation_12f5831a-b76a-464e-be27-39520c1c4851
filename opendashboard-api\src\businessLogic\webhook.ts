import {WorkflowService} from "../service/workflow";
import {BadRequestError, ErrorMessage, NotfoundError, UnauthorizedError} from "../errors/AppError";
import {IWorkflowNodeData, WebhookData, WebhookTriggerWorkflowNode_Configs, WorkflowApprovalLinks, WorkflowInstanceStatus, WorkflowStatus, WorkflowTaskStatus, WorkflowTriggerType} from "opendb-app-db-utils/lib/typings/workflow";
import {RequestWorkspaceVariablesSecretsSubstitutionVars} from "./workspace";
import {substituteTags} from "../utility/template";
import {WebhookService} from "../service/webhook";
import {broadcastTestIntegrationWebhookReceived, broadcastTestWebhookReceived} from "../socketio/workspace";
import {generateUUID, substituteVarsInObjects} from "opendb-app-db-utils/lib";
import {StartWorkflowRuns, triggerWorkflowRunner, WorkflowRunData} from "./runStarter/runStarter";
import {WorkflowInstanceService} from "../service/workflowInstance";
import {In, Not} from "typeorm";
import {TokenExpiryUnit, TokenService} from "../service/token";
import {Token, TokenType} from "../entity/Token";
import {apiUrl} from "../config";
import {WorkflowTaskService} from "../service/workflowTask";
import {Workflow} from "../entity/workflow";
import {getTriggerType} from "./workflow";
import {RunStepParams} from "@opendashboard-inc/integration-core-api";
import {getConnectionAPI, getFilesAPI, getStoreAPI, getWebhookURL} from "./integrationHelpers";
import {consoleLog} from "./logtail";
import {getCoreApi} from "./integration";

export const HandleDispatch = async (id: string, method: string, headers: object = {}, body: object = {}, queryParams: object = {}, isTest = false) => {
    const s = new WorkflowService()
    const workflow = await s.findOne({id, triggerType: WorkflowTriggerType.Webhook_OnWebhook, status: WorkflowStatus.Published})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)
    const definition = isTest ? workflow.draftDefinition || workflow.definition : workflow.definition
    if (!definition) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const triggerNode = definition.map[definition.triggerId]
    if (!triggerNode || triggerNode.data?.type !== WorkflowTriggerType.Webhook_OnWebhook) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const triggerConfigs: WebhookTriggerWorkflowNode_Configs | undefined = triggerNode.data?.configs
    if (!triggerConfigs) throw new NotfoundError(ErrorMessage.EntityNotFound)

    if (triggerConfigs.authType === 'basic') {
        const authHeader = headers['authorization']
        if (!authHeader || !authHeader.startsWith('Basic ') || !triggerConfigs.basicAuth) throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound)
        delete headers['authorization']

        const basicAuth = triggerConfigs.basicAuth
        let basicAuthUsername = basicAuth.username || ''
        let basicAuthPassword = basicAuth.password || ''

        const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workflow.workspaceId)

        basicAuthUsername = substituteTags(basicAuthUsername, vars)
        basicAuthPassword = substituteTags(basicAuthPassword, vars)

        const base64Credentials = authHeader.split(' ')[1]
        const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')
        const [username, password] = credentials.split(':')

        // console.log("Pre check:", {username, password, basicAuthUsername, basicAuthPassword})

        if (!username || !password) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
        if (username !== basicAuthUsername || password !== basicAuthPassword) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    } else if (triggerConfigs.authType === 'header') {
        if (!triggerConfigs.headerAuth || !triggerConfigs.headerAuth.key || !triggerConfigs.headerAuth.value) throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound)

        const headerKey = triggerConfigs.headerAuth.key || ''
        let headerValue = triggerConfigs.headerAuth.value || ''

        if (!headers[headerKey]) throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound)

        const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workflow.workspaceId)

        headerValue = substituteTags(headerValue, vars)

        if (headers[headerKey] !== headerValue) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

        delete headers[headerKey]
    }

    const hook: WebhookData = {
        id: generateUUID(),
        createdAt: new Date().toISOString(),
        workflowId: id,
        workspaceId: workflow.workspaceId,
        method,
        headers,
        body,
        query: queryParams,
        isTest
    }
    ProcessWebhook(workflow, hook, triggerNode.id).then()
}

const ProcessWebhook = async (workflow: Workflow, hook: WebhookData, triggerNodeId: string) => {
    if (hook.isTest) {
        broadcastTestWebhookReceived(hook.workspaceId, hook)
        return
    }
    if (workflow.status !== WorkflowStatus.Published && workflow.status !== WorkflowStatus.Paused) throw new BadRequestError(ErrorMessage.WorkflowNotPublished)
    const s = new WebhookService()
    const webhook = await s.insert(hook)

    const runData: WorkflowRunData = {
        triggerNodeId, triggerOutput: hook.body || {}, workflow, taskMeta: {webhookId: webhook.id}
    }

    await StartWorkflowRuns([runData])
}

export const DispatchApprovalAction = async (id: string, runId: string, action: 'approve' | 'reject', query: { hash?: string } = {}) => {
    if (runId === 'test-run') {
        switch (action) {
            case "approve":
                return {message: "Approved"}
            // case "details":
            //     return {message: "Waiting for approval/Approval requested/Approval granted/Approval rejected"}
            case "reject":
                return {message: "Rejected"}
            default:
                throw new BadRequestError(ErrorMessage.UnableToProcessRequest)
        }
    }
    const encodedHash = query.hash || ''
    if (!encodedHash) throw new BadRequestError(ErrorMessage.LinkIsInvalidOrExpired)

    const hash = decodeURIComponent(encodedHash)
    const [instanceId, tkn] = hash.split(':')

    if (instanceId !== runId) throw new BadRequestError(ErrorMessage.LinkIsInvalidOrExpired)

    const service = new TokenService();
    const approveToken: Token = await service.verifyToken(`wi:${instanceId}`, tkn, TokenType.WorkflowInstanceApproval)
    if (!approveToken) {
        throw new BadRequestError(`The token provided is invalid or expired`)
    }

    await FinalizeWorkflowInstanceApproval(id, instanceId, action)

    return {message: 'Response recorded, you can close the page now.'}
}

export const FinalizeWorkflowInstanceApproval = async (workflowId: string, instanceId: string, action: 'approve' | 'reject') => {
    const iS = new WorkflowInstanceService()
    const instance = await iS.findOne({id: instanceId, workflowId})
    if (!instance) throw new NotfoundError(ErrorMessage.WorkflowInstanceNotFound)
    if (instance.status !== WorkflowInstanceStatus.PendingApproval) throw new BadRequestError(ErrorMessage.WorkflowInstanceNotPendingApproval)

    const tS = new WorkflowTaskService()
    const task = await tS.findOne({instanceId: instanceId, status: WorkflowTaskStatus.PendingApproval})
    if (!task) throw new NotfoundError(ErrorMessage.WorkflowInstanceNotPendingApproval)

    await iS.update({id: instanceId}, {status: WorkflowInstanceStatus.Active})
    await tS.update({id: task.id}, {status: WorkflowTaskStatus.Scheduled, taskOutput: {approved: action === 'approve'}})

    const service = new TokenService();
    await service.deleteAllUserApiToken(`wi:${instanceId}`, TokenType.WorkflowInstanceApproval)

    triggerWorkflowRunner(`Approval ${action} for instance ${instanceId} in workflow: ${workflowId} ${action === 'approve' ? 'granted' : 'rejected'}`)
}

export const CreateWorkflowInstanceApprovalLink = async (workflowId: string, instanceId: string): Promise<WorkflowApprovalLinks> => {
    const s = new WorkflowInstanceService()
    const instance = await s.findOne({id: instanceId, workflowId, status: Not(In([WorkflowInstanceStatus.Completed, WorkflowInstanceStatus.Cancelled]))})
    if (!instance) throw new NotfoundError(ErrorMessage.WorkflowInstanceNotFound)

    const s2 = new TokenService()
    await s2.deleteAllUserApiToken(`wi:${instanceId}`, TokenType.WorkflowInstanceApproval)
    const token: Token = await s2.createToken(`wi:${instanceId}`, TokenType.WorkflowInstanceApproval, {
        value: 1000,
        unit: TokenExpiryUnit.Days
    })
    const baseLink = apiUrl(`/v0/webhooks/${workflowId}/approvals/${instanceId}`)

    const hash = encodeURIComponent(`${instanceId}:${token.token}`);

    const approveLink = `${baseLink}/approve?hash=${hash}`;
    const rejectLink = `${baseLink}/reject?hash=${hash}`;

    return {approveLink, rejectLink}
}

export const HandleIntegrationDispatch = async (id: string, integration: string, trigger: string, method: string, headers: Record<string, any> = {}, body: any = {}, queryParams: any = {}, isTest = false) => {
    const s = new WorkflowService()
    const workflow = await s.findOne({id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)
    const definition = isTest ? workflow.draftDefinition || workflow.definition : workflow.definition
    if (!definition) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const triggerNode = definition.map[definition.triggerId]

    // if (!triggerNode || triggerNode.data?.type !== triggerType) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (!triggerNode) throw new NotfoundError(ErrorMessage.WorkflowTriggerNotFound)
    const data = triggerNode.data as IWorkflowNodeData
    if (!data)  throw new BadRequestError(ErrorMessage.WorkflowTriggerNotFound)

    const type = data.type || ''
    const typeSplit = type.split(':')
    if (typeSplit.length < 2 || typeSplit[0] !== integration || typeSplit[1] !== trigger) {
        throw new BadRequestError(ErrorMessage.WorkflowTriggerNotFound)
    }
    const connectionId = data.connectionId
    const rawProps = data.configs || {}

    const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workflow.workspaceId)
    const propsValue = substituteVarsInObjects(rawProps || {}, vars, 'curly')
    const workspaceId = workflow.workspaceId

    const webhookUrl = getWebhookURL(workflow.id, integration, trigger, !!isTest)

    const params: RunStepParams<any> = {
        connectionId,
        integration: {name: integration},
        files: getFilesAPI(workspaceId),
        log: (args: any) => consoleLog(args),
        store: getStoreAPI(workspaceId, integration, id),
        connection: getConnectionAPI(workspaceId),
        propsValue: propsValue,
        type: 'trigger',
        name: trigger,
        mode: !isTest ? "run" : "test",
        payload: {
            headers: headers,
            body: body,
            query: queryParams,
            method: method
        },
        webhookUrl
    }
    const coreApi = getCoreApi()
    const result = await coreApi.runStep(params);

    if (isTest) {
        const hook: IntegrationWebhook = {
            workflowId: id,
            integration,
            trigger,
            result
        }
        broadcastTestIntegrationWebhookReceived(workflow.workspaceId, hook)
        return
    }
    const instanceData: any[] = Array.isArray(result) ? result : [result]
    const runData: WorkflowRunData[] = instanceData.map((data) => ({
        triggerNodeId: triggerNode.id,
        triggerOutput: data,
        workflow,
        taskMeta: {webhookId: id}
    }))
    await StartWorkflowRuns(runData)
}

export interface IntegrationWebhook {
    workflowId: string
    integration: string
    trigger: string
    result: any
}


