"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-parse";
exports.ids = ["vendor-chunks/rehype-parse"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-parse/index.js":
/*!********************************************!*\
  !*** ./node_modules/rehype-parse/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(ssr)/./node_modules/rehype-parse/lib/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLXBhcnNlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWh5cGUtcGFyc2UvaW5kZXguanM/MThlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHR9IGZyb20gJy4vbGliL2luZGV4LmpzJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-parse/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-parse/lib/errors.js":
/*!*************************************************!*\
  !*** ./node_modules/rehype-parse/lib/errors.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   errors: () => (/* binding */ errors)\n/* harmony export */ });\nconst errors = {\n  abandonedHeadElementChild: {\n    reason: 'Unexpected metadata element after head',\n    description:\n      'Unexpected element after head. Expected the element before `</head>`',\n    url: false\n  },\n  abruptClosingOfEmptyComment: {\n    reason: 'Unexpected abruptly closed empty comment',\n    description: 'Unexpected `>` or `->`. Expected `-->` to close comments'\n  },\n  abruptDoctypePublicIdentifier: {\n    reason: 'Unexpected abruptly closed public identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the public identifier'\n  },\n  abruptDoctypeSystemIdentifier: {\n    reason: 'Unexpected abruptly closed system identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the identifier identifier'\n  },\n  absenceOfDigitsInNumericCharacterReference: {\n    reason: 'Unexpected non-digit at start of numeric character reference',\n    description:\n      'Unexpected `%c`. Expected `[0-9]` for decimal references or `[0-9a-fA-F]` for hexadecimal references'\n  },\n  cdataInHtmlContent: {\n    reason: 'Unexpected CDATA section in HTML',\n    description:\n      'Unexpected `<![CDATA[` in HTML. Remove it, use a comment, or encode special characters instead'\n  },\n  characterReferenceOutsideUnicodeRange: {\n    reason: 'Unexpected too big numeric character reference',\n    description:\n      'Unexpectedly high character reference. Expected character references to be at most hexadecimal 10ffff (or decimal 1114111)'\n  },\n  closingOfElementWithOpenChildElements: {\n    reason: 'Unexpected closing tag with open child elements',\n    description:\n      'Unexpectedly closing tag. Expected other tags to be closed first',\n    url: false\n  },\n  controlCharacterInInputStream: {\n    reason: 'Unexpected control character',\n    description:\n      'Unexpected control character `%x`. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  controlCharacterReference: {\n    reason: 'Unexpected control character reference',\n    description:\n      'Unexpectedly control character in reference. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  disallowedContentInNoscriptInHead: {\n    reason: 'Disallowed content inside `<noscript>` in `<head>`',\n    description:\n      'Unexpected text character `%c`. Only use text in `<noscript>`s in `<body>`',\n    url: false\n  },\n  duplicateAttribute: {\n    reason: 'Unexpected duplicate attribute',\n    description:\n      'Unexpectedly double attribute. Expected attributes to occur only once'\n  },\n  endTagWithAttributes: {\n    reason: 'Unexpected attribute on closing tag',\n    description: 'Unexpected attribute. Expected `>` instead'\n  },\n  endTagWithTrailingSolidus: {\n    reason: 'Unexpected slash at end of closing tag',\n    description: 'Unexpected `%c-1`. Expected `>` instead'\n  },\n  endTagWithoutMatchingOpenElement: {\n    reason: 'Unexpected unopened end tag',\n    description: 'Unexpected end tag. Expected no end tag or another end tag',\n    url: false\n  },\n  eofBeforeTagName: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected tag name instead'\n  },\n  eofInCdata: {\n    reason: 'Unexpected end of file in CDATA',\n    description: 'Unexpected end of file. Expected `]]>` to close the CDATA'\n  },\n  eofInComment: {\n    reason: 'Unexpected end of file in comment',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  eofInDoctype: {\n    reason: 'Unexpected end of file in doctype',\n    description:\n      'Unexpected end of file. Expected a valid doctype (such as `<!doctype html>`)'\n  },\n  eofInElementThatCanContainOnlyText: {\n    reason: 'Unexpected end of file in element that can only contain text',\n    description: 'Unexpected end of file. Expected text or a closing tag',\n    url: false\n  },\n  eofInScriptHtmlCommentLikeText: {\n    reason: 'Unexpected end of file in comment inside script',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  eofInTag: {\n    reason: 'Unexpected end of file in tag',\n    description: 'Unexpected end of file. Expected `>` to close the tag'\n  },\n  incorrectlyClosedComment: {\n    reason: 'Incorrectly closed comment',\n    description: 'Unexpected `%c-1`. Expected `-->` to close the comment'\n  },\n  incorrectlyOpenedComment: {\n    reason: 'Incorrectly opened comment',\n    description: 'Unexpected `%c`. Expected `<!--` to open the comment'\n  },\n  invalidCharacterSequenceAfterDoctypeName: {\n    reason: 'Invalid sequence after doctype name',\n    description: 'Unexpected sequence at `%c`. Expected `public` or `system`'\n  },\n  invalidFirstCharacterOfTagName: {\n    reason: 'Invalid first character in tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  misplacedDoctype: {\n    reason: 'Misplaced doctype',\n    description: 'Unexpected doctype. Expected doctype before head',\n    url: false\n  },\n  misplacedStartTagForHeadElement: {\n    reason: 'Misplaced `<head>` start tag',\n    description:\n      'Unexpected start tag `<head>`. Expected `<head>` directly after doctype',\n    url: false\n  },\n  missingAttributeValue: {\n    reason: 'Missing attribute value',\n    description:\n      'Unexpected `%c-1`. Expected an attribute value or no `%c-1` instead'\n  },\n  missingDoctype: {\n    reason: 'Missing doctype before other content',\n    description: 'Expected a `<!doctype html>` before anything else',\n    url: false\n  },\n  missingDoctypeName: {\n    reason: 'Missing doctype name',\n    description: 'Unexpected doctype end at `%c`. Expected `html` instead'\n  },\n  missingDoctypePublicIdentifier: {\n    reason: 'Missing public identifier in doctype',\n    description: 'Unexpected `%c`. Expected identifier for `public` instead'\n  },\n  missingDoctypeSystemIdentifier: {\n    reason: 'Missing system identifier in doctype',\n    description:\n      'Unexpected `%c`. Expected identifier for `system` instead (suggested: `\"about:legacy-compat\"`)'\n  },\n  missingEndTagName: {\n    reason: 'Missing name in end tag',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  missingQuoteBeforeDoctypePublicIdentifier: {\n    reason: 'Missing quote before public identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  missingQuoteBeforeDoctypeSystemIdentifier: {\n    reason: 'Missing quote before system identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  missingSemicolonAfterCharacterReference: {\n    reason: 'Missing semicolon after character reference',\n    description: 'Unexpected `%c`. Expected `;` instead'\n  },\n  missingWhitespaceAfterDoctypePublicKeyword: {\n    reason: 'Missing whitespace after public identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  missingWhitespaceAfterDoctypeSystemKeyword: {\n    reason: 'Missing whitespace after system identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  missingWhitespaceBeforeDoctypeName: {\n    reason: 'Missing whitespace before doctype name',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  missingWhitespaceBetweenAttributes: {\n    reason: 'Missing whitespace between attributes',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: {\n    reason:\n      'Missing whitespace between public and system identifiers in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  nestedComment: {\n    reason: 'Unexpected nested comment',\n    description: 'Unexpected `<!--`. Expected `-->`'\n  },\n  nestedNoscriptInHead: {\n    reason: 'Unexpected nested `<noscript>` in `<head>`',\n    description:\n      'Unexpected `<noscript>`. Expected a closing tag or a meta element',\n    url: false\n  },\n  nonConformingDoctype: {\n    reason: 'Unexpected non-conforming doctype declaration',\n    description:\n      'Expected `<!doctype html>` or `<!doctype html system \"about:legacy-compat\">`',\n    url: false\n  },\n  nonVoidHtmlElementStartTagWithTrailingSolidus: {\n    reason: 'Unexpected trailing slash on start tag of non-void element',\n    description: 'Unexpected `/`. Expected `>` instead'\n  },\n  noncharacterCharacterReference: {\n    reason:\n      'Unexpected noncharacter code point referenced by character reference',\n    description: 'Unexpected code point. Do not use noncharacters in HTML'\n  },\n  noncharacterInInputStream: {\n    reason: 'Unexpected noncharacter character',\n    description: 'Unexpected code point `%x`. Do not use noncharacters in HTML'\n  },\n  nullCharacterReference: {\n    reason: 'Unexpected NULL character referenced by character reference',\n    description: 'Unexpected code point. Do not use NULL characters in HTML'\n  },\n  openElementsLeftAfterEof: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected closing tag instead',\n    url: false\n  },\n  surrogateCharacterReference: {\n    reason: 'Unexpected surrogate character referenced by character reference',\n    description:\n      'Unexpected code point. Do not use lone surrogate characters in HTML'\n  },\n  surrogateInInputStream: {\n    reason: 'Unexpected surrogate character',\n    description:\n      'Unexpected code point `%x`. Do not use lone surrogate characters in HTML'\n  },\n  unexpectedCharacterAfterDoctypeSystemIdentifier: {\n    reason: 'Invalid character after system identifier in doctype',\n    description: 'Unexpected character at `%c`. Expected `>`'\n  },\n  unexpectedCharacterInAttributeName: {\n    reason: 'Unexpected character in attribute name',\n    description:\n      'Unexpected `%c`. Expected whitespace, `/`, `>`, `=`, or probably an ASCII letter'\n  },\n  unexpectedCharacterInUnquotedAttributeValue: {\n    reason: 'Unexpected character in unquoted attribute value',\n    description: 'Unexpected `%c`. Quote the attribute value to include it'\n  },\n  unexpectedEqualsSignBeforeAttributeName: {\n    reason: 'Unexpected equals sign before attribute name',\n    description: 'Unexpected `%c`. Add an attribute name before it'\n  },\n  unexpectedNullCharacter: {\n    reason: 'Unexpected NULL character',\n    description:\n      'Unexpected code point `%x`. Do not use NULL characters in HTML'\n  },\n  unexpectedQuestionMarkInsteadOfTagName: {\n    reason: 'Unexpected question mark instead of tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  unexpectedSolidusInTag: {\n    reason: 'Unexpected slash in tag',\n    description:\n      'Unexpected `%c-1`. Expected it followed by `>` or in a quoted attribute value'\n  },\n  unknownNamedCharacterReference: {\n    reason: 'Unexpected unknown named character reference',\n    description:\n      'Unexpected character reference. Expected known named character references'\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-parse/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-parse/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-parse/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeParse)\n/* harmony export */ });\n/* harmony import */ var parse5_lib_parser_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5/lib/parser/index.js */ \"(ssr)/./node_modules/parse5/lib/parser/index.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./errors.js */ \"(ssr)/./node_modules/rehype-parse/lib/errors.js\");\n/**\n * @typedef {import('hast').Root} Root\n * @typedef {Pick<import('hast-util-from-parse5').Options, 'space' | 'verbose'>} FromParse5Options\n *\n * @typedef {keyof errors} ErrorCode\n * @typedef {0|1|2|boolean|null|undefined} ErrorSeverity\n * @typedef {Partial<Record<ErrorCode, ErrorSeverity>>} ErrorFields\n *\n * @typedef ParseFields\n * @property {boolean|undefined} [fragment=false]\n *   Specify whether to parse a fragment, instead of a complete document.\n *   In document mode, unopened `html`, `head`, and `body` elements are opened\n *   in just the right places.\n * @property {boolean|undefined} [emitParseErrors=false]\n *   > ⚠️ Parse errors are currently being added to HTML.\n *   > Not all errors emitted by parse5 (or rehype-parse) are specced yet.\n *   > Some documentation may still be missing.\n *\n *   Emit parse errors while parsing on the vfile.\n *   Setting this to `true` starts emitting HTML parse errors.\n *\n *   Specific rules can be turned off by setting them to `false` (or `0`).\n *   The default, when `emitParseErrors: true`, is `true` (or `1`), and means\n *   that rules emit as warnings.\n *   Rules can also be configured with `2`, to turn them into fatal errors.\n *\n * @typedef {FromParse5Options & ParseFields & ErrorFields} Options\n */\n\n// @ts-expect-error: remove when typed\n\n\n\n\nconst base = 'https://html.spec.whatwg.org/multipage/parsing.html#parse-error-'\n\nconst fatalities = {2: true, 1: false, 0: null}\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?] | Array<void>, string, Root>}\n */\nfunction rehypeParse(options) {\n  const processorSettings = /** @type {Options} */ (this.data('settings'))\n  const settings = Object.assign({}, processorSettings, options)\n\n  Object.assign(this, {Parser: parser})\n\n  /** @type {import('unified').ParserFunction<Root>} */\n  function parser(doc, file) {\n    const fn = settings.fragment ? 'parseFragment' : 'parse'\n    const onParseError = settings.emitParseErrors ? onerror : null\n    const parse5 = new parse5_lib_parser_index_js__WEBPACK_IMPORTED_MODULE_0__({\n      sourceCodeLocationInfo: true,\n      onParseError,\n      scriptingEnabled: false\n    })\n\n    // @ts-expect-error: `parse5` returns document or fragment, which are always\n    // mapped to roots.\n    return (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_1__.fromParse5)(parse5[fn](doc), {\n      space: settings.space,\n      file,\n      verbose: settings.verbose\n    })\n\n    /**\n     * @param {{code: string, startLine: number, startCol: number, startOffset: number, endLine: number, endCol: number, endOffset: number}} error\n     */\n    function onerror(error) {\n      const code = error.code\n      const name = camelcase(code)\n      const setting = settings[name]\n      const config = setting === undefined || setting === null ? true : setting\n      const level = typeof config === 'number' ? config : config ? 1 : 0\n      const start = {\n        line: error.startLine,\n        column: error.startCol,\n        offset: error.startOffset\n      }\n      const end = {\n        line: error.endLine,\n        column: error.endCol,\n        offset: error.endOffset\n      }\n      if (level) {\n        /* c8 ignore next */\n        const info = _errors_js__WEBPACK_IMPORTED_MODULE_2__.errors[name] || {reason: '', description: '', url: ''}\n        const message = file.message(format(info.reason), {start, end})\n        message.source = 'parse-error'\n        message.ruleId = code\n        message.fatal = fatalities[level]\n        message.note = format(info.description)\n        message.url = 'url' in info && info.url === false ? null : base + code\n      }\n\n      /**\n       * @param {string} value\n       * @returns {string}\n       */\n      function format(value) {\n        return value\n          .replace(/%c(?:-(\\d+))?/g, (_, /** @type {string} */ $1) => {\n            const offset = $1 ? -Number.parseInt($1, 10) : 0\n            const char = doc.charAt(error.startOffset + offset)\n            return char === '`' ? '` ` `' : char\n          })\n          .replace(\n            /%x/g,\n            () =>\n              '0x' +\n              doc.charCodeAt(error.startOffset).toString(16).toUpperCase()\n          )\n      }\n    }\n  }\n}\n\n/**\n * @param {string} value\n * @returns {ErrorCode}\n */\nfunction camelcase(value) {\n  // @ts-expect-error: this returns a valid error code.\n  return value.replace(/-[a-z]/g, ($0) => $0.charAt(1).toUpperCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-parse/lib/index.js\n");

/***/ })

};
;