import {NextFunction, Request, Response} from "express"
import {HttpStatusCode} from "../errors/AppError";
import {VerifyJWTToken} from "../businessLogic/auth";
import {saveAuthInfo} from "../businessLogic/authInfo";

export const verifyJWT = async (request: Request, response: Response, next: NextFunction, allowAnanymous = false) => {
    const {authorization: authHeader} = request.headers;
    if (!authHeader && !allowAnanymous) {
        return response.status(HttpStatusCode.UNAUTHORIZED).json({
            error: "Authorization header not found",
            title: `BadRequestError: "Invalid JWT Token"`
        })
    }
    const [, tkn] = (authHeader || '').split(' ')
    const validApiToken = await VerifyJWTToken(tkn, allowAnanymous)

    if (!validApiToken) {
        return response.status(HttpStatusCode.UNAUTHORIZED).json({
            error: "Invalid JWT Token",
            title: `UnauthorizedError: "Invalid JWT Token"`
        })
    }
    saveAuthInfo(request, validApiToken);
    next()
}

export const verifyJWTIfPossible = async (request: Request, response: Response, next: NextFunction) => {
    return verifyJWT(request, response, next, true)
}





