import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {AffiliateController} from "./controller";

export const affiliateRoutes: Routes = {
    basePath: '/affiliates',
    middleware: [verifyJWT],
    routes: {
        "/": {
            post: {controller: Affiliate<PERSON>ontroller, action: "create"},
            get: {controller: AffiliateController, action: "get"},
            patch: {controller: AffiliateController, action: "patch"},
        },
        "/payouts": {
            get: {controller: AffiliateController, action: "getPayouts"},
        },
        "/stats": {
            get: {controller: AffiliateController, action: "getStats"},
        },
    }
}
