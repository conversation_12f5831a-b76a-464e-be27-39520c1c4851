import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from 'typeorm';
import {KeyValueStore} from "./WorkspaceMemberSettings";
import {DbRecordFilter} from "opendb-app-db-utils/lib/typings/db";

export enum CampaignType {
    Campaign = "campaign",
    Sequence = "sequence"
}

export enum CampaignStatus {
    Draft = "draft",
    InReview = "in_review",
    Published = "published",
    Scheduled = "scheduled",
    Queued = "queued",
    Sending = "sending",
    Failed = "failed",
    Sent = "sent",
    PartialDelivery = "partial_delivery",
}

export enum SequenceTarget {
    All = "all",
    Opened = "opened",
    Clicked = "clicked",
    DidNotOpen = "did_not_open",
    DidNotClick = "did_not_click"
}

export interface CampaignTarget {
    recordId: string,
    targetRecordId: string
}

export interface CampaignAttachment {
    id: string,
    title: string,
    link: string
    type: string;
    size: number;
    sizeReadable: string;
}

@Entity()
export class Campaign {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    parentId: string;

    @Column({type: 'varchar', nullable: true})
    subject: string;

    @Column({type: 'text', nullable: true})
    contentText: string;

    @Column({type: 'json', nullable: true})
    cc: string[];

    @Column({type: 'json', nullable: true})
    bcc: string[];

    @Column({type: 'json', nullable: true})
    attachments: CampaignAttachment[];

    @Index()
    @Column({type: 'varchar', nullable: false})
    type: CampaignType;

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    updatedById: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    databaseId: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    targetDatabaseId: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    targetColId: string;

    @Column({type: 'json', nullable: true})
    recordIds: string[];

    @Column({type: 'json', nullable: true})
    targetRecords: CampaignTarget[];

    @Column({type: 'json', nullable: true})
    targetRecordIdsToSkip: string[];

    @Column({type: 'json', nullable: true})
    sequenceRecordFilter: DbRecordFilter;

    @Index()
    @Column({type: 'varchar', nullable: true, default: SequenceTarget.All})
    sequenceTarget: SequenceTarget;

    @Index()
    @Column({type: 'varchar', nullable: false, default: CampaignStatus.Draft})
    status: CampaignStatus;

    @Column({type: 'varchar', nullable: true})
    failedReason: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    senderId: string;

    @Column({type: 'timestamp', nullable: true})
    deliverAt: Date;

    @Column({type: 'timestamp', nullable: true})
    deliveredAt: Date;

    @Index()
    @Column({type: 'timestamp', nullable: true, select: false})
    deliveryHeartbeatAt: Date;

    @Column({type: 'varchar', nullable: true})
    sendAtLocalTime: string;

    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true})
    auditLog: string[]

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

}
