import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {AddPageView, CreatePage, DeletePage, DeletePageView, DuplicateMyPage, DuplicateView, GetMyPage, GetMyPages, InviteToPage, PublishPageView, RemoveAccessToPage, UnPublishPageView, UpdateAccessToPage, UpdatePage} from "../../businessLogic/page";


export interface UpdateViewRequestData {
    viewId: string;
    isPublished: boolean;
}

export class PageController {

    async getPages(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {pages} = await GetMyPages(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                pages
            },
        }
        return response.json(
            responseData
        )
    }

    async createPage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {page} = await CreatePage(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                page
            },
        }
        return response.json(
            responseData
        )
    }

    async updatePage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const page = await UpdatePage(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                page
            },
        }
        return response.json(
            responseData
        )
    }

    async deletePage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeletePage(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getPage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {page} = await GetMyPage(authInfo.userId, request.params.id, request.params.pageId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                page
            },
        }
        return response.json(
            responseData
        )
    }

    async addPermission(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {permissions} = await InviteToPage(authInfo.userId, request.params.id, request.params.pageId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                permissions
            },
        }
        return response.json(
            responseData
        )
    }

    async updatePermission(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await UpdateAccessToPage(authInfo.userId, request.params.id, request.params.pageId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async deletePermission(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await RemoveAccessToPage(authInfo.userId, request.params.id, request.params.pageId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async addView(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {view} = await AddPageView(authInfo.userId, request.params.id, request.params.pageId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                view
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteView(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeletePageView(authInfo.userId, request.params.id, request.params.pageId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async updateView(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const data: UpdateViewRequestData = request.body
        if (data.isPublished) await PublishPageView(authInfo.userId, request.params.id, request.params.pageId, request.body)
        else await UnPublishPageView(authInfo.userId, request.params.id, request.params.pageId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async duplicatePage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {page} = await DuplicateMyPage(authInfo.userId, request.params.id, request.params.pageId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                page
            },
        }
        return response.json(
            responseData
        )
    }

    async duplicateView(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {view} = await DuplicateView(authInfo.userId, request.params.id, request.params.pageId, request.params.viewId)


        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                view
            },
        }
        return response.json(
            responseData
        )
    }


}