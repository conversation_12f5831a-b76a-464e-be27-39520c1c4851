import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";
import {WorkspaceIntegrationConnection} from "../entity/workspaceIntegrationConnection";

export class WorkspaceIntegrationConnectionService extends BaseService<WorkspaceIntegrationConnection> {

    initRepository = (): Repository<WorkspaceIntegrationConnection> => {
        return getRepository(WorkspaceIntegrationConnection);
    }


}
