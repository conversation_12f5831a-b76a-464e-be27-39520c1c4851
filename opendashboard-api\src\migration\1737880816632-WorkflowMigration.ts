import { MigrationInterface, Query<PERSON>unner } from "typeorm";
import {DocumentType} from "../entity/documentHistory";

export class WorkflowMigration1737880816632 implements MigrationInterface {
    name = 'WorkflowMigration1737880816632'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`workspace_secret\` (\`id\` int NOT NULL AUTO_INCREMENT, \`workspaceId\` varchar(255) NOT NULL, \`type\` varchar(255) NOT NULL DEFAULT 'active', \`name\` varchar(255) NULL, \`value\` varchar(255) NULL, \`createdById\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_2e22db2595e74900d7e99a3fcd\` (\`workspaceId\`), INDEX \`IDX_45cbfd38b9674d17f638c64d0a\` (\`type\`), INDEX \`IDX_f88588165ab16aede855c6b9c3\` (\`name\`), UNIQUE INDEX \`IDX_d951d3017f189b909627b54beb\` (\`workspaceId\`, \`type\`, \`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workflow_analytic\` (\`id\` int NOT NULL AUTO_INCREMENT, \`hour\` int NOT NULL, \`day\` int NOT NULL, \`month\` int NOT NULL, \`year\` int NOT NULL, \`inboundId\` int NOT NULL DEFAULT '0', \`outboundId\` int NOT NULL DEFAULT '0', \`locationId\` int NOT NULL DEFAULT '0', \`workflowId\` varchar(255) NULL, \`instanceId\` int NOT NULL DEFAULT '0', \`taskId\` int NOT NULL DEFAULT '0', \`nodeId\` varchar(255) NULL, \`pageViews\` int NOT NULL DEFAULT '0', \`clickCount\` int NOT NULL DEFAULT '0', \`unsubscribeCount\` int NOT NULL DEFAULT '0', INDEX \`IDX_b51046be0b2104f76a2bf932a9\` (\`hour\`), INDEX \`IDX_dac2881a1dd052aa10a90f3cfe\` (\`day\`), INDEX \`IDX_bdee5ce130c08402ef11069717\` (\`month\`), INDEX \`IDX_ff6416a99edd4cb040e835c986\` (\`year\`), INDEX \`IDX_0a8cfef942b5cd1a103e365ce9\` (\`inboundId\`), INDEX \`IDX_bd73b7fb815e191ddb818aceb3\` (\`outboundId\`), INDEX \`IDX_288c9f5aa58de89c1212ae0ad2\` (\`locationId\`), INDEX \`IDX_989fd04e14e4d613d6e2832f63\` (\`workflowId\`), INDEX \`IDX_0833c9d4510f7a6bb6459dfda1\` (\`instanceId\`), INDEX \`IDX_e90dae12246bcfaf78aa5433ed\` (\`taskId\`), INDEX \`IDX_1178f40197ab316884093b182f\` (\`nodeId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workflow_task\` (\`id\` varchar(36) NOT NULL, \`workflowId\` varchar(255) NOT NULL, \`instanceId\` varchar(255) NOT NULL, \`nodeId\` varchar(255) NOT NULL, \`executeAt\` timestamp NULL, \`taskOutput\` json NULL, \`status\` varchar(255) NOT NULL DEFAULT 'scheduled', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`startedAt\` timestamp NULL, \`completedAt\` timestamp NULL, \`taskInput\` json NULL, \`meta\` json NULL, \`error\` varchar(255) NULL, INDEX \`IDX_108c790eb81b2cc2f7d9de5e45\` (\`workflowId\`), INDEX \`IDX_a3d1d3d486fae6ced680a63198\` (\`instanceId\`), INDEX \`IDX_6adde71185fb9148d1b51271f5\` (\`nodeId\`), INDEX \`IDX_6f9f36949cccb1e270e15c0c78\` (\`executeAt\`), INDEX \`IDX_702006efdd5a67aca21452a496\` (\`createdAt\`), INDEX \`IDX_85211d85df0a9890bce5b1ec1c\` (\`deletedAt\`), INDEX \`IDX_dceb240621a339c438b82517c2\` (\`startedAt\`), INDEX \`IDX_c84d8d69e9fc0e77872eda2216\` (\`completedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workflow\` (\`id\` varchar(36) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`name\` varchar(255) NULL, \`description\` varchar(255) NULL, \`nodeCount\` int NOT NULL DEFAULT '0', \`nodesList\` json NULL, \`triggerType\` varchar(255) NULL, \`triggerObjectId\` varchar(255) NULL, \`definition\` json NULL, \`draftDefinition\` json NULL, \`status\` varchar(255) NOT NULL DEFAULT 'draft', \`meta\` json NULL, \`createdById\` varchar(36) NULL, \`updatedById\` varchar(36) NULL, \`templateReleaseId\` int NULL, \`publishedAt\` timestamp NULL, \`lastExecutedAt\` timestamp NULL, \`lastResultAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`auditLog\` json NULL, INDEX \`IDX_fbce9a986a577698821a7e301b\` (\`workspaceId\`), INDEX \`IDX_8ec5afd3566bb368910c59f441\` (\`name\`), INDEX \`IDX_110bf92bbb944c477794f5e1f0\` (\`nodeCount\`), INDEX \`IDX_dc9f21e530d0e00bc1bcbe3912\` (\`triggerType\`), INDEX \`IDX_caa81e2bae39af871dc0785066\` (\`triggerObjectId\`), INDEX \`IDX_0b0de9cf6dda31444bb30bfad4\` (\`createdById\`), INDEX \`IDX_6bf40e9e4b1a123229519c75fb\` (\`updatedById\`), INDEX \`IDX_cd4216a4c6a8f91c801e156bc2\` (\`templateReleaseId\`), INDEX \`IDX_aa6ef686176028db3cfd2104e7\` (\`publishedAt\`), INDEX \`IDX_9fee262c44a37840e81fa72db9\` (\`lastExecutedAt\`), INDEX \`IDX_d786951ca088744d70bc8e2120\` (\`lastResultAt\`), INDEX \`IDX_0de09bc7359d53c9862b9b5b22\` (\`createdAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`workflow_instance\` (\`id\` varchar(36) NOT NULL, \`workflowId\` varchar(255) NOT NULL, \`databaseId\` varchar(255) NULL, \`recordId\` varchar(255) NULL, \`status\` varchar(255) NOT NULL DEFAULT 'active', \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`startedAt\` timestamp NULL, \`completedAt\` timestamp NULL, \`meta\` json NULL, INDEX \`IDX_85fab878fa62504ba5b1299a43\` (\`workflowId\`), INDEX \`IDX_dc6537527dd8e4ee31146d1aa3\` (\`databaseId\`), INDEX \`IDX_960140f335aed4179d5a93c517\` (\`recordId\`), INDEX \`IDX_ea606a3b1a3efa7c3b0106fa7c\` (\`createdAt\`), INDEX \`IDX_c9554477e7d0dd0bc993c1bf7d\` (\`deletedAt\`), INDEX \`IDX_033031001fb448a4d1f328ddd9\` (\`startedAt\`), INDEX \`IDX_d5308bbadc086263cf4114c45d\` (\`completedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`document_history\` ADD \`type\` varchar(255) NULL DEFAULT 'document'`);

        await queryRunner.query(`ALTER TABLE \`push_registration\` DROP \`refreshedAt\``);
        await queryRunner.query(`ALTER TABLE \`push_registration\` ADD \`refreshedAt\` TIMESTAMP NULL AFTER \`provider\``);
        await queryRunner.query(`UPDATE \`push_registration\` SET refreshedAt=updatedAt`);
        await queryRunner.query(`UPDATE \`document_history\` SET type=? WHERE type IS NULL`, [DocumentType.Document]);

        await queryRunner.query(`CREATE INDEX \`IDX_434dff27a71545957994f90cdb\` ON \`push_registration\` (\`refreshedAt\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_aa0ea23fda8184a68450065dcf\` ON \`document_history\` (\`type\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_aa0ea23fda8184a68450065dcf\` ON \`document_history\``);
        await queryRunner.query(`DROP INDEX \`IDX_434dff27a71545957994f90cdb\` ON \`push_registration\``);
        await queryRunner.query(`ALTER TABLE \`document_history\` DROP COLUMN \`type\``);
        await queryRunner.query(`DROP INDEX \`IDX_d5308bbadc086263cf4114c45d\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_033031001fb448a4d1f328ddd9\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_c9554477e7d0dd0bc993c1bf7d\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_ea606a3b1a3efa7c3b0106fa7c\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_960140f335aed4179d5a93c517\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_dc6537527dd8e4ee31146d1aa3\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_85fab878fa62504ba5b1299a43\` ON \`workflow_instance\``);
        await queryRunner.query(`DROP TABLE \`workflow_instance\``);
        await queryRunner.query(`DROP INDEX \`IDX_0de09bc7359d53c9862b9b5b22\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_d786951ca088744d70bc8e2120\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_9fee262c44a37840e81fa72db9\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_aa6ef686176028db3cfd2104e7\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_cd4216a4c6a8f91c801e156bc2\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_6bf40e9e4b1a123229519c75fb\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_0b0de9cf6dda31444bb30bfad4\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_caa81e2bae39af871dc0785066\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_dc9f21e530d0e00bc1bcbe3912\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_110bf92bbb944c477794f5e1f0\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_8ec5afd3566bb368910c59f441\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_fbce9a986a577698821a7e301b\` ON \`workflow\``);
        await queryRunner.query(`DROP TABLE \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_c84d8d69e9fc0e77872eda2216\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_dceb240621a339c438b82517c2\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_85211d85df0a9890bce5b1ec1c\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_702006efdd5a67aca21452a496\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_6f9f36949cccb1e270e15c0c78\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_6adde71185fb9148d1b51271f5\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_a3d1d3d486fae6ced680a63198\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_108c790eb81b2cc2f7d9de5e45\` ON \`workflow_task\``);
        await queryRunner.query(`DROP TABLE \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_1178f40197ab316884093b182f\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_e90dae12246bcfaf78aa5433ed\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_0833c9d4510f7a6bb6459dfda1\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_989fd04e14e4d613d6e2832f63\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_288c9f5aa58de89c1212ae0ad2\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_bd73b7fb815e191ddb818aceb3\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_0a8cfef942b5cd1a103e365ce9\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_ff6416a99edd4cb040e835c986\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_bdee5ce130c08402ef11069717\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_dac2881a1dd052aa10a90f3cfe\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_b51046be0b2104f76a2bf932a9\` ON \`workflow_analytic\``);
        await queryRunner.query(`DROP TABLE \`workflow_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_d951d3017f189b909627b54beb\` ON \`workspace_secret\``);
        await queryRunner.query(`DROP INDEX \`IDX_f88588165ab16aede855c6b9c3\` ON \`workspace_secret\``);
        await queryRunner.query(`DROP INDEX \`IDX_45cbfd38b9674d17f638c64d0a\` ON \`workspace_secret\``);
        await queryRunner.query(`DROP INDEX \`IDX_2e22db2595e74900d7e99a3fcd\` ON \`workspace_secret\``);
        await queryRunner.query(`DROP TABLE \`workspace_secret\``);
    }

}
