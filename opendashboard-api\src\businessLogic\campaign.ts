import {AdjacentDatabases, GetRecords, performRecordUpdate, RecordIdMap, resolveDatabaseAccessLevel} from "./database";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, UnauthorizedError} from "../errors/AppError";
import {getCompanyDbDefinition, getCustomerDbDefinition, getDatabasePackageName} from "opendb-app-db-utils/lib/utils/onboarding";
import {CampaignService, CreateCampaignServiceData} from "../service/campaign";
import {Campaign, CampaignAttachment, CampaignStatus, CampaignType, SequenceTarget} from "../entity/Campaign";
import {DatabaseService} from "../service/database";
import {RecordService, UpdateRecordData} from "../service/record";
import {DatabaseColumnReturnValue, DatabaseFieldDataType, DbRecordFilter, LinkedColumn, Match, ProcessedDbRecord, RecordValues, TagColumnDbValue, TextColumnDbValue} from "opendb-app-db-utils/lib/typings/db";
import {Record} from "../entity/Record";
import {GetMyWorkspace, getWorkspaceMembersAsPersons} from "./workspace";
import {CampaignEmailService, CreateCampaignEmailData} from "../service/campaignEmailService";
import {CampaignAnalyticsData, CampaignAnalyticService} from "../service/campaignAnalyticService";
import {CampaignEmail, CampaignEmailStatus} from "../entity/CampaignEmail";
import {Database} from "../entity/Database";
import {CampaignAnalytic} from "../entity/CampaignAnalytic";
import {WorkspaceSenderService} from "../service/workspaceSenderEmail";
import {WorkspaceDomainService} from "../service/workspaceDomain";
import {EmailUser} from "./email";
import {LinkedDatabases, recordValueToText, transformRawRecords} from "opendb-app-db-utils/lib/utils/db";
import {SubstituteVarData, substituteVars} from "opendb-app-db-utils/lib/methods/object";
import {validateEmail} from "../utility/validator";
import {initDateWithTimezone, isDateObjValid} from "opendb-app-db-utils/lib";
import {In} from "typeorm";
import {redisPubSubPublish} from "../redis-pubsub/publisher";
import {LinkService} from "../service/link";
import {Link} from "../entity/Link";

const crypto = require("crypto")

export enum TargetEntityScope {
    All = 'all',
    First = 'first',
}

export interface CreateCampaignData {
    databaseId: string;
    targetColId?: string;
    recordIds: string[];
    targetEntityScope?: TargetEntityScope
}

export const createCampaign = async (userId: string, workspaceId: string, data: CreateCampaignData) => {
    let {databaseId, recordIds, targetColId} = data
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)

    if (!resolve) {
        throw new NotfoundError(ErrorMessage.AssocEntityNotFound)
    }
    const {database} = resolve
    const companyDef = getCompanyDbDefinition()
    const contactsDef = getCustomerDbDefinition("")

    const dS = new DatabaseService()
    const rS = new RecordService()
    const cS = new CampaignService()

    const readOnlyDbs = [
        getDatabasePackageName(companyDef),
        getDatabasePackageName(contactsDef),
    ]
    const isTargetLinked = !readOnlyDbs.includes(database.srcPackageName) && !database.isMessagingEnabled

    const createData: CreateCampaignServiceData = {
        targetDatabaseId: "",
        targetRecords: [],
        createdById: userId,
        databaseId,
        targetColId,
        recordIds: [],
        type: CampaignType.Campaign,
        updatedById: userId, workspaceId
    }
    let targetDatabaseId = ''
    if (isTargetLinked) {
        if (!targetColId) {
            throw new RequiredParameterError("data.targetColId")
        }
        const column = database.definition.columnsMap[targetColId]
        if (column.type !== DatabaseFieldDataType.Linked) {
            throw new BadRequestError(ErrorMessage.AssocEntityNotFound)
        }
        const linkedDbId = (column as LinkedColumn).databaseId

        const linkedDatabase = await dS.findOne({workspaceId, id: linkedDbId})
        if (!linkedDatabase) throw new BadRequestError(ErrorMessage.AssocEntityNotFound)

        targetDatabaseId = linkedDbId
    } else {
        targetDatabaseId = databaseId
    }
    const records = await rS.find({databaseId})
    const recordsMap: { [id: string]: Record } = {}

    for (let record of records) {
        recordsMap[record.id] = record
    }
    if (recordIds && Array.isArray(recordIds)) {
        for (const id of recordIds) {
            if (!recordsMap[id]) throw new BadRequestError(ErrorMessage.AssocRecordNotFound)
        }
    } else {
        recordIds = Object.keys(recordsMap)
    }
    const targets: {
        recordId: string,
        targetRecordId: string
    }[] = []

    if (isTargetLinked) {
        const targetRecords = await rS.find({databaseId: createData.targetDatabaseId})
        const targetRecordsMap: { [recordId: string]: Record } = {}

        for (const targetRecord of targetRecords) {
            targetRecordsMap[targetRecord.id] = targetRecord
        }
        for (const id of recordIds) {
            const columnValue = recordsMap[id].recordValues[targetColId] as TagColumnDbValue | TextColumnDbValue

            const targetIds: string[] = []
            if (typeof columnValue === 'string') {
                targetIds.push(columnValue)
            } else if (Array.isArray(columnValue) && columnValue.length > 0 && typeof columnValue[0] === 'string') {
                targetIds.push(...columnValue)
            }
            for (const targetId of targetIds) {
                if (targetRecordsMap[targetId]) targets.push({targetRecordId: targetId, recordId: id})
            }
        }
    } else {
        for (let recordId of recordIds) {
            targets.push({targetRecordId: recordId, recordId})
        }
    }

    createData.targetRecords = targets
    createData.targetDatabaseId = targetDatabaseId
    createData.recordIds = recordIds

    const campaign = await cS.create(createData)

    return {campaign}
}

export interface CampaignWithStatsAndDatabase extends CampaignStats {
    totalCampaigns: number
    sentCampaigns: number
    database: { id: string, name: string }
}

export interface GetCampaignsResponse {
    campaigns: CampaignWithStatsAndDatabase[]
}

export const getCampaigns = async (userId: string, workspaceId: string): Promise<GetCampaignsResponse> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.UnableToAuthorize)
    }

    const cS = new CampaignService()
    const dS = new DatabaseService()
    const allCampaigns = await cS.find({workspaceId, type: CampaignType.Campaign}, {createdAt: 'DESC'})

    const ids = allCampaigns.map(c => c.id)
    const databaseIds = allCampaigns.map(c => c.databaseId)
    const databases = await dS.find({id: In(databaseIds)})

    const databaseMap: { [id: string]: Database } = {}
    for (let database of databases) {
        databaseMap[database.id] = database
    }

    const campaignsMap: { [id: string]: CampaignWithStatsAndDatabase } = {}

    for (let c of allCampaigns) {
        const db = databaseMap[c.databaseId]
        campaignsMap[c.id] = {
            bounced: 0, clicked: 0, emails: 0, opened: 0, sent: 0, sentCampaigns: 0, totalCampaigns: 0, unsubscribed: 0,
            campaign: c,
            database: {id: c.databaseId, name: db?.name}
        }
    }
    const cStats = await getCampaignStats(ids)
    const statsList = Object.values(cStats)

    for (let campaignStat of statsList) {
        for (const id of [campaignStat.campaign.id, campaignStat.campaign.parentId]) {
            if (!id) continue
            if (!campaignsMap[id]) continue

            campaignsMap[id].emails = campaignsMap[id].emails + campaignStat.emails
            campaignsMap[id].sent = campaignsMap[id].sent + campaignStat.sent
            campaignsMap[id].opened = campaignsMap[id].opened + campaignStat.opened
            campaignsMap[id].clicked = campaignsMap[id].clicked + campaignStat.clicked
            campaignsMap[id].bounced = campaignsMap[id].bounced + campaignStat.bounced
            campaignsMap[id].unsubscribed = campaignsMap[id].unsubscribed + campaignStat.unsubscribed

            campaignsMap[id].totalCampaigns++
            if (campaignStat.campaign.status === CampaignStatus.Sent) campaignsMap[id].sentCampaigns++
        }
    }

    const campaigns = Object.values(campaignsMap)

    return {
        campaigns
    }
}

export interface GetCampaignResponse {
    campaign: Campaign,
    statsMap: CampaignStatsMap
    emailsMap: CampaignEmailsMap
    analytics: CampaignAnalytic[]
    records: Record[]
    links: Link[]
    database: Database
    adjacentDatabases: AdjacentDatabases
}

export const getCampaign = async (userId: string, workspaceId: string, campaignId: string): Promise<GetCampaignResponse> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.UnableToAuthorize)
    }
    const cS = new CampaignService()

    const campaign = await cS.findOne({workspaceId, id: campaignId})
    if (!campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const recordIds = campaign.recordIds

    const res = await GetRecords(userId, workspaceId, campaign.databaseId, recordIds, true)
    if (!res) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)
    const {records, database, adjacentDatabases} = res

    const analytics: CampaignAnalytic[] = await getCampaignAnalytics([campaignId])
    const statsMap = await getCampaignStats([campaignId])
    const emailsMap = await getCampaignEmails(campaignId)

    const linkIds = analytics.map(a => a.linkId).filter(id => id > 0)
    let links: Link[] = []

    if (linkIds.length > 0) {
        const lS = new LinkService()
        links = await lS.find({id: In(linkIds)})
    }

    return {
        campaign, statsMap, analytics, records, database, adjacentDatabases, emailsMap, links
    }
}

export interface CampaignStats {
    campaign: Campaign
    emails: number
    sent: number
    opened: number
    clicked: number
    bounced: number
    unsubscribed: number
}

export interface CampaignStatsMap {
    [id: string]: CampaignStats
}

export const getCampaignStats = async (ids: string[]): Promise<CampaignStatsMap> => {
    const campaignStatsMap: CampaignStatsMap = {}
    if (ids.length === 0) return campaignStatsMap

    const cS = new CampaignService();

    const statsQuery = cS.getRepository().createQueryBuilder('c')
        .select('c')
        .addSelect('COUNT(cE.id) AS emails')
        .addSelect('SUM(cE.isSent) AS sent')
        .addSelect('SUM(cE.isBounced) AS bounced')
        .addSelect('SUM(cE.isUnsubscribed) AS unsubscribed')
        .addSelect(qb => {
            return qb.select("CONCAT(SUM(openCount), '|', SUM(clickCount))", 'campaign_stats')
                .from(CampaignAnalytic, "cA")
                .where('cA.campaignId=c.id')
                .groupBy("cA.campaignId")
        }, 'campaign_stats')
        .leftJoin(CampaignEmail, 'cE', 'c.id = cE.campaignId')
        .where('c.id IN(:...ids) OR c.parentId IN (:...ids)', {ids})
        .groupBy('c.id');

    const rawResults = await statsQuery.getRawMany()

    for (let row of rawResults) {
        const {emails, sent, bounced, unsubscribed} = row

        const campaign: Campaign = {} as Campaign
        for (let key of Object.keys(row)) {
            const value = row[key]

            if (!value) continue

            if (key.includes('c_')) {
                const rKey = key.replace('c_', '')
                if (key.includes('At')) {
                    campaign[rKey] = new Date(value)
                } else {
                    campaign[rKey] = value
                }
            }
        }
        const {id, parentId} = campaign

        const stats = (row['campaign_stats'] || '').toString('utf8').split('|')

        campaignStatsMap[id] = {
            campaign,
            emails: Number(emails) || 0,
            sent: Number(sent) || 0,
            opened: Number(stats[0]) || 0,
            clicked: Number(stats[1]) || 0,
            bounced: Number(bounced) || 0,
            unsubscribed: Number(unsubscribed) || 0,
        }
    }
    return campaignStatsMap;
}

export const getCampaignAnalytics = async (ids: string[]) => {
    if (ids.length === 0) return []
    const subQ = new CampaignService().getRepository()
        .createQueryBuilder()
        .select('id')
        .where({id: In(ids)})
        .orWhere({parentId: In(ids)})

    const cAS = new CampaignAnalyticService()
    // const analytics = await cAS.find({campaignId: In(ids)})
    const qB = cAS.getRepository().createQueryBuilder()
        .select()
        .where(`campaignId IN (${subQ.getQuery()})`)
        .setParameters(subQ.getParameters())

    return await qB.getMany()
}

export interface CampaignEmailsMap {
    [id: string]: CampaignEmail[]
}

export const getCampaignEmails = async (id: string) => {
    const cES = new CampaignEmailService();

    const subQuery = new CampaignService().getRepository()
        .createQueryBuilder("c")
        .select("id")
        .where("parentId = :id", {id});

    const qB = cES.getRepository().createQueryBuilder("cE")
        .select()
        .where({campaignId: id})
        .orWhere(`cE.campaignId IN (${subQuery.getQuery()})`)
        .setParameters(subQuery.getParameters())

    const emails = await qB.getMany();

    const emailsMap: CampaignEmailsMap = {}
    for (let email of emails) {
        const campaignId = email.campaignId
        if (!emailsMap[campaignId]) {
            emailsMap[campaignId] = []
        }
        emailsMap[campaignId].push(email)
    }
    return emailsMap
}

export interface UpdateCampaignData {
    senderId: string,
    subject: string
    contentText: string
    cc: string[]
    bcc: string[]
    attachments: CampaignAttachment[]
}

export const updateCampaign = async (userId: string, workspaceId: string, campaignId: string, data: UpdateCampaignData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.UnableToAuthorize)
    }
    let {senderId, subject, contentText, cc, bcc, attachments} = data

    if (senderId) {
        const wS = new WorkspaceSenderService()
        const sender = await wS.findOne(({workspaceId, id: Number(senderId)}))
        if (!sender) {
            throw new BadRequestError(ErrorMessage.AssocEntityNotFound)
        }
    }

    cc = Array.isArray(cc) ? cc : []
    bcc = Array.isArray(bcc) ? bcc : []
    attachments = Array.isArray(bcc) ? attachments : []
    subject = subject || ''
    contentText = contentText || ''

    const cS = new CampaignService()
    const campaign = await cS.findOne({workspaceId, id: campaignId})
    if (!campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (campaign.status !== CampaignStatus.Draft) throw new NotfoundError(ErrorMessage.CampaignNotInDraft)

    const update: Partial<Campaign> = {
        cc, bcc, subject, contentText, attachments, senderId
    }
    await cS.update({id: campaignId}, update)

    return true
}

export const deleteCampaign = async (userId: string, workspaceId: string, campaignId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.UnableToAuthorize)
    }

    const cS = new CampaignService()
    await cS.remove({workspaceId, id: campaignId})

    return true
}

export const reviewCampaign = async (userId: string, workspaceId: string, campaignId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const companyDef = getCompanyDbDefinition()
    const contactsDef = getCustomerDbDefinition("")

    const cS = new CampaignService()
    const cES = new CampaignEmailService()

    const campaign = await cS.findOne({workspaceId, id: campaignId})
    if (!campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (campaign.status !== CampaignStatus.Draft) {
        throw new NotfoundError(ErrorMessage.CampaignNotInDraft)
    }

    const {senderId, subject, contentText, attachments, bcc, cc, targetColId, targetDatabaseId, targetRecordIdsToSkip, targetRecords, databaseId} = campaign

    const {sender} = await verifySender(workspaceId, senderId);

    const {database, adjacentDatabases, records} = await GetRecords(userId, workspaceId, databaseId, campaign.recordIds, true)

    const persons = await getWorkspaceMembersAsPersons(workspaceId)

    const linkedDatabases: LinkedDatabases = {}

    for (const {database, page, recordsMap, error} of Object.values(adjacentDatabases)) {
        if (database && recordsMap) {
            linkedDatabases[database.id] = {
                id: database.id,
                definition: database.definition,
                recordsMap: {},
                srcPackageName: database.srcPackageName
            }
            for (let r of Object.values(recordsMap)) {
                linkedDatabases[database.id].recordsMap[r.record.id] = r.record
            }
        }
    }
    const processedRecords = transformRawRecords(
        database.definition,
        records,
        persons,
        linkedDatabases
    )
    const recordsMap: RecordIdMap = {}
    for (let i = 0; i < records.length; i++) {
        const record = records[i]
        const processedRecord = processedRecords[i]

        recordsMap[record.id] = {
            record, processedRecord
        }
    }

    let targetDatabase: Database;
    let targetRecordsMap: RecordIdMap = {}
    if (!targetDatabaseId || targetDatabaseId === databaseId) {
        targetDatabase = database
        targetRecordsMap = recordsMap
    } else {
        if (!adjacentDatabases[targetDatabaseId]) {
            throw new BadRequestError(ErrorMessage.TargetDatabaseNotFound)
        }
        targetDatabase = adjacentDatabases[targetDatabaseId].database as Database
        targetRecordsMap = adjacentDatabases[targetDatabaseId].recordsMap as RecordIdMap
    }

    const emailFrom: EmailUser = {
        email: sender.email,
        name: sender.name
    }
    const isEmailToContacts = targetDatabase.srcPackageName === getDatabasePackageName(contactsDef) || targetDatabase.isMessagingEnabled

    const emailDatas: CreateCampaignEmailData[] = []
    for (const {recordId, targetRecordId} of targetRecords) {
        const target = targetRecordsMap[targetRecordId]
        const record = recordsMap[recordId]


        let emailTo: EmailUser = {
            email: '', name: ''
        }
        const tags: SubstituteVarData = {}


        let skippedReason = ''
        let isSkipped = false

        if (target) {
            const email = String(recordValueToText(target.record.recordValues['email'] as DatabaseColumnReturnValue))
            let name = ''
            if (isEmailToContacts) {
                const firstName = `${String(recordValueToText(target.record.recordValues['firstName'] as DatabaseColumnReturnValue))}`
                const lastName = `${String(recordValueToText(target.record.recordValues['lastName'] as DatabaseColumnReturnValue))}`
                name = `${firstName} ${lastName}`.trim()
            } else {
                name = `${String(recordValueToText(target.record.recordValues['name'] as DatabaseColumnReturnValue))}`
            }
            emailTo.email = email
            emailTo.name = name

            const unsubscribed = !!target.record.recordValues['unsubscribed']
            const bounced = !!target.record.recordValues['bounced']

            if (unsubscribed) {
                isSkipped = true
                skippedReason = 'The recipient has unsubscribed from your emails.'
            } else if (bounced) {
                isSkipped = true
                skippedReason = 'Delivery failed previously. No further emails can be sent.'
            }

        }
        if (!emailTo.email) {
            skippedReason = ErrorMessage.NoEmailFound
            isSkipped = true
        } else if (!validateEmail(emailTo.email)) {
            skippedReason = ErrorMessage.EmailIsInvalid
            isSkipped = true
        }
        if (record) {
            const processedRecord = record.processedRecord as ProcessedDbRecord
            for (let key of Object.keys(processedRecord.processedRecordValues)) {
                const value = processedRecord.processedRecordValues[key]
                tags[key] = recordValueToText(value)
            }
        }
        const targetSubject = substituteVars(subject, tags, undefined, 'curly')
        const targetContent = substituteVars(contentText, tags, undefined, 'curly')
        const status: CampaignEmailStatus = CampaignEmailStatus.InReview

        const emailData: CreateCampaignEmailData = {
            campaignId,
            targetRecordId,
            recordId,
            status,
            skippedReason,
            emailTo,
            emailFrom,
            cc,
            bcc,
            isSkipped,
            attachments,
            subject: targetSubject,
            contentText: targetContent,
        }
        emailDatas.push(emailData)
    }

    const emails = await cES.batchAdd(emailDatas)

    await cS.update({workspaceId, id: campaignId}, {status: CampaignStatus.InReview})

    campaign.status = CampaignStatus.InReview

    return {
        emails
    }
}

export const removeCampaignReview = async (userId: string, workspaceId: string, campaignId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    const cS = new CampaignService()
    const cES = new CampaignEmailService()

    const campaign = await cS.findOne({workspaceId, id: campaignId})
    if (!campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (campaign.status !== CampaignStatus.InReview) {
        throw new BadRequestError(ErrorMessage.CampaignNotInReview)
    }
    await cES.hardRemove({campaignId})
    await cS.update({id: campaignId}, {status: CampaignStatus.Draft})

    return {}
}

export interface updateCampaignEmailData {
    subject: string,
    contentText: string
}

export const updateCampaignEmail = async (userId: string, workspaceId: string, campaignId: string, emailId: string, data: updateCampaignEmailData) => {
    const {subject, contentText} = data
    if (!subject) throw new RequiredParameterError("subject")
    if (!contentText) throw new RequiredParameterError("contentText")
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const cS = new CampaignService()
    const cES = new CampaignEmailService()

    const campaign = await cS.findOne({workspaceId, id: campaignId})
    if (!campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (campaign.status !== CampaignStatus.InReview) {
        throw new BadRequestError(ErrorMessage.CampaignNotInReview)
    }
    const email = await cES.update({id: Number(emailId), campaignId}, {subject, contentText})

    return {}
}

export interface PublishCampaignData {
    sendAt: 'now' | 'string'
    sequenceRecordFilter?: DbRecordFilter
    sequenceTarget?: SequenceTarget
}

const verifySender = async (workspaceId: string, senderId: string) => {
    const wS = new WorkspaceSenderService()
    const wDS = new WorkspaceDomainService()

    if (!senderId) {
        throw new RequiredParameterError("Sender")
    }
    const sender = await wS.findOne({workspaceId, id: Number(senderId)})
    if (!sender || !sender.isVerified) {
        throw new BadRequestError(ErrorMessage.SenderNotVerified)
    }
    const domain = await wDS.findOne({workspaceId, id: sender.workspaceDomainId})
    if (!domain || !domain.isVerified) {
        throw new BadRequestError(ErrorMessage.DomainNotVerifiedForSending)
    }
    return {
        sender, domain
    }
}

export const publishCampaign = async (userId: string, workspaceId: string, campaignId: string, data: PublishCampaignData) => {
    const {sendAt} = data
    if (!sendAt) throw new RequiredParameterError("sendAt")


    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    let deliverAt: Date = new Date()

    if (sendAt !== 'now') {
        deliverAt = initDateWithTimezone(sendAt, member.workspace.timezone)
        // const date = new Date(sendAt)
        if (!isDateObjValid(deliverAt)) {
            throw new InvalidParameterError('sendAt')
        }
    }
    const cS = new CampaignService()

    const campaign = await cS.findOne({workspaceId, id: campaignId})
    if (!campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (campaign.status !== CampaignStatus.InReview && campaign.status !== CampaignStatus.Draft) {
        throw new BadRequestError(ErrorMessage.CampaignNotInReviewOrDraft)
    }
    const {senderId} = campaign

    const {sender} = await verifySender(workspaceId, senderId);

    const update: Partial<Campaign> = {
        deliverAt,
        sendAtLocalTime: sendAt,
        status: sendAt === 'now' ? CampaignStatus.Published : CampaignStatus.Scheduled
    }
    if (campaign.type === CampaignType.Sequence) {
        update.sequenceTarget = data.sequenceTarget || SequenceTarget.All
        update.sequenceRecordFilter = data.sequenceRecordFilter || {conditions: [], match: Match.All}

        const originalCampaign = await cS.findOne({workspaceId, id: campaign.parentId})
        if (!originalCampaign) {
            throw new NotfoundError(ErrorMessage.AssocEntityNotFound)
        }
        if (sendAt === 'now' && originalCampaign.status !== CampaignStatus.Sent) {
            throw new BadRequestError(ErrorMessage.UnableToSendOriginalCampaignNotSent)
        }
        const originalDeliverAt = originalCampaign.deliverAt
        if (deliverAt.getTime() < originalDeliverAt.getTime()) {
            throw new BadRequestError(ErrorMessage.UnableToScheduleCannotBeEarlierThanOriginalCampaign)
        }
    }

    await cS.update({id: campaignId, workspaceId}, update)

    if (sendAt === 'now') {
        setTimeout(() => {
            // delay for one second to allow the workflow instance to be created
            redisPubSubPublish('published-campaign-queue', campaignId)
        }, 1000)
    }

    return {
        campaign: {...campaign, ...update}
    }
}

export const createSequence = async (userId: string, workspaceId: string, campaignId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const cS = new CampaignService()
    const parentCampaign = await cS.findOne({workspaceId, id: campaignId})
    if (!parentCampaign || parentCampaign.parentId || parentCampaign.type !== CampaignType.Campaign) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const {targetDatabaseId, targetRecords, recordIds, targetColId, databaseId} = parentCampaign

    const createData: CreateCampaignServiceData = {
        targetDatabaseId,
        targetRecords,
        createdById: userId,
        recordIds,
        targetColId,
        databaseId,
        type: CampaignType.Sequence,
        updatedById: userId,
        workspaceId,
        parentId: parentCampaign.id
    }

    const campaign = await cS.create(createData)

    return {
        campaign
    }
}


export const getCampaignEmailFromTracking = async (eId: string, urlHash: string, salt: string) => {
    const cES = new CampaignEmailService()
    const email = await cES.findOne({id: Number(eId)})

    if (!email) return null

    const cS = new CampaignService()
    const campaign = await cS.findOne({id: email.campaignId})
    if (!campaign) return null

    const tsNow = email.createdAt.getTime()

    const tokenValue = `${salt}:${campaign.targetDatabaseId}:${email.targetRecordId}:${email.id}:${tsNow}`
    const hash = crypto.createHash('sha1').update(tokenValue).digest('hex')
    // const unsubscribeUrl = apiUrl(`/v0/unsubscribe?eId=${campaignEmail.id}&tS=${salt}&hash=${hash}`)
    // const redirectUrl = apiUrl(`/v0/redirect?eId=${campaignEmail.id}&tS=${salt}&hash=${hash}`)

    if (hash !== urlHash) {
        console.log({hash, urlHash})
        return null
    }

    return {
        email, campaign
    }
}

export const registerCampaignClick = async (eId: string, hash: string, salt: string, url: string) => {
    const emailResponse = await getCampaignEmailFromTracking(eId, hash, salt)
    if (!emailResponse) return false
    const {email} = emailResponse

    const lS = new LinkService()
    const cAS = new CampaignAnalyticService()
    const link = await lS.getLink(url)

    const data: CampaignAnalyticsData = {
        campaignId: email.campaignId, clickCount: 1, emailId: email.id, linkId: link.id, openCount: 0
    }
    await cAS.addAnalytics(data)

    return true

}

export const registerCampaignOpen = async (eId: string, hash: string, salt: string) => {
    const emailResponse = await getCampaignEmailFromTracking(eId, hash, salt)
    if (!emailResponse) return false
    const {email} = emailResponse

    const cAS = new CampaignAnalyticService()

    const data: CampaignAnalyticsData = {
        campaignId: email.campaignId, clickCount: 0, emailId: email.id, linkId: 0, openCount: 1
    }
    await cAS.addAnalytics(data)
    return true
}

export const registerUnsubscribe = async (eId: string, hash: string, salt: string) => {
    const emailResponse = await getCampaignEmailFromTracking(eId, hash, salt)
    if (!emailResponse) return false
    const {email, campaign} = emailResponse

    const cES = new CampaignEmailService()
    const dS = new DatabaseService()

    const database = await dS.findOne({id: campaign.targetDatabaseId})
    if (!database) return false

    const values: RecordValues = {}
    values['unsubscribed'] = true
    const data: UpdateRecordData = {
        updatedById: '',
        recordValues: values
    }
    await performRecordUpdate(database, [email.targetRecordId], data)

    await cES.update({id: email.id}, {isUnsubscribed: true, unsubscribedAt: new Date()})

    return true
}




