import {User} from "../../entity/User";
import {Workspace} from "../../entity/Workspace";
import {logInfo} from "../logtail";
import {sendHTTPRequest} from "../../utility/http";
import {ErrorMessage} from "../../errors/AppError";
import config from "../../config";
import {UserService} from "../../service/user";
import {WorkspaceService} from "../../service/workspace";


let isWorkflowsEnabled = false;

const workflowUrl = (id: string) => `https://api.inbranded.co/workflow/hook/${id}/catch`

export const setIsInbrandedWorkflowsEnabled = (val: boolean) => {
    isWorkflowsEnabled = val;
}

enum WorkflowIdsEnum {
    NewUser = 'd0f2e239-32dc-49cb-86af-d28511d499a6',
    NewWorkspace = '01c9e9dd-6253-405b-b344-302cd9c9e391',
    UserActivated = '04d1f1f8-eafc-4516-b0bd-58d14c260f01',
}

export const TriggerNewUserWorkflow = async (user: User) => {
    await sendToWorkflow('NewUser', user, {})
}

export const TriggerUserActivatedWorkflow = async (user: User) => {
    await sendToWorkflow('UserActivated', user, {})
}

export const TriggerNewWorkspaceWorkflow = async (user: User, workspace: Workspace) => {
    const data = {
        workspaceId: workspace.id,
        name: workspace.name
    }
    await sendToWorkflow('NewWorkspace', user, data)
}

const sendToWorkflow = async (workflow: keyof typeof WorkflowIdsEnum, user: User, data: any) => {
    const workflowId = WorkflowIdsEnum[workflow]
    if (!isWorkflowsEnabled) {
        logInfo(`Inbranded Workflow ${workflow} not triggered, isWorkflowsEnabled flag is false`, {
            isWorkflowsEnabled,
            workflowId,
            data,
            user,
            workflow
        })
        return
    }

    const url = workflowUrl(workflowId)

    const name = (`${user.firstName || ''} ${user.lastName || ''}`.trim() || ' ')
    const email = user.email
    const body = {
        name,
        email,
        data
    }
    const headers = {
        apikey: config.INBRANDED.api_key
    }

    const res = await sendHTTPRequest("post", url, headers, body)

    let error = ''
    if (!res.isSuccess) {
        error = res.data?.error || ErrorMessage.UnableToProcessRequest
    }

    logInfo(`Inbranded Workflow ${workflow} triggered ${error ? 'with error:' + error : ''}`, {
        isWorkflowsEnabled,
        workflowId,
        postBody: body,
        user,
        workflow,
        error,
        isSuccess: res.isSuccess,
    })

}

export const SendExistingDataToInbrandedWorkflows = async () => {
    const uS = new UserService()
    const users = await uS.find({})

    const userMap: { [id: string]: User } = {}
    for (let user of users) {
        userMap[user.id] = user
    }
    const wS = new WorkspaceService()
    const workspaces = await wS.find({})


    logInfo("Inbranded workflow: Send existing data", {
        usersCount: users.length,
        workspacesCount: workspaces.length
    })

    for (const user of users) {
        if (user.isSetupCompleted) {
            await TriggerUserActivatedWorkflow(user)
        } else {
            await TriggerNewUserWorkflow(user)
        }
    }
    for (const workspace of workspaces) {
        const user = userMap[workspace.createdById]
        if (!user) continue

        await TriggerNewWorkspaceWorkflow(user, workspace)
    }

    logInfo(`Inbranded workflow: Send existing data completed`, {
        usersCount: users.length,
        workspacesCount: workspaces.length
    })
}