import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

export enum DocumentType {
    Document = 'document',
    Workflow = 'workflow',
    Page = 'page',
}

@Entity()
export class DocumentHistory {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    documentId: string

    @Index()
    @Column({type: 'varchar', nullable: true, default: DocumentType.Document})
    type: DocumentType

    @Index()
    @Column({type: 'varchar', nullable: true})
    recordId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Column({type: 'json', nullable: true})
    contentJSON: object

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    contentText: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    updatedById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}