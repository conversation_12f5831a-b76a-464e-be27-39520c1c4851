import config from "../config";
import {sendHTTPRequest} from "../utility/http";
import {redisGet, redisSet} from "../connection/redis";


const rateCache = Object.freeze({
    fallbackRate: 1700,
    officialToParallelRatio: 1.1,
    live: {
        hours: 6,
        key: "__convert_rates__"
    },
    fallback: {
        hours: 24 * 30,
        key: "__convert_rates_fallback__"
    }
})

export const getUSDNGNRate = async () => {
    let rateData: ExchangeRateApiResponse = undefined
    const cacheRes = await redisGet(rateCache.live.key)
    if (cacheRes) {
        try {
            rateData = JSON.parse(cacheRes) as ExchangeRateApiResponse
        } catch (e) {
            // do nothing
        }
    }

    let isLive = false

    if (!rateData) {
        const rateResponse = await getExchangeRateApiRates()
        console.log({rateResponse})

        if (rateResponse.isSuccess) {
            rateData = rateResponse.data
            isLive = true


            await redisSet(rateCache.live.key, JSON.stringify(rateData), rateCache.live.hours * 60 * 60)
            await redisSet(rateCache.fallback.key, JSON.stringify(rateData), rateCache.fallback.hours * 60 * 60)

        } else {
            const cacheRes = await redisGet(rateCache.fallback.key)
            if (cacheRes) {
                try {
                    rateData = JSON.parse(cacheRes) as ExchangeRateApiResponse
                } catch (e) {
                    // do nothing
                }
            }
        }
    }
    let parallelRate = rateCache.fallbackRate
    if (rateData) {
        const rate = rateData.conversion_rate
        parallelRate = rate * rateCache.officialToParallelRatio
    }
    return {
        rate: parallelRate,
        isLive
    }
}

const apiKey = config.EXCHANGERATE_API.api_key


interface ExchangeRateApiResponse {
    base_code: string
    target_code: string
    conversion_rate: number
    result: 'success' | string
}

// {
//     "result": "success",
//     "documentation": "https://www.exchangerate-api.com/docs",
//     "terms_of_use": "https://www.exchangerate-api.com/terms",
//     "time_last_update_unix": 1734566401,
//     "time_last_update_utc": "Thu, 19 Dec 2024 00:00:01 +0000",
//     "time_next_update_unix": 1734652801,
//     "time_next_update_utc": "Fri, 20 Dec 2024 00:00:01 +0000",
//     "base_code": "USD",
//     "target_code": "NGN",
//     "conversion_rate": 1544.9915
// }

export const getExchangeRateApiRates = async () => {
    const endpoint = `https://v6.exchangerate-api.com/v6/${apiKey}/pair/USD/NGN`

    return await sendHTTPRequest("get", endpoint, {})
}



