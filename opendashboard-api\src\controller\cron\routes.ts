import {Routes} from "../../routes";
import {CronController} from "./controller"

export const cronRoutes: Routes = {
    basePath: '/cron',
    routes: {
        '/billing-renewals': {
            get: {controller: <PERSON>ron<PERSON>ontroller, action: "billingRenewals"}
        },
        '/handle-over-quota': {
            get: {controller: CronController, action: "checkAndHandleOverQuota",}
        },
        '/push-metrics': {
            get: {controller: CronController, action: "pushMetrics",}
        },
        '/push-inbranded-workflow-data': {
            get: {controller: CronController, action: "pushInbrandedWorkflowData",}
        },
        '/prepare-payouts': {
            get: {controller: CronController, action: "prepareCreatorPayouts",}
        },
        '/process-reminders': {
            get: {controller: CronController, action: "processReminders",}
        },
        '/expire-template-purchases': {
            get: {controller: CronController, action: "expireTemplatePurchases",}
        },
        '/trigger-scheduled-workflows': {
            get: {controller: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, action: "triggerScheduledWorkflows",}
        },
        '/provision-opendashboard-site-sender': {
            get: {controller: CronController, action: "provisionOpendashboardSiteEmail",}
        },
        '/turnoff-support-access': {
            get: {controller: CronController, action: "turnOffSupportAccess",}
        },
    }
}


