import { MigrationInterface, QueryRunner } from "typeorm";

export class TaskInputMigration1742528967293 implements MigrationInterface {
    name = 'TaskInputMigration1742528967293'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workflow_task\` ADD \`taskInput\` json NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workflow_task\` DROP COLUMN \`taskInput\``);
    }

}
