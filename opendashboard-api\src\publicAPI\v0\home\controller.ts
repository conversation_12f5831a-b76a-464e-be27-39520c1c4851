import {ApiResponseStatus, GenericApiResponseBody} from "../../../controller/interface";
import {NextFunction, Request, Response} from "express"
import {getEmailWithContentBody} from "../../../businessLogic/email";
import {appUrl} from "../../../config";
import {registerCampaignClick, registerCampaignOpen, registerUnsubscribe} from "../../../businessLogic/campaign";
import {logInfo} from "../../../businessLogic/logtail";
import {registerWorkflowEmailClick, registerWorkflowEmailOpen, registerWorkflowEmailUnsubscribe} from "../../../businessLogic/workflow";
import {AddAffiliateClick} from "../../../businessLogic/affiliate";
import {HandleNGNWorkspaceCreditPurchaseCallback} from "../../../businessLogic/workspace";

export class HomeController {

    async home(request: Request, response: Response, next: NextFunction) {
        return response.redirect("/v0/docs");
        // return response.redirect(appUrl());
    }

    async welcome(request: Request, response: Response, next: NextFunction) {
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "Welcome to V0 Routes",
            data: {
                // user
            },
        }
        return response.json(
            responseData
        )
    }

    async email(request: Request, response: Response, next: NextFunction) {
        const body = `
        <p style="font-size: 24px; font-weight: 600;">Your sign in request to Opendashboard</p>
        <p style="color:#313539;">Click on the button below to sign in to Opendashboard. The link expires in 5 minutes.</p>
        `

        return response.send(
            getEmailWithContentBody(body, {
                label: "Sign In", url: "https://opendashboard.app"
            }, " ")
        )
    }

    async handleUnsubscribe(request: Request, response: Response, next: NextFunction) {
        const eId: string = String(request.query.eId)
        const wTId: string = String(request.query.wTId)
        const hash: string = String(request.query.hash)
        const tS: string = String(request.query.tS)

        if (eId) await registerUnsubscribe(eId, hash, tS)
        else if (wTId) await registerWorkflowEmailUnsubscribe(wTId, hash, tS)

        const body = `
        <p style="font-size: 24px; font-weight: 600;">Unsubscribe successful</p>
        <p style="color:#313539;">Your email has been successfully unsubscribed from this list!</p>
        `
        return response.send(
            getEmailWithContentBody(body, null, ' ')
        )
    }

    async openTracking(request: Request, response: Response, next: NextFunction) {
        const eId: string = String(request.query.eId)
        const wTId: string = String(request.query.wTId)
        const hash: string = String(request.query.hash)
        const tS: string = String(request.query.tS)

        if (eId) await registerCampaignOpen(eId, hash, tS)
        else if (wTId) await registerWorkflowEmailOpen(wTId, hash, tS)

        logInfo("Campaign/workflow open triggered", {eId, hash, tS, headers: request.headers, query: request.query, params: request.params})

        const buf = new Buffer([
            0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00,
            0x80, 0x00, 0x00, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x2c,
            0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x02,
            0x02, 0x44, 0x01, 0x00, 0x3b
        ]);

        response.set('Content-Type', 'image/png');
        response.end(buf, 'binary');

        return
    }

    async clickTracking(request: Request, response: Response, next: NextFunction) {
        const nextUrl: string = String(request.query.next)
        const eId: string = String(request.query.eId)
        const wTId: string = String(request.query.wTId)
        const hash: string = String(request.query.hash)
        const tS: string = String(request.query.tS)

        logInfo("Campaign click triggered", {eId, hash, tS, headers: request.headers, query: request.query, params: request.params, nextUrl})

        if (!nextUrl) {
            return response.redirect(appUrl());
        }

        if (eId) await registerCampaignClick(eId, hash, tS, nextUrl)
        else if (wTId) await registerWorkflowEmailClick(wTId, hash, tS, nextUrl)

        return response.redirect(nextUrl);
    }

    async referralClickTracking(request: Request, response: Response, next: NextFunction) {
        const {offer} = await AddAffiliateClick(request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                offer
            },
        }
        return response.json(
            responseData
        )
    }

    async verifyNGNWorkspaceCreditPurchase(request: Request, response: Response, next: NextFunction) {
        try {
            const {nextUrl} = await HandleNGNWorkspaceCreditPurchaseCallback(request.params.creditId, request.query.reference as string)
            return response.redirect(nextUrl);
        } catch (e) {

        }
        return response.redirect(appUrl('/welcome'));
    }

}