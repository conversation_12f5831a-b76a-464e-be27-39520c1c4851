import config, {corsOption} from "../config";
import {Server} from "http";
import {createClient} from "redis";
import {createAdapter} from "@socket.io/redis-adapter";
import {VerifyJWTToken} from "../businessLogic/auth";
import {getAuthInfo, saveAuthInfo} from "../businessLogic/authInfo";
import {workspaceHandlers} from "../socketio/workspace";
import {SocketAcknowledgeCallback} from "../socketio";
import {ApiResponseStatus} from "../controller/interface";
import {initializeYJS} from "../socketio/yjs";
import {backendHandlers} from "../socketio/backend";


const socketio = require("socket.io");

let ioInstance;

export const getIO = () => {
    return ioInstance
}

export const setIO = (io) => {
    ioInstance = io
}


const verifyJWTSocket = async (socket, next) => {
    const {request, handshake} = socket
    const token = handshake && handshake.auth ? handshake.auth.token : undefined;

    const validApiToken = await VerifyJWTToken(token)

    if (!validApiToken) {
        const error = new Error("Invalid JWT Token")
        next(error);
        return
    }
    saveAuthInfo(request, validApiToken);
    next()
}

const verifyBackendSocket = async (socket, next) => {
    const {request, handshake} = socket
    const client_id = handshake && handshake.auth ? handshake.auth.client_id : undefined;
    const client_secret = handshake && handshake.auth ? handshake.auth.client_secret : undefined;

    const credIsValid = client_id && client_secret && client_id === config.CLIENT.id && client_secret === config.CLIENT.secret

    if (!credIsValid) {
        const error = new Error("Invalid Client Credentials")
        next(error);
        return
    }
    next()
}

export const setupSocketIO = async (server: Server) => {
    const io = socketio(server, {
        path: "/ws/",
        cors: {
            ...corsOption,
            methods: ["GET", "POST"],
        },
    })
    const pubClient = createClient({url: "redis://localhost:6379"});
    const subClient = pubClient.duplicate();
    await Promise.all([
        pubClient.connect(),
        subClient.connect()
    ]);
    io.adapter(createAdapter(pubClient, subClient));

    setupWorkspaceConnection(io)
    setupBackendConnection(io)
    setIO(io)
    initializeYJS(io)
}

const setupWorkspaceConnection = (io) => {
    const workspaceNamespace = io.of(/^\/workspace\/[A-Za-z0-9-]+$/);
    workspaceNamespace.use(verifyJWTSocket);

    workspaceNamespace.on("connection", socket => {
        const namespace = socket.nsp;
        const namespaceId = namespace.name.split("/")[2]; // Extract the value of X from the namespace
        console.log(`Connected to workspace with Id ${namespaceId}`);

        const authInfo = getAuthInfo(socket.request)

        // console.log('AuthInfo:', authInfo)
        socket.__client_id = authInfo.userId
        socket.__workspace_id = namespaceId

        socket.on("event", (data) => {
            console.log(`Received event in namespace ${namespaceId}:`, data);
            // Process the event logic for the specific namespace
        });

        workspaceHandlers.forEach((h) => {
            const callback = async (data: any, cb: SocketAcknowledgeCallback) => {
                // console.log(`Handler: ${h.on} called`)
                try {
                    let res = await h.handler(h.on, namespace, socket, data)
                    if (!res) res = {}
                    cb({
                        status: ApiResponseStatus.Ok,
                        message: '',
                        data: {...res}
                    })
                } catch (ex) {
                    console.log(ex);
                    cb({
                        status: ApiResponseStatus.Error,
                        message: ex.message,
                        data: {}
                    })
                }
            }
            socket.on(h.on, callback)
        })
        console.log('All Namespace Handlers defined')
    })
}

const setupBackendConnection = (io) => {
    const backendNamespace = io.of(/^\/backend\/[A-Za-z0-9-]+$/);

    backendNamespace.use(verifyBackendSocket);

    backendNamespace.on("connection", socket => {
        const namespace = socket.nsp;
        const namespaceId = namespace.name.split("/")[2]; // Extract the value of X from the namespace
        console.log(`Connected to backend with Id ${namespaceId}`);

        socket.on("hello", (data, cb: SocketAcknowledgeCallback) => {
            console.log(`Received hello in namespace ${namespaceId}:`, data);
            // Process the event logic for the specific namespace
            cb({
                status: ApiResponseStatus.Ok,
                message: 'Hello from server',
                data: data
            })
        });
        backendHandlers.forEach((h) => {
            const callback = async (data: any, cb: SocketAcknowledgeCallback) => {
                // console.log(`Handler: ${h.on} called`)
                try {
                    let res = await h.handler(h.on, namespace, socket, data)
                    if (!res) res = {}
                    cb?.({
                        status: ApiResponseStatus.Ok,
                        message: '',
                        data: {...res}
                    })
                } catch (ex) {
                    console.log(ex);
                    cb?.({
                        status: ApiResponseStatus.Error,
                        message: ex.message,
                        data: {}
                    })
                }
            }
            socket.on(h.on, callback)
        })
        console.log('All Namespace Handlers defined')
    })
}

