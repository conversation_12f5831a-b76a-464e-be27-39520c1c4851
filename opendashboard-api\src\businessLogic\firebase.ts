import * as admin from 'firebase-admin';
import {Message} from "firebase-admin/lib/messaging/messaging-api";
import {consoleLog} from "./logtail";

const serviceAccount = require("../../configs/opendashboard-firebase-adminsdk-service-account.json");

const app = admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
});

export const SendNotification = async (message: Message) => {
    try {
        const response = await admin.messaging().send(message);
        consoleLog('FCM message sent successfully', response);

        return response
    } catch (error) {
        console.error('FCM message sending failed', error);
    }
    return null
}