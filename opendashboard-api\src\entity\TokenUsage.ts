import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {EntityStatus} from "./common";

@Entity()
@Index(["tokenId", "date", "ipAddress"], {unique: true})

export class TokenUsage {

    @PrimaryGeneratedColumn()
    id: number

    @Index()
    @Column({type: "int"})
    tokenId: number

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'tinyint', default: EntityStatus.Active})
    status: EntityStatus

    @Index()
    @Column({type: "date", nullable: true})
    date: Date

    @Column({type: "varchar", nullable: true})
    ipAddress: string

    @Index()
    @Column({type: "int", default: 0})
    usageCount: number

}

