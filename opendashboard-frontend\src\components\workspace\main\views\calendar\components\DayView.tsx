import React, { useRef } from 'react';
import { Button } from "@/components/ui/button";
import { format, isToday, setHours, isSameDay } from 'date-fns';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '../index';
import { getEventDurationInMinutes } from '@/utils/dateUtils';
import { useScreenSize } from '@/providers/screenSize';
import { PlusIcon } from '@heroicons/react/24/outline';

interface DayViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  savedScrollTop: React.MutableRefObject<number>;
  handleEventClick: (event: CalendarEvent) => void;
  onEventDrop?: (event: CalendarEvent, date: Date) => void;
}

const CalendarEventItem = ({
  event,
  style,
  selectedEvent,
  onClick,
  onDragStart,
  canEditData
}: {
  event: CalendarEvent;
  style: React.CSSProperties;
  selectedEvent: string | null;
  onClick: (e: React.MouseEvent) => void;
  onDragStart: (event: CalendarEvent, e: React.DragEvent) => void;
  canEditData: boolean;
}) => {
  return (
    <div
      draggable={canEditData}
      onDragStart={(e) => onDragStart(event, e)}
      className={cn(
        "absolute left-2 right-2 px-3 py-2 rounded-md text-xs shadow-sm border cursor-pointer",
        "transition-all duration-200 hover:shadow-md",
        selectedEvent === event.id
          ? "bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20"
          : "bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700"
      )}
      style={style}
      onClick={onClick}
    >
      <div className="font-medium truncate leading-tight">{event.title}</div>
      <div className={cn(
        "text-[10px] mt-0.5 opacity-75",
        selectedEvent === event.id ? "text-primary-foreground/80" : "text-muted-foreground"
      )}>
        {format(new Date(event.start), 'h:mm a')}
      </div>
    </div>
  );
};

const TimeSlot = ({
  hour,
  date,
  children,
  onDragOver,
  onDrop,
  onClick
}: {
  hour: number;
  date: Date;
  children: React.ReactNode;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  onClick: () => void;
}) => {
  return (
    <div
      onDragOver={onDragOver}
      onDrop={onDrop}
      className="flex-1 relative border-b border-gray-100 min-h-[60px] cursor-pointer"
      style={{ height: '60px' }}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export const DayView: React.FC<DayViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  openAddEventForm,
  canEditData,
  savedScrollTop,
  handleEventClick,
  onEventDrop
}) => {
  const { isMobile } = useScreenSize();
  const draggedEvent = useRef<CalendarEvent | null>(null);
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const dayEvents = events.filter(event =>
    isSameDay(new Date(event.start), selectedDate)
  );

  const currentTimePosition = isToday(selectedDate) ? {
    hour: new Date().getHours(),
    minutes: new Date().getMinutes()
  } : null;

  const handleDragStart = (event: CalendarEvent, e: React.DragEvent) => {
    if (!canEditData) return;

    draggedEvent.current = event;

    // Get the actual event element to match its size
    const eventElement = e.currentTarget as HTMLElement;
    const eventRect = eventElement.getBoundingClientRect();

    // Create a drag image that matches the actual event size
    const dragImg = document.createElement('div');
    dragImg.style.position = 'absolute';
    dragImg.style.top = '-1000px';
    dragImg.style.left = '0px';
    dragImg.style.width = `${eventRect.width}px`;
    dragImg.style.height = `${Math.max(eventRect.height, 40)}px`;
    dragImg.style.background = '#1e293b';
    dragImg.style.border = '2px solid #475569';
    dragImg.style.borderRadius = '6px';
    dragImg.style.padding = '8px 12px';
    dragImg.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
    dragImg.style.fontSize = '12px';
    dragImg.style.fontWeight = '600';
    dragImg.style.color = 'white';
    dragImg.style.display = 'flex';
    dragImg.style.flexDirection = 'column';
    dragImg.style.justifyContent = 'center';
    dragImg.style.opacity = '1';
    dragImg.style.zIndex = '9999';
    dragImg.style.boxSizing = 'border-box';
    dragImg.style.transform = 'scale(1)';
    dragImg.style.backfaceVisibility = 'hidden';

    dragImg.innerHTML = `
      <div style="font-weight: 600; margin-bottom: 4px; color: white; line-height: 1.2;">
        ${event.title}
      </div>
      <div style="font-size: 10px; color: #cbd5e1; line-height: 1.2;">
        ${format(new Date(event.start), 'h:mm a')}
      </div>
    `;

    document.body.appendChild(dragImg);

    // Set drag image with center offset based on actual size
    e.dataTransfer.setDragImage(dragImg, eventRect.width / 2, eventRect.height / 2);

    // Clean up after a short delay
    setTimeout(() => {
      if (document.body.contains(dragImg)) {
        document.body.removeChild(dragImg);
      }
    }, 100);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!canEditData) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (hour: number, e: React.DragEvent) => {
    e.preventDefault();
    if (!canEditData || !draggedEvent.current) return;

    const event = draggedEvent.current;
    const originalDate = new Date(event.start);

    // Calculate minutes based on drop position within the time slot
    const timeSlotHeight = 60; // height of time slot in pixels
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const minutes = Math.floor((relativeY / timeSlotHeight) * 60);

    // Create new date with calculated minutes
    const newDate = new Date(selectedDate);
    newDate.setHours(hour, minutes, 0, 0);

    // Check if the new date is the same as the original
    if (newDate.getTime() === originalDate.getTime()) {
      draggedEvent.current = null;
      return;
    }

    onEventDrop?.(event, newDate);
    draggedEvent.current = null;
  };

  if (dayEvents.length === 0) {
    return (
      <div className="flex flex-col h-full overflow-hidden">
        {/* Compact Day Header */}
        <div className="border-b bg-secondary sticky top-0 z-20">
          <div className={cn(
            "text-center",
            isMobile ? "py-3" : "py-4"
          )}>
            <div className={cn(
              "font-semibold text-foreground mb-1",
              isMobile ? "text-sm" : "text-base"
            )}>
              {format(selectedDate, 'EEEE')}
            </div>
            <div className={cn(
              "inline-flex items-center justify-center font-medium",
              isMobile ? "text-base w-6 h-6" : "text-lg w-8 h-8",
              isToday(selectedDate)
                ? "bg-primary text-primary-foreground rounded-full shadow-sm"
                : "text-muted-foreground"
            )}>
              {format(selectedDate, 'd')}
            </div>
          </div>
        </div>

        {/* Enhanced Empty State */}
        <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent">
          <div className="text-center max-w-md mx-auto px-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No events scheduled
            </h3>
            <p className="text-muted-foreground mb-6">
              {isToday(selectedDate)
                ? "You have a free day ahead! Add an event to get started."
                : `No events planned for ${format(selectedDate, 'EEEE, MMMM d')}.`
              }
            </p>
            {canEditData && (
              <Button
                onClick={() => {
                  const newDate = new Date(selectedDate);
                  newDate.setHours(new Date().getHours(), 0, 0, 0);
                  openAddEventForm(newDate);
                }}
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Create Event
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Compact Day Header with Events */}
      <div className="border-b bg-secondary sticky top-0 z-20">
        <div className={cn(
          "text-center",
          isMobile ? "py-3" : "py-4"
        )}>
          <div className={cn(
            "font-semibold text-foreground mb-1",
            isMobile ? "text-sm" : "text-base"
          )}>
            {format(selectedDate, 'EEEE')}
          </div>
          <div className={cn(
            "inline-flex items-center justify-center font-medium",
            isMobile ? "text-base w-6 h-6" : "text-lg w-8 h-8",
            isToday(selectedDate)
              ? "bg-primary text-primary-foreground rounded-full shadow-sm"
              : "text-muted-foreground"
          )}>
            {format(selectedDate, 'd')}
          </div>
        </div>
      </div>

      {/* Enhanced Time Grid */}
      <div className="flex-1 overflow-auto relative bg-white" id="day-view-container">
        {hours.map((hour, i) => (
          <div key={i} className="flex border-b border-gray-50 hover:bg-gray-25 transition-colors" style={{ height: '60px' }}>
            {/* Time Label */}
            <div className={cn(
              "flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 sticky left-0 bg-white z-10",
              isMobile ? "w-14" : "w-20"
            )}>
              <div className="text-right">
                <div className="text-sm font-semibold">
                  {format(setHours(selectedDate, hour), isMobile ? 'h' : 'h')}
                </div>
                <div className="text-[10px] text-gray-400">
                  {format(setHours(selectedDate, hour), 'a')}
                </div>
              </div>
            </div>

            {/* Time Slot */}
            <TimeSlot
              hour={hour}
              date={selectedDate}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(hour, e)}
              onClick={() => {
                if (canEditData) {
                  const newDate = new Date(selectedDate);
                  newDate.setHours(hour, 0, 0, 0);
                  openAddEventForm(newDate);
                }
              }}
            >
              {dayEvents
                .filter(event => {
                  const eventHour = new Date(event.start).getHours();
                  return eventHour === hour;
                })
                .map((event) => (
                  <CalendarEventItem
                    key={event.id}
                    event={event}
                    selectedEvent={selectedEvent}
                    style={{
                      top: `${new Date(event.start).getMinutes() / 60 * 100}%`,
                      height: `${Math.max(30, getEventDurationInMinutes(event) / 60 * 100)}%`,
                      zIndex: selectedEvent === event.id ? 20 : 10
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      const container = document.getElementById('day-view-container');
                      if (container) {
                        savedScrollTop.current = container.scrollTop;
                      }
                      setSelectedEvent(event.id);
                      handleEventClick(event);
                    }}
                    onDragStart={handleDragStart}
                    canEditData={canEditData}
                  />
                ))}
            </TimeSlot>
          </div>
        ))}

        {/* Enhanced Current Time Indicator */}
        {currentTimePosition && (
          <div
            className={cn(
              "absolute right-0 flex items-center z-30 pointer-events-none",
              isMobile ? "left-14" : "left-20"
            )}
            style={{
              top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`
            }}
          >
            <div className="w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5" />
            <div className="flex-1 border-t-2 border-red-500 shadow-sm" />
          </div>
        )}
      </div>
    </div>
  );
};
