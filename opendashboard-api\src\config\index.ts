const dotenv = require('dotenv');
import * as path from "path";

dotenv.config();


interface EnvConfig {
    PORT: string
    API_VERSION: string
    API_URL: string
    APP_URL: string
    LANDING_URL: string
    ENVIRONMENT: "production" | "stage" | "local"
    TEMP_FOLDER: string
    CLIENT: {
        id: string
        secret: string
    },
    DATABASE: {
        host: string
        username: string
        password: string
        database: string
        port: string
        slave_host: string
    },
    JWT: {
        secret: string
        expires: number
    },
    EMAIL: {
        host: string
        port: string
        username: string
        password: string
        name: string
        email: string
        secure: boolean
    },
    SENTRY: {
        dsn: string
    },
    CRONITOR: {
        key: string
    },
    DO_SPACE: {
        endpoint: string
        bucket: string
        access_key: string
        access_secret: string
    },
    IPINFO: {
        token: string
    },
    CORS: {},
    LOGTAIL: {
        token: string
    },
    STRIPE_KEY: {
        secret_key: string
        webhook_secret: string
    },
    OPENDASHBOARD: {
        api_key: string
    },
    INBRANDED: {
        api_key: string
    },
    ANTHROPIC: {
        api_key: string
    },
    OPENAI: {
        api_key: string
    },
    SENDGRID: {
        api_key: string
    },
    GOOGLE: {
        client_id: string
        client_secret: string
    },
    PAYSTACK: {
        public_key: string,
        secret_key: string
    },
    EXCHANGERATE_API: {
        api_key: string
    },
}

const tempFolder = __dirname + "/../../temp/";

const config = {
    PORT: process.env.PORT,
    API_VERSION: process.env.API_VERSION,
    API_URL: process.env.API_BASEURL,
    APP_URL: process.env.APP_URL,
    LANDING_URL: process.env.LANDING_URL,
    TEMP_FOLDER: tempFolder,
    ENVIRONMENT: process.env.NODE_ENV,
    CLIENT: {
        id: process.env.API_CLIENT_ID,
        secret: process.env.API_CLIENT_SECRET
    },
    DATABASE: {
        host: process.env.DATABASE_HOST,
        username: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.DATABASE_NAME,
        port: process.env.DATABASE_PORT,
        slave_host: process.env.DATABASE_SLAVE_HOST
    },
    JWT: {
        secret: process.env.JWT_SECRET,
        expires: Number(process.env.JWT_EXPIRES),
    },
    EMAIL: {
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        username: process.env.EMAIL_USERNAME,
        password: process.env.EMAIL_PASSWORD,
        name: process.env.EMAIL_NAME,
        email: process.env.EMAIL_EMAIL,
        secure: Boolean(process.env.EMAIL_SECURE),
    },
    SENTRY: {
        dsn: process.env.SENTRY_DSN,
    },
    CRONITOR: {
        key: process.env.CRONITOR_API_KEY
    },
    DO_SPACE: {
        endpoint: process.env.DO_SPACE_ENDPOINT,
        bucket: process.env.DO_SPACE_BUCKET,
        access_key: process.env.DO_SPACE_ACCESS_KEY,
        access_secret: process.env.DO_SPACE_ACCESS_SECRET
    },
    IPINFO: {
        token: process.env.IPINFO_TOKEN,
    },
    CORS: {},
    GOOGLE: {
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET
    },
    LOGTAIL: {
        token: process.env.LOGTAIL_TOKEN
    },
    STRIPE_KEY: {
        secret_key: process.env.STRIPE_SECRET_KEY,
        webhook_secret: process.env.STRIPE_WEBHOOK_SECRET
    },
    OPENDASHBOARD: {
        api_key: process.env.OPENDASHBOARD_API_KEY
    },
    INBRANDED: { 
        api_key: process.env.INBRANDED_API_KEY
    },
    ANTHROPIC: {
        api_key: process.env.ANTHROPIC_API_KEY
    },
    OPENAI: {
        api_key: process.env.OPENAI_API_KEY
    },
    SENDGRID: {
        api_key: process.env.SENDGRID_API_KEY
    },
    PAYSTACK: {
        public_key: process.env.PAYSTACK_PUBLIC_KEY,
        secret_key: process.env.PAYSTACK_SECRET_KEY
    },
    EXCHANGERATE_API: {
        api_key: process.env.EXCHANGERATE_API_KEY
    },
} as EnvConfig

export default config

export const getEnv = () => config.ENVIRONMENT

export const isProduction = () => config.ENVIRONMENT === 'production'

export const isStage = () => config.ENVIRONMENT === 'stage'

export const isLocal = () => config.ENVIRONMENT === 'local'

export const apiUrl = (relPath = '') => config.API_URL + (relPath || '')

export const landingUrl = (relPath = '') => config.LANDING_URL + (relPath || '')

export const appUrl = (relPath = '') => config.APP_URL + (relPath || '')

const rootDir = path.join(__dirname, "..", "..");

export const rootPath = (relPath = '') => path.join(rootDir, relPath);

export const publicPath = (relPath = '') => `${rootDir}/public${relPath}`;

export const allowedOrigins = [
    'localhost:*',
    'opendashboard.co',
    'opendashboard.app',
    '*.opendashboard.co',
    '*.opendashboard.app',
    '*.opendashboard.site',
];

export const corsOption = {
    origin: (origin, callBack) => {
        if (!origin) {
            return callBack(null, true); // Allow non-origin requests (e.g., mobile apps, server-to-server)
        }

        try {
            // Extract hostname and port from origin
            const { hostname, port } = new URL(origin);

            // Function to check if origin matches allowed origins
            const isAllowed = allowedOrigins.some((allowed) => {
                if (allowed === '*') return true; // Allow all origins

                const [allowedDomain, allowedPort] = allowed.split(':');

                // Handle localhost:* case
                if (allowedDomain === 'localhost' && allowedPort === '*') {
                    return hostname === 'localhost';
                }

                // Handle wildcard subdomains (*.example.com)
                if (allowedDomain.startsWith('*.')) {
                    const baseDomain = allowedDomain.substring(2); // Remove *.
                    return hostname.endsWith(`.${baseDomain}`);
                }

                // Exact match (with optional port check)
                return hostname === allowedDomain && (!allowedPort || port === allowedPort);
            });

            if (isAllowed) {
                callBack(null, true);
            } else {
                callBack(new Error('Not allowed by CORS'));
            }
        } catch (error) {
            callBack(new Error('Invalid origin format'));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200,
};

export const pm2ClusterInstanceId = () => {
    return process.env.NODE_APP_INSTANCE
}