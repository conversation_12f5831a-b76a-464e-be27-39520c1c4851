import { MigrationInterface, QueryRunner } from "typeorm";

export class ReminderNotification1735489777296 implements MigrationInterface {
    name = 'ReminderNotification1735489777296'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`reminder\` ADD \`resolvedById\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`reminder\` ADD \`resolvedAt\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`reminder\` ADD \`assignedToUserIds\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`reminder\` ADD \`taggedRecordIds\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`document\` ADD \`assignedToUserIds\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`document\` ADD \`taggedRecordIds\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_551fd93a41848b589fb1f4f275\` ON \`reminder\` (\`resolvedById\`)`);
        await queryRunner.query(`CREATE FULLTEXT INDEX \`IDX_79b3e6d35763521fcb7bf090fc\` ON \`reminder\` (\`assignedToUserIds\`)`);
        await queryRunner.query(`CREATE FULLTEXT INDEX \`IDX_f73a205683b9bff74ff3ef1e46\` ON \`reminder\` (\`taggedRecordIds\`)`);
        await queryRunner.query(`CREATE FULLTEXT INDEX \`IDX_edc891bb4fed901397cfab5a7f\` ON \`document\` (\`assignedToUserIds\`)`);
        await queryRunner.query(`CREATE FULLTEXT INDEX \`IDX_91c31cb87f169a1d7094a38e5f\` ON \`document\` (\`taggedRecordIds\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_91c31cb87f169a1d7094a38e5f\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_edc891bb4fed901397cfab5a7f\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_f73a205683b9bff74ff3ef1e46\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_79b3e6d35763521fcb7bf090fc\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_551fd93a41848b589fb1f4f275\` ON \`reminder\``);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`document\` DROP COLUMN \`taggedRecordIds\``);
        await queryRunner.query(`ALTER TABLE \`document\` DROP COLUMN \`assignedToUserIds\``);
        await queryRunner.query(`ALTER TABLE \`reminder\` DROP COLUMN \`taggedRecordIds\``);
        await queryRunner.query(`ALTER TABLE \`reminder\` DROP COLUMN \`assignedToUserIds\``);
        await queryRunner.query(`ALTER TABLE \`reminder\` DROP COLUMN \`resolvedAt\``);
        await queryRunner.query(`ALTER TABLE \`reminder\` DROP COLUMN \`resolvedById\``);
    }

}
