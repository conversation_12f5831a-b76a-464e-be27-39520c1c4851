import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {ObjectMappings, TemplateHomePage} from "./templateRelease";


export enum TemplateInstallStatus {
    Pending = "pending",
    Completed = "completed",
    Failed = "failed",
}


export enum TemplateInstallMethod {
    Install = "install",
    Upgrade = "upgrade",
}

export interface ItemInstallSelection {
    installMethod?: TemplateInstallMethod
    upgradeId?: string
    installData?: boolean
    installViews?: boolean
}

export interface ItemInstallSelectionMap {
    [key: string]: ItemInstallSelection | undefined | null
}

export interface TemplateInstallPrepareData {
    databases: ItemInstallSelectionMap
    pages: ItemInstallSelectionMap
    workflows: ItemInstallSelectionMap
    installAll?: boolean
}


@Entity()
export class TemplateInstall {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    templateId: string

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    templateReleaseId: number

    @Column({type: 'json', nullable: true, select: false})
    objectMappings: ObjectMappings;

    @Column({type: 'json', nullable: true, select: false})
    prepareData: TemplateInstallPrepareData;

    @Column({type: "json", nullable: true})
    meta: { homePage?: TemplateHomePage } & object

    @Index()
    @Column({type: 'varchar', nullable: false})
    installedById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    installStatus: TemplateInstallStatus

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    upgradedFromReleaseId: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Column({type: 'timestamp', nullable: true})
    completedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}