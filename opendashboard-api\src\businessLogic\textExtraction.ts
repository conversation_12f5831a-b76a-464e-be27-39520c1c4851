import axios from 'axios';
import {parse} from 'csv-parse/sync';
import * as mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import * as Tesseract from 'tesseract.js';
// import * as pptxParser from 'pptx-parser';
const officeParser = require('officeparser');
import pdfParse = require('pdf-parse');


function render_page(pageData) {
    //check documents https://mozilla.github.io/pdf.js/
    let render_options = {
        //replaces all occurrences of whitespace with standard spaces (0x20). The default value is `false`.
        normalizeWhitespace: false,
        //do not attempt to combine same line TextItem's. The default value is `false`.
        disableCombineTextItems: false
    }

    return pageData.getTextContent(render_options)
        .then(function (textContent) {
            let lastY, text = '';
            for (let item of textContent.items) {
                if (lastY == item.transform[5] || !lastY) {
                    text += item.str;
                } else {
                    text += '\n' + item.str;
                }
                lastY = item.transform[5];
            }
            return text;
        });
}

let options = {
    pagerender: render_page
}


const extractTextFromPdf = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'arraybuffer'});
        // const pdfDoc = await PDFDocument.load(response.data);
        let text = '';

        const pdfData = await pdfParse(response.data, options);

        text = pdfData.text
        // const pages = pdfDoc.getPages();
        // for (const page of pages) {
        //     text += page.getTextContent();
        // }
        return text;
    } catch (error) {
        throw new Error(`Failed to load or process PDF: ${error.message}`);
    }
}

const extractTextFromDoc = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'arraybuffer'});
        const {value: text} = await mammoth.extractRawText({buffer: response.data});
        return text;
    } catch (error) {
        throw new Error(`Failed to load or process DOC/DOCX: ${error.message}`);
    }
};

const extractTextFromSpreadsheet = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'arraybuffer'});
        const workbook = XLSX.read(response.data, {type: 'buffer'});
        let text = '';
        workbook.SheetNames.forEach(sheetName => {
            const worksheet = workbook.Sheets[sheetName];
            const sheetText = XLSX.utils.sheet_to_csv(worksheet);
            text += sheetText;
        });
        return text;
    } catch (error) {
        throw new Error(`Failed to load or process spreadsheet: ${error.message}`);
    }
};

const extractTextViaOfficeParser = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'arraybuffer'});
        // const result = await pptxParser(response.data);
        let text = '';
        text = await officeParser.parseOfficeAsync(response.data);
        return text;
    } catch (error) {
        throw new Error(`Failed to load or process presentation: ${error.message}`);
    }
};

const extractTextFromImage = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'arraybuffer'});
        const result = await Tesseract.recognize(Buffer.from(response.data), 'eng');
        return result.data.text;
    } catch (error) {
        throw new Error(`Failed to load or process image for OCR: ${error.message}`);
    }
};

const extractTextFromTxt = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'text'});
        return response.data;
    } catch (error) {
        throw new Error(`Failed to load or process TXT file: ${error.message}`);
    }
};

const extractTextFromCsv = async (url: string): Promise<string> => {
    try {
        const response = await axios.get(url, {responseType: 'text'});
        const records = parse(response.data, {
            skip_empty_lines: true
        });

        // Join all fields and rows into a single string
        return records.map(row => row.join(' ')).join('\n');
    } catch (error) {
        throw new Error(`Failed to load or process CSV file: ${error.message}`);

    }
};


const extractors = {
    'text/plain': extractTextFromTxt,
    'text/csv': extractTextFromCsv,
    // 'application/pdf': extractTextFromPdf,
    'application/pdf': extractTextViaOfficeParser,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': extractTextFromDoc,
    'application/msword': extractTextFromDoc,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': extractTextFromSpreadsheet,
    'application/vnd.ms-excel': extractTextFromSpreadsheet,
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': extractTextViaOfficeParser,
    'application/vnd.ms-powerpoint': extractTextViaOfficeParser,
    'image/jpeg': extractTextFromImage,
    'image/png': extractTextFromImage,
    'image/gif': extractTextFromImage,
};

export const extractTextFromDocument = async (url: string, mimeType: string) => {
    const extractor = await extractors[mimeType];
    if (!extractor) {
        throw new Error(`Unsupported MIME type: ${mimeType}`);
    }
    return extractor(url);
};

