"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, selectedEvent, onClick, onDragStart, canEditData } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md cursor-pointer select-none shadow-sm border transition-all duration-200\", isMobile ? \"px-2 py-1 text-[10px] mb-0.5\" : \"px-2 py-1 text-xs mb-1\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-md ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700 hover:shadow-md\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-full flex-shrink-0\", isMobile ? \"w-1.5 h-1.5\" : \"w-2 h-2\", selectedEvent === event.id ? \"bg-primary-foreground/60\" : \"bg-primary\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"truncate font-medium leading-tight\",\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-[10px] mt-0.5 opacity-75 ml-3\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), isMobile ? \"h:mm\" : \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventItem, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c = CalendarEventItem;\nconst DayCell = (param)=>{\n    let { date, children, onClick, onDragOver, onDrop, isCurrentMonth } = param;\n    _s1();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r relative cursor-pointer transition-colors group\", isMobile ? \"min-h-[80px] p-2\" : \"min-h-[110px] p-3\", isCurrentMonth ? \"bg-card hover:bg-accent\" : \"bg-muted hover:bg-muted/80\", \"border-border\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayCell, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c1 = DayCell;\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, onEventDrop } = param;\n    _s2();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Create a simple, visible drag image\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"200px\";\n        dragImg.style.height = \"50px\";\n        dragImg.style.background = \"white\";\n        dragImg.style.border = \"1px solid #d1d5db\";\n        dragImg.style.borderRadius = \"6px\";\n        dragImg.style.padding = \"8px 12px\";\n        dragImg.style.boxShadow = \"0 4px 6px -1px rgb(0 0 0 / 0.1)\";\n        dragImg.style.fontSize = \"12px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"#1f2937\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.opacity = \"0.9\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; margin-bottom: 2px; color: #1f2937; line-height: 1.2;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 10px; color: #6b7280; line-height: 1.2;\">\\n        ').concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        // Set drag image with proper offset\n        e.dataTransfer.setDragImage(dragImg, 100, 25);\n        // Clean up after a short delay\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (date, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the cell\n        const cellHeight = e.target.offsetHeight;\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const hourPercentage = relativeY / cellHeight;\n        const hour = Math.floor(hourPercentage * 24);\n        const minutes = Math.floor((hourPercentage * 24 - hour) * 60);\n        // Create new date with calculated time\n        const newDate = new Date(date);\n        newDate.setHours(hour, minutes, 0, 0);\n        // Check if the new date is the same as the original\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate);\n    const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate);\n    const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(monthStart, {\n        weekStartsOn: 0\n    });\n    const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(monthEnd, {\n        weekStartsOn: 0\n    });\n    const days = [];\n    let day = startDay;\n    while(day <= endDay){\n        days.push(day);\n        day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, 1);\n    }\n    const weeks = [];\n    for(let i = 0; i < days.length; i += 7){\n        weeks.push(days.slice(i, i + 7));\n    }\n    const monthEvents = events.filter((event)=>{\n        const eventStart = new Date(event.start);\n        return eventStart >= startDay && eventStart <= endDay;\n    });\n    if (monthEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b bg-secondary\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-foreground\", isMobile ? \"py-3 text-xs\" : \"py-4 text-sm\"),\n                            children: isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events this month\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: [\n                                    (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, \"MMMM yyyy\"),\n                                    \" is completely free. Start planning your month!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>openAddEventForm(selectedDate),\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-full bg-background\", isMobile ? \"flex flex-col\" : \"flex\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 border-b sticky top-0 z-10 bg-secondary\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-foreground\", isMobile ? \"py-3 text-xs\" : \"py-4 text-sm\"),\n                                children: isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 grid grid-cols-7 auto-rows-fr border-gray-100 border-b\",\n                        children: weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), day));\n                                const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                    date: day,\n                                    isCurrentMonth: isCurrentMonth,\n                                    onClick: ()=>setSelectedDate(day),\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(day, e),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold\", isMobile ? \"text-sm w-6 h-6\" : \"text-base w-7 h-7\", isCurrentDay ? \"bg-primary text-primary-foreground shadow-sm\" : isCurrentMonth ? \"text-foreground hover:bg-accent\" : \"text-muted-foreground\"),\n                                                    children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(day, \"d\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canEditData && isCurrentMonth && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-accent\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openAddEventForm(day);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-1\", isMobile && \"space-y-0.5\"),\n                                            children: [\n                                                dayEvents.slice(0, isMobile ? 2 : 4).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                                        event: event,\n                                                        selectedEvent: selectedEvent,\n                                                        canEditData: canEditData,\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            setSelectedEvent(event.id);\n                                                            handleEventClick(event);\n                                                        },\n                                                        onDragStart: handleDragStart\n                                                    }, event.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                dayEvents.length > (isMobile ? 2 : 4) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-primary hover:text-primary/80 font-medium pl-3 py-1 rounded-md hover:bg-accent transition-colors\", isMobile ? \"text-[10px]\" : \"text-xs\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedDate(day);\n                                                    },\n                                                    children: [\n                                                        \"+ \",\n                                                        dayEvents.length - (isMobile ? 2 : 4),\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(weekIndex, \"-\").concat(dayIndex), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, undefined);\n                            }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, undefined),\n            (!isMobile || isMobile && selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-l border-gray-200 flex flex-col bg-gray-50\", isMobile ? \"w-full border-t\" : \"w-96\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-gray-200 bg-white\", isMobile ? \"p-4\" : \"p-6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-bold text-gray-900 mb-1\", isMobile ? \"text-lg\" : \"text-xl\"),\n                                children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, isMobile ? \"MMM d, yyyy\" : \"MMMM d, yyyy\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).length,\n                                    \" events scheduled\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1\", isMobile ? \"p-4\" : \"p-6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).sort((a, b)=>new Date(a.start).getTime() - new Date(b.start).getTime()).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"cursor-pointer transition-all duration-200 border-0 shadow-sm\", isMobile ? \"p-3\" : \"p-4\", selectedEvent === event.id ? \"bg-primary text-primary-foreground shadow-lg ring-2 ring-primary/20\" : \"bg-primary/5 hover:shadow-md hover:bg-primary/10\"),\n                                        onClick: ()=>{\n                                            setSelectedEvent(event.id);\n                                            handleEventClick(event);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold mb-2\", isMobile ? \"text-sm\" : \"text-base\", selectedEvent === event.id ? \"text-primary-foreground\" : \"text-foreground\"),\n                                                children: event.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", isMobile ? \"text-xs\" : \"text-sm\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"),\n                                                            \" - \",\n                                                            (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.end), \"h:mm a\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, event.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined),\n                            events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-primary\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground mb-4\", isMobile ? \"text-sm\" : \"text-base\"),\n                                        children: \"No events on this day\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>openAddEventForm(selectedDate),\n                                        className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2 rounded-lg shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Add Event\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c2 = MonthView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"DayCell\");\n$RefreshReg$(_c2, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});