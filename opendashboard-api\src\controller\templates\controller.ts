import {NextFunction, Request, Response} from "express"
import {ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {applyTemplateDiscount, BuildTemplateInstallOptions, BuildTemplateInstallOptionsData, GetMarketplaceCreator, GetMarketplaceTemplate, GetMarketplaceTemplates, GetPurchasedTemplates, GetTemplateReleaseWithAssets, GetTemplateReviews, HandleNGNTemplatePurchaseCompletedWebhook, InstallTemplate, InstallTemplateViaCode, PostTemplateReview, PurchaseTemplate} from "../../businessLogic/templates";
import {CategoryService} from "../../service/category";
import {CategoryType} from "../../entity/category";
import {In} from "typeorm";
import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {IpRecord} from "../../entity/ipRecord";
import {queryClientIpRecord} from "../../businessLogic/ipUtility";
import {getUSDNGNRate} from "../../businessLogic/currencyConverter";
import {TemplatePurchaseService} from "../../service/templatePurchase";
import {TemplateListingService} from "../../service/templateListing";
import {appUrl} from "../../config";
import {nameToSlug} from "../../utility/slug";
import {ErrorMessage} from "../../errors/AppError";

export class TemplatesController {

    async getTemplates(request: Request, response: Response, next: NextFunction) {
        // const authInfo: AuthInfo = getAuthInfo(request)
        const categories = await (new CategoryService().find({type: In([CategoryType.All, CategoryType.Template])}))

        const templates = await GetMarketplaceTemplates(request.query)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                templates,
                categories
            },
        }
        return response.json(
            responseData
        )
    }

    async getTemplate(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {template, tags, purchases} = await GetMarketplaceTemplate(request.params.id, authInfo.userId)
        const categories = await (new CategoryService().find({type: In([CategoryType.All, CategoryType.Template])}))

        const ipRecord: IpRecord = await queryClientIpRecord(request);
        const country = ipRecord.country

        const USDNGNRate = (await getUSDNGNRate()).rate

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                template, tags, categories, purchases, country, USDNGNRate
            },
        }
        return response.json(
            responseData
        )
    }

    async getTemplateReleaseResources(request: Request, response: Response, next: NextFunction) {
        const templateId = request.params.id
        const releaseId = request.params.releaseId

        const data = await GetTemplateReleaseWithAssets(templateId, releaseId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: data,
        }
        return response.json(
            responseData
        )
    }

    async getCategories(request: Request, response: Response, next: NextFunction) {
        const categories = await (new CategoryService().find({type: In([CategoryType.All, CategoryType.Template])}))
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                categories
            },
        }
        return response.json(
            responseData
        )
    }


    async getDiscussions(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const reviews = await GetTemplateReviews(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                reviews
            },
        }
        return response.json(
            responseData
        )
    }

    async postDiscussion(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {review, replaces} = await PostTemplateReview(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                review, replaces
            },
        }
        return response.json(
            responseData
        )
    }

    async getCreator(request: Request, response: Response, next: NextFunction) {
        const res = await GetMarketplaceCreator(request.params.domain)
        const categories = await (new CategoryService().find({type: In([CategoryType.All, CategoryType.Template])}))
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                ...res, categories
            },
        }
        return response.json(
            responseData
        )
    }

    async purchaseTemplate(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {purchase, checkoutUrl} = await PurchaseTemplate(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                checkoutUrl, purchase
            },
        }
        return response.json(
            responseData
        )
    }

    async applyDiscount(request: Request, response: Response, next: NextFunction) {
        const discount = await applyTemplateDiscount(request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                discount
            },
        }
        return response.json(
            responseData
        )
    }

    async verifyNGNTemplatePurchase(request: Request, response: Response, next: NextFunction) {
        try {
            const {nextUrl} = await HandleNGNTemplatePurchaseCompletedWebhook(request.params.id, request.params.purchaseId, request.query.reference as string)
            return response.redirect(nextUrl);
        } catch (e) {
            const purchase = await new TemplatePurchaseService().findOne({id: request.params.purchaseId})
            const templateListing = purchase ? await new TemplateListingService().findOne({id: purchase.templateListingId}) :
                                    await new TemplateListingService().findOne({id: Number(request.params.id)}, {createdAt: "DESC"})

            const templateLink = appUrl(`/templates/${nameToSlug(templateListing ? templateListing.name : "Unknown Template")}/${request.params.id}?action=purchase&wId=${purchase?.workspaceId}&error=${ErrorMessage.ErrorOccurredWhileProcessingRequest}`)
            return response.redirect(templateLink);
        }
    }

    async installOptions(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const data = await BuildTemplateInstallOptions(authInfo.userId, request.params.id, request.query as any as BuildTemplateInstallOptionsData)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data,
        }
        return response.json(
            responseData
        )
    }

    async startInstall(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {install} = await InstallTemplate(authInfo.userId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                install
            },
        }
        return response.json(
            responseData
        )
    }

    async startInstallViaCode(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {install} = await InstallTemplateViaCode(authInfo.userId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                install
            },
        }
        return response.json(
            responseData
        )
    }

    async getPurchases(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const purchases = await GetPurchasedTemplates(authInfo.userId, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: "",
            data: {
                purchases
            },
        }
        return response.json(
            responseData
        )
    }

}