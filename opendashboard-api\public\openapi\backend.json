{"info": {"title": "Opendashboard API", "version": "0.0.1"}, "openapi": "3.0.3", "paths": {"/auth/sign-in": {"post": {"operationId": "ApiV1Spec_post_/auth/sign-in", "tags": [], "summary": "Initiate Sign In", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}, "additionalProperties": false, "required": ["email"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/auth/exchange-token": {"post": {"operationId": "ApiV1Spec_post_/auth/exchange-token", "tags": [], "summary": "Exchange Token", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"hash": {"type": "string"}}, "additionalProperties": false, "required": ["hash"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__token_ApiToken_user_User___"}}}}}}}, "/auth/verify-email": {"get": {"operationId": "ApiV1Spec_get_/auth/verify-email", "tags": [], "summary": "<PERSON><PERSON><PERSON>", "parameters": [], "responses": {"301": {"description": "", "content": {"application/json": {"schema": {"type": "string", "enum": [""]}}}}}}}, "/auth/verify-sender": {"get": {"operationId": "ApiV1Spec_get_/auth/verify-sender", "tags": [], "summary": "<PERSON><PERSON><PERSON>", "parameters": [], "responses": {"301": {"description": "", "content": {"application/json": {"schema": {"type": "string", "enum": [""]}}}}}}}, "/account": {"get": {"operationId": "ApiV1Spec_get_/account", "tags": [], "summary": "Get Profile", "security": [{"jwt": []}], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__user_User___"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/account", "tags": [], "summary": "Update Profile", "security": [{"jwt": []}], "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}, "additionalProperties": false, "required": ["email", "firstName", "lastName"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__user_User____1"}}}}}}}, "/account/photo": {"patch": {"operationId": "ApiV1Spec_patch_/account/photo", "tags": [], "summary": "Update Profile Photo", "security": [{"jwt": []}], "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"enum": ["image/jpeg", "image/png"], "type": "string"}}, "additionalProperties": false, "required": ["file"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__user_User____2"}}}}}}}, "/account/settings": {"get": {"operationId": "ApiV1Spec_get_/account/settings", "tags": [], "summary": "Get Settings", "security": [{"jwt": []}], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__settings_Settings___"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/account/settings", "tags": [], "summary": "Update Settings", "security": [{"jwt": []}], "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["notification"]}, "settings": {"$ref": "#/components/schemas/NotificationSettings"}}, "additionalProperties": false, "required": ["settings", "type"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/account/sessions": {"get": {"operationId": "ApiV1Spec_get_/account/sessions", "tags": [], "summary": "Get Sessions", "security": [{"jwt": []}], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__sessions_Token_____"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/account/sessions", "tags": [], "summary": "Delete Session", "security": [{"jwt": []}], "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "number"}}, "additionalProperties": false, "required": ["id"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/account/ping": {"post": {"operationId": "ApiV1Spec_post_/account/ping", "tags": [], "summary": "Ping Session", "security": [{"jwt": []}], "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "client": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["client", "name"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/account/prompt-email-verification": {"post": {"operationId": "ApiV1Spec_post_/account/prompt-email-verification", "tags": [], "summary": "Prompt Email Verification", "security": [{"jwt": []}], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces": {"get": {"operationId": "ApiV1Spec_get_/workspaces", "tags": [], "summary": "Get Workspaces", "security": [{"jwt": []}], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspaces_MyWorkspace_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces", "tags": [], "summary": "Create Workspace", "security": [{"jwt": []}], "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "domain": {"type": "string"}}, "additionalProperties": false, "required": ["name"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace___"}}}}}}}, "/workspaces/domain/{domain}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/domain/{domain}", "tags": [], "summary": "Get Workspace By Domain", "security": [{"jwt": []}], "parameters": [{"name": "domain", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_Workspace___"}}}}}}}, "/workspaces/{id}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}", "tags": [], "summary": "Get Workspace", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace____1"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}", "tags": [], "summary": "Update Workspace", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}}, "additionalProperties": false, "required": ["name"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace____2"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}", "tags": [], "summary": "Delete Workspace", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"reason": {"type": "string"}}, "additionalProperties": false, "required": ["reason"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace____3"}}}}}}}, "/workspaces/{id}/logo": {"patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/logo", "tags": [], "summary": "Update Workspace Logo", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"enum": ["image/jpeg", "image/png"], "type": "string"}}, "additionalProperties": false, "required": ["file"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace____4"}}}}}}}, "/workspaces/{id}/complete-setup": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/complete-setup", "tags": [], "summary": "Complete Workspace Setup", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"orgPhoneNumber": {"type": "string"}, "orgSize": {"type": "string"}, "orgType": {"type": "string"}, "orgUseCase": {"type": "string"}}, "additionalProperties": false, "required": ["orgPhoneNumber", "orgSize", "orgType", "orgUseCase"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/switch-to": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/switch-to", "tags": [], "summary": "Switch to Workspace", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace____5"}}}}}}}, "/workspaces/{id}/support-access": {"patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/support-access", "tags": [], "summary": "Toggle Support Access to Workspace", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"enable": {"type": "boolean"}}, "additionalProperties": false, "required": ["enable"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/members": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/members", "tags": [], "summary": "Get Members", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"enable": {"type": "boolean"}}, "additionalProperties": false, "required": ["enable"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__members_MyWorkspaceMember___invitations_WorkspaceInvitation_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/members", "tags": [], "summary": "Invite Member", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}}, "additionalProperties": false, "required": ["email", "role"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__invitation_WorkspaceInvitation___"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/members", "tags": [], "summary": "Update Member", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}, "userId": {"type": "string"}}, "additionalProperties": false, "required": ["role", "userId"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/members", "tags": [], "summary": "Delete Member", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}}, "additionalProperties": false, "required": ["userId"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/members/{userId}/make-owner": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/members/{userId}/make-owner", "tags": [], "summary": "Make Member Owner", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/invitations/{inviteId}/resend": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/invitations/{inviteId}/resend", "tags": [], "summary": "Resend Invitation", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "inviteId", "in": "path", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/invitations/{inviteId}": {"delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/invitations/{inviteId}", "tags": [], "summary": "Delete Invitation", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "inviteId", "in": "path", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/invitation": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/invitation", "tags": [], "summary": "Get Invitation", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__invitation_MyWorkspaceInvitation___"}}}}}}}, "/workspaces/{id}/invitation/accept": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/invitation/accept", "tags": [], "summary": "Accept Invitation", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__workspace_MyWorkspace____6"}}}}}}}, "/workspaces/{id}/invitation/decline": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/invitation/decline", "tags": [], "summary": "Decline Invitation", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/senders": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/senders", "tags": [], "summary": "Get Senders", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__senders_WorkspaceSenderEmail___domains_WorkspaceDomain_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/senders", "tags": [], "summary": "Add Sender", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}}, "additionalProperties": false, "required": ["email", "name"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__sender_WorkspaceSenderEmail_domain_WorkspaceDomain___"}}}}}}}, "/workspaces/{id}/senders/{senderId}": {"delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/senders/{senderId}", "tags": [], "summary": "Get Senders", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "senderId", "in": "path", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/senders/{senderId}/prompt-verification": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/senders/{senderId}/prompt-verification", "tags": [], "summary": "Prompt Sender Verification", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "senderId", "in": "path", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/domains/{domainId}/verify": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/domains/{domainId}/verify", "tags": [], "summary": "Verify Domain", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "domainId", "in": "path", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/domains/{domainId}": {"delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/domains/{domainId}", "tags": [], "summary": "Delete Domain", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "domainId", "in": "path", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/usage": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/usage", "tags": [], "summary": "Get Usage", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__stats_WorkspaceStats_billingCycle_BillingCycle_availableCreditInCents_number___"}}}}}}}, "/workspaces/{id}/plans": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/plans", "tags": [], "summary": "Get Usage", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__plans___key_string__WorkspacePlan___addOns___key_string__PlanAddOn___payPerUsePricing_PayPerUse___"}}}}}}}, "/workspaces/{id}/checkout-session": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/checkout-session", "tags": [], "summary": "Get Checkout Session URL", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__url_string___"}}}}}}}, "/workspaces/{id}/customer-portal": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/customer-portal", "tags": [], "summary": "Get Customer Portal URL", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__url_string____1"}}}}}}}, "/workspaces/{id}/future-subscription": {"delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/future-subscription", "tags": [], "summary": "Delete Future Subscription", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/purchase-credit": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/purchase-credit", "tags": [], "summary": "Purchase Credit", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"amountInCents": {"type": "number"}}, "additionalProperties": false, "required": ["amountInCents"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/pages": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/pages", "tags": [], "summary": "Get Pages", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__pages_PermissiblePageWithPermissions_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/pages", "tags": [], "summary": "Create Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"visibility": {"$ref": "#/components/schemas/Visibility"}, "name": {"type": "string"}, "icon": {"$ref": "#/components/schemas/PickedIcon"}}, "additionalProperties": false, "required": ["icon", "name"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__page_PermissiblePage___"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/pages", "tags": [], "summary": "Update Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string"}, "icon": {"$ref": "#/components/schemas/PickedIcon"}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}, "visibility": {"$ref": "#/components/schemas/Visibility"}}, "additionalProperties": false, "required": ["accessLevel", "icon", "id", "name", "visibility"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/pages", "tags": [], "summary": "Delete Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}}, "additionalProperties": false, "required": ["id"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/pages/{pageId}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/pages/{pageId}", "tags": [], "summary": "Get <PERSON>", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__page_PermissiblePageWithPermissions___"}}}}}}}, "/workspaces/{id}/pages/{pageId}/permissions": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/pages/{pageId}/permissions", "tags": [], "summary": "Invite to <PERSON>", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"emails": {"type": "array", "items": {"type": "string"}}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "emails"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__permissions_PagePermission_____"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/pages/{pageId}/permissions", "tags": [], "summary": "Update Access to Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "string"}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "userId"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/pages/{pageId}/permissions", "tags": [], "summary": "Delete Access from Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["userIds"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/pages/{pageId}/views": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/pages/{pageId}/views", "tags": [], "summary": "Add View to Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"$ref": "#/components/schemas/ViewType"}, "definition": {"$ref": "#/components/schemas/ViewDefinition"}}, "additionalProperties": false, "required": ["definition", "name", "type"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__view_View___"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/pages/{pageId}/views", "tags": [], "summary": "Update View in Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"viewId": {"type": "string"}, "isPublished": {"type": "boolean"}}, "additionalProperties": false, "required": ["isPublished", "viewId"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/pages/{pageId}/views", "tags": [], "summary": "Delete View from Page", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"viewId": {"type": "string"}}, "additionalProperties": false, "required": ["viewId"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/databases": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/databases", "tags": [], "summary": "Get Databases", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__databases_PermissibleDatabaseWithPermissions_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/databases", "tags": [], "summary": "Create Database", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"definition": {"$ref": "#/components/schemas/DatabaseDefinition"}, "name": {"type": "string"}, "visibility": {"$ref": "#/components/schemas/Visibility"}}, "additionalProperties": false, "required": ["name"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__database_PermissibleDatabase___"}}}}}}}, "/workspaces/{id}/databases/{databaseId}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/databases/{databaseId}", "tags": [], "summary": "Get Database", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__database_PermissibleDatabaseWithPermissions___"}}}}}}}, "/workspaces/{id}/databases/{databaseId}/records": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Get Records", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__records_Record_____"}}}}}}, "post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Add Records", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"valuesList": {"type": "array", "items": {"$ref": "#/components/schemas/RecordValues"}}, "onDuplicate": {"$ref": "#/components/schemas/OnDuplicateAction"}}, "additionalProperties": false, "required": ["onDuplicate", "valuesList"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__records_Record______1"}}}}}}, "patch": {"operationId": "ApiV1Spec_patch_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Update Record", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "values": {"$ref": "#/components/schemas/RecordValues"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["ids", "values"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}, "delete": {"operationId": "ApiV1Spec_delete_/workspaces/{id}/databases/{databaseId}/records", "tags": [], "summary": "Delete Records", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["ids"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericApiResponseBody"}}}}}}}, "/workspaces/{id}/settings": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/settings", "tags": [], "summary": "Get User Workspace Settings", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "databaseId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__settings_WorkspaceMemberSettings___"}}}}}}}, "/workspaces/{id}/campaigns": {"post": {"operationId": "ApiV1Spec_post_/workspaces/{id}/campaigns", "tags": [], "summary": "Create Campaign", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"databaseId": {"type": "string"}, "targetColId": {"type": "string"}, "recordIds": {"type": "array", "items": {"type": "string"}}, "targetEntityScope": {"$ref": "#/components/schemas/TargetEntityScope"}}, "additionalProperties": false, "required": ["databaseId", "recordIds"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody__campaign_Campaign___"}}}}}}}, "/workspaces/{id}/campaigns/{campaignId}": {"get": {"operationId": "ApiV1Spec_get_/workspaces/{id}/campaigns/{campaignId}", "tags": [], "summary": "Get Campaign", "security": [{"jwt": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "campaignId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"databaseId": {"type": "string"}, "targetColId": {"type": "string"}, "recordIds": {"type": "array", "items": {"type": "string"}}, "targetEntityScope": {"$ref": "#/components/schemas/TargetEntityScope"}}, "additionalProperties": false, "required": ["databaseId", "recordIds"]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseBody_GetCampaignResponse_"}}}}}}}}, "components": {"schemas": {"GenericApiResponseBody": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseStatus": {"type": "string", "enum": ["ok", "error"]}, "ApiResponseBody__token_ApiToken_user_User___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"$ref": "#/components/schemas/ApiToken"}, "user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false, "required": ["token", "user"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiToken": {"type": "object", "properties": {"token": {"type": "string"}, "type": {"type": "string", "enum": ["Bearer"]}, "expiresAt": {"type": "string", "format": "date-time"}, "id": {"type": "number"}}, "additionalProperties": false, "required": ["expiresAt", "id", "token", "type"]}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "profilePhoto": {"type": "string"}, "isEmailVerified": {"type": "boolean"}, "isSetupCompleted": {"type": "boolean"}, "isSupportAccount": {"type": "boolean"}, "activeWorkspaceId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "auditLog": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["activeWorkspaceId", "auditLog", "createdAt", "deletedAt", "email", "firstName", "id", "isEmailVerified", "isSetupCompleted", "isSupportAccount", "lastName", "profilePhoto", "updatedAt"]}, "ApiResponseBody__user_User___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false, "required": ["user"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__user_User____1": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false, "required": ["user"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "UpdateProfileData": {"type": "object", "properties": {"email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}, "additionalProperties": false, "required": ["email", "firstName", "lastName"]}, "ApiResponseBody__user_User____2": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false, "required": ["user"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__settings_Settings___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"settings": {"$ref": "#/components/schemas/Settings"}}, "additionalProperties": false, "required": ["settings"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "Settings": {"type": "object", "properties": {"id": {"type": "number"}, "userId": {"type": "string"}, "notification": {"$ref": "#/components/schemas/NotificationSettings"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "id", "notification", "updatedAt", "userId"]}, "NotificationSettings": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/NotificationChannelSetting"}, "properties": {"email": {"$ref": "#/components/schemas/NotificationChannelSetting"}}, "required": ["email"]}, "NotificationChannelSetting": {"type": "object", "properties": {"isActive": {"type": "boolean"}, "notifyOn": {"type": "object", "properties": {"comment": {"type": "boolean"}, "replies": {"type": "boolean"}, "assignedRecord": {"type": "boolean"}, "dueTask": {"type": "boolean"}}, "additionalProperties": false, "required": ["assignedRecord", "comment", "dueTask", "replies"]}}, "additionalProperties": false, "required": ["isActive", "notifyOn"]}, "UpdateSettingsData": {"type": "object", "properties": {"type": {"type": "string", "enum": ["notification"]}, "settings": {"$ref": "#/components/schemas/NotificationSettings"}}, "additionalProperties": false, "required": ["settings", "type"]}, "ApiResponseBody__sessions_Token_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"sessions": {"type": "array", "items": {"$ref": "#/components/schemas/Token"}}}, "additionalProperties": false, "required": ["sessions"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "Token": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "purpose": {"$ref": "#/components/schemas/TokenType"}, "status": {"$ref": "#/components/schemas/TokenStatus"}, "userId": {"type": "string"}, "workspaceId": {"type": "string"}, "token": {"type": "string"}, "clientName": {"type": "string"}, "lastActiveAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["clientName", "createdAt", "deletedAt", "expiresAt", "id", "lastActiveAt", "meta", "name", "purpose", "status", "token", "updatedAt", "userId", "workspaceId"]}, "TokenType": {"type": "number", "enum": [1, 2, 3, 4, 5]}, "TokenStatus": {"type": "number", "enum": [1, 2]}, "PingSessionRequestBody": {"type": "object", "properties": {"name": {"type": "string"}, "client": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["client", "name"]}, "ApiResponseBody__workspaces_MyWorkspace_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspaces": {"type": "array", "items": {"$ref": "#/components/schemas/MyWorkspace"}}}, "additionalProperties": false, "required": ["workspaces"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "MyWorkspace": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/Workspace"}, "workspaceMember": {"$ref": "#/components/schemas/WorkspaceMember"}, "membersCount": {"type": "number"}, "planId": {"type": "string"}, "priceId": {"type": "string"}, "billingCycle": {"$ref": "#/components/schemas/BillingCycle"}}, "additionalProperties": false, "required": ["billingCycle", "membersCount", "planId", "priceId", "workspace", "workspaceMember"]}, "Workspace": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "domain": {"type": "string"}, "isSetupCompleted": {"type": "boolean"}, "logo": {"type": "string"}, "createdById": {"type": "string"}, "ownerId": {"type": "string"}, "stripeCustomerId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "orgUseCase": {"type": "string"}, "orgType": {"type": "string"}, "orgSize": {"type": "string"}, "orgPhoneNumber": {"type": "string"}, "deletionReason": {"type": "string"}, "timezone": {"type": "string"}, "isSupportAccessEnabled": {"type": "boolean"}, "isFunctionalityLimited": {"type": "boolean"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["createdAt", "createdById", "deletedAt", "deletionReason", "domain", "id", "isFunctionalityLimited", "isSetupCompleted", "isSupportAccessEnabled", "logo", "meta", "name", "orgPhoneNumber", "orgSize", "orgType", "orgUseCase", "ownerId", "stripeCustomerId", "timezone", "updatedAt"]}, "KeyValueStore": {"type": "object", "additionalProperties": {"type": "object", "properties": {}, "additionalProperties": true}}, "WorkspaceMember": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "userId": {"type": "string"}, "role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}, "invitedById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "deletedAt", "id", "invitedById", "role", "updatedAt", "userId", "workspaceId"]}, "WorkspaceMemberRole": {"type": "string", "enum": ["owner", "admin", "member", "collaborator", "supportUser"]}, "BillingCycle": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "stripeSubscriptionId": {"type": "string"}, "futureStripeSubscriptionId": {"type": "string"}, "planId": {"type": "string"}, "priceId": {"type": "string"}, "futurePriceId": {"type": "string"}, "anchorDay": {"type": "number"}, "startsAt": {"type": "string", "format": "date-time"}, "endsAt": {"type": "string", "format": "date-time"}, "endedAt": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "isPaid": {"type": "boolean"}, "isRenewing": {"type": "boolean"}, "costInCents": {"type": "number"}, "amountPaidInCents": {"type": "number"}, "paidAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "cyclePlanQuota": {"$ref": "#/components/schemas/UsageLimits"}, "cycleUsage": {"$ref": "#/components/schemas/UsageLimits"}, "addOnsQuota": {"$ref": "#/components/schemas/AddOnsQuota"}, "usersQuota": {"type": "number"}, "collaboratorsQuota": {"type": "number"}, "recordsQuota": {"type": "number"}, "auditLog": {"type": "array", "items": {"type": "string"}}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["addOnsQuota", "amountPaidInCents", "anchorDay", "auditLog", "<PERSON><PERSON><PERSON><PERSON>", "costInCents", "createdAt", "cyclePlanQuota", "cycleUsage", "deletedAt", "endedAt", "endsAt", "futurePriceId", "futureStripeSubscriptionId", "id", "isActive", "isPaid", "isRenewing", "meta", "paidAt", "planId", "priceId", "recordsQuota", "startsAt", "stripeSubscriptionId", "updatedAt", "usersQuota", "workspaceId"]}, "UsageLimits": {"type": "object", "properties": {"users": {"type": "number"}, "collaborators": {"type": "number"}, "records": {"type": "number"}, "emails": {"type": "number"}, "senderDomains": {"type": "number"}, "senderEmails": {"type": "number"}, "enrichment": {"type": "number"}, "aiGeneration": {"type": "number"}, "dataHistory": {"type": "number"}}, "additionalProperties": false, "required": ["aiGeneration", "collaborators", "dataHistory", "emails", "enrichment", "records", "senderDomains", "senderEmails", "users"]}, "AddOnsQuota": {"type": "object", "properties": {"users": {"type": "number"}, "collaborators": {"type": "number"}, "records": {"type": "number"}, "sendingDomains": {"type": "number"}, "sendingEmails": {"type": "number"}}, "additionalProperties": false, "required": ["collaborators", "records", "sendingDomains", "sendingEmails", "users"]}, "ApiResponseBody__workspace_MyWorkspace___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "CreateWorkspaceRequestData": {"type": "object", "properties": {"name": {"type": "string"}, "domain": {"type": "string"}}, "additionalProperties": false, "required": ["name"]}, "ApiResponseBody__workspace_Workspace___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/Workspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__workspace_MyWorkspace____1": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__workspace_MyWorkspace____2": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__workspace_MyWorkspace____3": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "DeleteWorkspaceRequestData": {"type": "object", "properties": {"reason": {"type": "string"}}, "additionalProperties": false, "required": ["reason"]}, "ApiResponseBody__workspace_MyWorkspace____4": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "CompleteWorkspaceSetupData": {"type": "object", "properties": {"orgPhoneNumber": {"type": "string"}, "orgSize": {"type": "string"}, "orgType": {"type": "string"}, "orgUseCase": {"type": "string"}}, "additionalProperties": false, "required": ["orgPhoneNumber", "orgSize", "orgType", "orgUseCase"]}, "ApiResponseBody__workspace_MyWorkspace____5": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__members_MyWorkspaceMember___invitations_WorkspaceInvitation_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"members": {"type": "array", "items": {"$ref": "#/components/schemas/MyWorkspaceMember"}}, "invitations": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceInvitation"}}}, "additionalProperties": false, "required": ["invitations", "members"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "MyWorkspaceMember": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "workspaceMember": {"$ref": "#/components/schemas/WorkspaceMember"}}, "additionalProperties": false, "required": ["user", "workspaceMember"]}, "WorkspaceInvitation": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "email": {"type": "string"}, "userId": {"type": "string"}, "role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}, "token": {"type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "invitedById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "deletedAt", "email", "expiresAt", "id", "invitedById", "role", "token", "updatedAt", "userId", "workspaceId"]}, "ApiResponseBody__invitation_WorkspaceInvitation___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"invitation": {"$ref": "#/components/schemas/WorkspaceInvitation"}}, "additionalProperties": false, "required": ["invitation"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "CreateWorkspaceInvitationData": {"type": "object", "properties": {"email": {"type": "string"}, "role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}}, "additionalProperties": false, "required": ["email", "role"]}, "UpdateWorkspaceMemberData": {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/WorkspaceMemberRole"}, "userId": {"type": "string"}}, "additionalProperties": false, "required": ["role", "userId"]}, "DeleteWorkspaceMemberData": {"type": "object", "properties": {"userId": {"type": "string"}}, "additionalProperties": false, "required": ["userId"]}, "ApiResponseBody__invitation_MyWorkspaceInvitation___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"invitation": {"$ref": "#/components/schemas/MyWorkspaceInvitation"}}, "additionalProperties": false, "required": ["invitation"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "MyWorkspaceInvitation": {"type": "object", "properties": {"invitation": {"$ref": "#/components/schemas/WorkspaceInvitation"}, "workspace": {"$ref": "#/components/schemas/Workspace"}}, "additionalProperties": false, "required": ["invitation", "workspace"]}, "ApiResponseBody__workspace_MyWorkspace____6": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"workspace": {"$ref": "#/components/schemas/MyWorkspace"}}, "additionalProperties": false, "required": ["workspace"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__senders_WorkspaceSenderEmail___domains_WorkspaceDomain_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"senders": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceSenderEmail"}}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceDomain"}}}, "additionalProperties": false, "required": ["domains", "senders"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "WorkspaceSenderEmail": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "addedByUserId": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "workspaceDomainId": {"type": "number"}, "isVerified": {"type": "boolean"}, "verifiedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["addedByUserId", "createdAt", "deletedAt", "email", "id", "isVerified", "name", "updatedAt", "verifiedAt", "workspaceDomainId", "workspaceId"]}, "WorkspaceDomain": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "addedByUserId": {"type": "string"}, "domainId": {"type": "number"}, "isVerified": {"type": "boolean"}, "verifiedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["addedByUserId", "createdAt", "deletedAt", "domainId", "id", "isVerified", "updatedAt", "verifiedAt", "workspaceId"]}, "ApiResponseBody__sender_WorkspaceSenderEmail_domain_WorkspaceDomain___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"sender": {"$ref": "#/components/schemas/WorkspaceSenderEmail"}, "domain": {"$ref": "#/components/schemas/WorkspaceDomain"}}, "additionalProperties": false, "required": ["domain", "sender"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "CreateSenderEmailData": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}}, "additionalProperties": false, "required": ["email", "name"]}, "ApiResponseBody__stats_WorkspaceStats_billingCycle_BillingCycle_availableCreditInCents_number___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"stats": {"$ref": "#/components/schemas/WorkspaceStats"}, "billingCycle": {"$ref": "#/components/schemas/BillingCycle"}, "availableCreditInCents": {"type": "number"}}, "additionalProperties": false, "required": ["availableCreditInCents", "billingCycle", "stats"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "WorkspaceStats": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "users": {"type": "number"}, "collaborators": {"type": "number"}, "records": {"type": "number"}, "sendingEmails": {"type": "number"}, "sendingDomains": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["collaborators", "createdAt", "deletedAt", "id", "records", "sendingDomains", "sendingEmails", "updatedAt", "users", "workspaceId"]}, "ApiResponseBody__plans___key_string__WorkspacePlan___addOns___key_string__PlanAddOn___payPerUsePricing_PayPerUse___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"plans": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/WorkspacePlan"}}, "addOns": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PlanAddOn"}}, "payPerUsePricing": {"$ref": "#/components/schemas/PayPerUse"}}, "additionalProperties": false, "required": ["addOns", "payPerUsePricing", "plans"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "WorkspacePlan": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string"}, "description": {"type": "string"}, "priceId": {"type": "string"}, "commitments": {"$ref": "#/components/schemas/SubscriptionCommitment"}, "limits": {"$ref": "#/components/schemas/UsageLimits"}}, "additionalProperties": false, "required": ["commitments", "description", "id", "limits", "name", "priceId"]}, "SubscriptionCommitment": {"type": "object", "properties": {"monthToMonth": {"type": "object", "properties": {"costInCents": {"type": "number"}, "priceId": {"type": "string"}}, "additionalProperties": false, "required": ["costInCents", "priceId"]}, "annual": {"type": "object", "properties": {"costInCents": {"type": "number"}, "priceId": {"type": "string"}}, "additionalProperties": false, "required": ["costInCents", "priceId"]}}, "additionalProperties": false, "required": ["annual", "monthToMonth"]}, "PlanAddOn": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "priceId": {"type": "string"}, "commitments": {"$ref": "#/components/schemas/SubscriptionCommitment"}}, "additionalProperties": false, "required": ["commitments", "id", "name", "priceId"]}, "PayPerUse": {"type": "object", "properties": {"enrichment": {"type": "number"}, "aiGeneration": {"type": "number"}, "email": {"type": "number"}}, "additionalProperties": false, "required": ["aiGeneration", "email", "enrichment"]}, "ApiResponseBody__url_string___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"url": {"type": "string"}}, "additionalProperties": false, "required": ["url"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__url_string____1": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"url": {"type": "string"}}, "additionalProperties": false, "required": ["url"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "PurchaseWorkspaceCreditData": {"type": "object", "properties": {"amountInCents": {"type": "number"}}, "additionalProperties": false, "required": ["amountInCents"]}, "ApiResponseBody__pages_PermissiblePageWithPermissions_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"pages": {"type": "array", "items": {"$ref": "#/components/schemas/PermissiblePageWithPermissions"}}}, "additionalProperties": false, "required": ["pages"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "PermissiblePageWithPermissions": {"type": "object", "properties": {"permissions": {"type": "array", "items": {"$ref": "#/components/schemas/PagePermission"}}, "page": {"$ref": "#/components/schemas/Page"}, "views": {"type": "array", "items": {"$ref": "#/components/schemas/View"}}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "page", "permissions", "views"]}, "PagePermission": {"type": "object", "properties": {"id": {"type": "number"}, "workspaceId": {"type": "string"}, "pageId": {"type": "string"}, "userId": {"type": "string"}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["accessLevel", "createdAt", "deletedAt", "id", "pageId", "updatedAt", "userId", "workspaceId"]}, "AccessLevel": {"type": "string", "enum": ["view", "edit", "full"]}, "Page": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "icon": {"$ref": "#/components/schemas/PickedIcon"}, "workspaceId": {"type": "string"}, "databaseId": {"type": "string"}, "slug": {"type": "string"}, "visibility": {"$ref": "#/components/schemas/Visibility"}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}, "viewsOrder": {"type": "array", "items": {"type": "string"}}, "ownerId": {"type": "string"}, "createdById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["accessLevel", "createdAt", "createdById", "databaseId", "deletedAt", "icon", "id", "name", "ownerId", "slug", "updatedAt", "viewsOrder", "visibility", "workspaceId"]}, "PickedIcon": {"anyOf": [{"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "Icon": {"type": "object", "properties": {"type": {"type": "string", "enum": ["icon"]}, "icon": {"type": "string"}}, "additionalProperties": false, "required": ["icon", "type"]}, "Emoji": {"type": "object", "properties": {"type": {"type": "string", "enum": ["emoji"]}, "emoji": {"type": "string"}}, "additionalProperties": false, "required": ["emoji", "type"]}, "Visibility": {"type": "string", "enum": ["private", "open"]}, "View": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "pageId": {"type": "string"}, "slug": {"type": "string"}, "type": {"$ref": "#/components/schemas/ViewType"}, "definition": {"anyOf": [{"$ref": "#/components/schemas/ViewDefinition"}, {"$ref": "#/components/schemas/TableViewDefinition"}, {"$ref": "#/components/schemas/DashboardViewDefinition"}, {"$ref": "#/components/schemas/CalendarViewDefinition"}]}, "isPublished": {"type": "boolean"}, "allowSearchEngineIndex": {"type": "boolean"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["allowSearchEngineIndex", "createdAt", "createdById", "definition", "deletedAt", "description", "id", "isPublished", "name", "pageId", "publishedAt", "slug", "type", "updatedAt"]}, "ViewType": {"type": "string", "enum": ["table", "board", "form", "document", "dashboard", "summary-table", "list-view", "calendar"]}, "ViewDefinition": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/ViewType"}, "lockContent": {"type": "boolean"}}, "additionalProperties": false, "required": ["type"]}, "TableViewDefinition": {"type": "object", "properties": {"type": {"type": "string", "enum": ["table"]}, "lockContent": {"type": "boolean"}, "databaseId": {"type": "string"}, "filter": {"$ref": "#/components/schemas/DbRecordFilter"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/DbRecordSort"}}, "columnsOrder": {"type": "array", "items": {"type": "string"}}, "columnPropsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ViewColumnCustomization"}}}, "additionalProperties": false, "required": ["columnPropsMap", "columnsOrder", "databaseId", "filter", "sorts", "type"]}, "DbRecordFilter": {"type": "object", "properties": {"conditions": {"type": "array", "items": {"$ref": "#/components/schemas/DbCondition"}}, "match": {"$ref": "#/components/schemas/Match"}}, "additionalProperties": false, "required": ["conditions", "match"]}, "DbCondition": {"type": "object", "properties": {"columnId": {"type": "string"}, "op": {"$ref": "#/components/schemas/CompareOperator"}, "value": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "string"}]}, "extra": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "string"}]}}, "additionalProperties": false, "required": ["columnId", "op", "value"]}, "CompareOperator": {"type": "string", "enum": ["equals", "not_equals", "is_empty", "is_not_empty", "is_checked", "is_not_checked", "contains", "does_not_contain", "starts_with", "ends_with", "greater_than", "less_than", "is_same_day_as", "is_earlier_than", "is_later_than", "is_any_of", "is_none_of"]}, "Match": {"type": "string", "enum": ["all", "any"]}, "DbRecordSort": {"type": "object", "properties": {"columnId": {"type": "string"}, "order": {"$ref": "#/components/schemas/Sort"}}, "additionalProperties": false, "required": ["columnId", "order"]}, "Sort": {"type": "string", "enum": ["asc", "desc"]}, "ViewColumnCustomization": {"type": "object", "properties": {"isHidden": {"type": "boolean"}, "color": {"$ref": "#/components/schemas/Color"}}, "additionalProperties": false}, "Color": {"enum": ["AtomicTangerine", "Beige", "Black", "<PERSON>", "<PERSON><PERSON>", "DarkSlateGray", "<PERSON><PERSON>", "Emerald", "EnglishViolet", "IndianRed", "Jet", "LapisLazuli", "LightGray", "LightGreen", "<PERSON><PERSON><PERSON>", "Old<PERSON>ose", "OxfordBlue", "Red", "<PERSON><PERSON><PERSON><PERSON>", "SpaceBlue", "<PERSON><PERSON>", "Turquoise", "UltraViolet", "Vanilla", "Yellow"], "type": "string"}, "DashboardViewDefinition": {"type": "object", "properties": {"type": {"type": "string", "enum": ["dashboard"]}, "definition": {"$ref": "#/components/schemas/DashboardDefinition"}, "lockContent": {"type": "boolean"}}, "additionalProperties": false, "required": ["definition", "type"]}, "DashboardDefinition": {"type": "object", "properties": {"elementMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DashboardElement"}}, "rowsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DashboardRow"}}, "children": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["children", "elementMap", "rowsMap"]}, "DashboardElement": {"anyOf": [{"$ref": "#/components/schemas/DashboardBaseElement"}, {"$ref": "#/components/schemas/EmbedElement"}, {"$ref": "#/components/schemas/ImageElement"}, {"$ref": "#/components/schemas/TextElement"}, {"$ref": "#/components/schemas/InfoboxElement"}, {"$ref": "#/components/schemas/LineChartElement"}, {"$ref": "#/components/schemas/PieChartElement"}]}, "DashboardBaseElement": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "type": {"$ref": "#/components/schemas/DashboardElementType"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "DashboardElementType": {"type": "string", "enum": ["infobox", "lineChart", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "funnelChart", "embed", "image", "text"]}, "Image": {"type": "object", "properties": {"type": {"type": "string", "enum": ["image"]}, "url": {"type": "string"}}, "additionalProperties": false, "required": ["type", "url"]}, "EmbedElement": {"type": "object", "properties": {"type": {"type": "string", "enum": ["embed"]}, "embedCode": {"type": "string"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["embedCode", "id", "title", "type"]}, "ImageElement": {"type": "object", "properties": {"type": {"type": "string", "enum": ["image"]}, "images": {"type": "array", "items": {"type": "string"}}, "autoRotate": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["autoRotate", "id", "images", "title", "type"]}, "TextElement": {"type": "object", "properties": {"type": {"type": "string", "enum": ["text"]}, "content": {"type": "string"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "InfoboxElement": {"type": "object", "properties": {"type": {"type": "string", "enum": ["infobox"]}, "iconPosition": {"enum": ["left", "right"], "type": "string"}, "valueResolve": {"$ref": "#/components/schemas/DbValueResolver"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["iconPosition", "id", "title", "type", "valueResolve"]}, "DbValueResolver": {"type": "object", "properties": {"columnId": {"type": "string"}, "aggregateBy": {"enum": ["average", "count_all", "count_checked", "count_empty", "count_not_empty", "count_unchecked", "count_unique", "count_values", "max", "min", "percent_checked", "percent_empty", "percent_not_checked", "percent_not_empty", "range", "sum"], "type": "string"}, "databaseId": {"type": "string"}, "filter": {"$ref": "#/components/schemas/DbRecordFilter"}}, "additionalProperties": false, "required": ["aggregateBy", "columnId", "databaseId", "filter"]}, "LineChartElement": {"type": "object", "properties": {"type": {"type": "string", "enum": ["lineChart"]}, "recordsResolve": {"$ref": "#/components/schemas/DbRecordsResolver"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}, "columnsOrder": {"type": "array", "items": {"type": "string"}}, "columnPropsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ViewColumnCustomization"}}}, "additionalProperties": false, "required": ["columnPropsMap", "columnsOrder", "id", "recordsResolve", "title", "type"]}, "DbRecordsResolver": {"type": "object", "properties": {"databaseId": {"type": "string"}, "filter": {"$ref": "#/components/schemas/DbRecordFilter"}, "sorts": {"type": "array", "items": {"$ref": "#/components/schemas/DbRecordSort"}}}, "additionalProperties": false, "required": ["databaseId", "filter", "sorts"]}, "PieChartElement": {"type": "object", "properties": {"type": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>"]}, "recordsResolve": {"additionalProperties": false, "type": "object", "properties": {"groupByIds": {"type": "array", "items": {"type": "string"}}, "titleColId": {"type": "string"}, "databaseId": {"type": "string"}, "filter": {"$ref": "#/components/schemas/DbRecordFilter"}}, "required": ["databaseId", "filter", "groupByIds", "titleColId"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "icon": {"anyOf": [{"$ref": "#/components/schemas/Image"}, {"$ref": "#/components/schemas/Icon"}, {"$ref": "#/components/schemas/Emoji"}]}, "width": {"type": "number"}, "height": {"type": "number"}, "parentId": {"type": "string"}, "childrenIds": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["id", "recordsResolve", "title", "type"]}, "DashboardRow": {"type": "object", "properties": {"id": {"type": "string"}, "children": {"type": "array", "items": {"type": "string"}}, "updatedTs": {"type": "number"}}, "additionalProperties": false, "required": ["children", "id"]}, "ApiResponseBody__page_PermissiblePage___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"$ref": "#/components/schemas/PermissiblePage"}}, "additionalProperties": false, "required": ["page"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "PermissiblePage": {"type": "object", "properties": {"page": {"$ref": "#/components/schemas/Page"}, "views": {"type": "array", "items": {"$ref": "#/components/schemas/View"}}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "page", "views"]}, "PageRequestData": {"type": "object", "properties": {"visibility": {"$ref": "#/components/schemas/Visibility"}, "name": {"type": "string"}, "icon": {"$ref": "#/components/schemas/PickedIcon"}}, "additionalProperties": false, "required": ["icon", "name"]}, "UpdatePageRequestData": {"type": "object", "properties": {"name": {"type": "string"}, "id": {"type": "string"}, "icon": {"$ref": "#/components/schemas/PickedIcon"}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}, "visibility": {"$ref": "#/components/schemas/Visibility"}}, "additionalProperties": false, "required": ["accessLevel", "icon", "id", "name", "visibility"]}, "DeletePageRequestData": {"type": "object", "properties": {"id": {"type": "string"}}, "additionalProperties": false, "required": ["id"]}, "ApiResponseBody__page_PermissiblePageWithPermissions___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"$ref": "#/components/schemas/PermissiblePageWithPermissions"}}, "additionalProperties": false, "required": ["page"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__permissions_PagePermission_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"permissions": {"type": "array", "items": {"$ref": "#/components/schemas/PagePermission"}}}, "additionalProperties": false, "required": ["permissions"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "AddPermissionRequestBody": {"type": "object", "properties": {"emails": {"type": "array", "items": {"type": "string"}}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "emails"]}, "UpdatePermissionRequestBody": {"type": "object", "properties": {"userId": {"type": "string"}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "userId"]}, "RemovePermissionRequestBody": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["userIds"]}, "ApiResponseBody__view_View___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"view": {"$ref": "#/components/schemas/View"}}, "additionalProperties": false, "required": ["view"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "AddViewRequestData": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"$ref": "#/components/schemas/ViewType"}, "definition": {"$ref": "#/components/schemas/ViewDefinition"}}, "additionalProperties": false, "required": ["definition", "name", "type"]}, "UpdateViewRequestData": {"type": "object", "properties": {"viewId": {"type": "string"}, "isPublished": {"type": "boolean"}}, "additionalProperties": false, "required": ["isPublished", "viewId"]}, "ApiResponseBody__databases_PermissibleDatabaseWithPermissions_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"databases": {"type": "array", "items": {"$ref": "#/components/schemas/PermissibleDatabaseWithPermissions"}}}, "additionalProperties": false, "required": ["databases"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "PermissibleDatabaseWithPermissions": {"type": "object", "properties": {"permissions": {"type": "array", "items": {"$ref": "#/components/schemas/PagePermission"}}, "database": {"$ref": "#/components/schemas/Database"}, "page": {"$ref": "#/components/schemas/Page"}, "views": {"type": "array", "items": {"$ref": "#/components/schemas/View"}}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "database", "page", "permissions", "views"]}, "Database": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "definition": {"$ref": "#/components/schemas/DatabaseDefinition"}, "workspaceId": {"type": "string"}, "srcPackageName": {"type": "string"}, "srcVersionNumber": {"type": "number"}, "srcVersionName": {"type": "string"}, "packageName": {"type": "string"}, "versionNumber": {"type": "number"}, "versionName": {"type": "string"}, "createdById": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "createdById", "definition", "deletedAt", "id", "name", "packageName", "srcPackageName", "srcVersionName", "srcVersionNumber", "updatedAt", "versionName", "versionNumber", "workspaceId"]}, "DatabaseDefinition": {"type": "object", "properties": {"uniqueColumnId": {"type": "string"}, "titleColumnId": {"type": "string"}, "titleColumnIds": {"type": "array", "items": {"type": "string"}}, "columnIds": {"type": "array", "items": {"type": "string"}}, "columnsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DatabaseColumn"}}}, "additionalProperties": false, "required": ["columnIds", "columnsMap"]}, "DatabaseColumn": {"anyOf": [{"$ref": "#/components/schemas/TextColumn"}, {"$ref": "#/components/schemas/AIColumn"}, {"$ref": "#/components/schemas/UUIDColumn"}, {"$ref": "#/components/schemas/NumberColumn"}, {"$ref": "#/components/schemas/CheckboxColumn"}, {"$ref": "#/components/schemas/SelectColumn"}, {"$ref": "#/components/schemas/PersonColumn"}, {"$ref": "#/components/schemas/CreatedByColumn"}, {"$ref": "#/components/schemas/UpdatedByColumn"}, {"$ref": "#/components/schemas/LinkedColumn"}, {"$ref": "#/components/schemas/SummarizeColumn"}, {"$ref": "#/components/schemas/DerivedColumn"}, {"$ref": "#/components/schemas/ButtonGroupColumn"}, {"$ref": "#/components/schemas/DateColumn"}, {"$ref": "#/components/schemas/CreatedAtColumn"}, {"$ref": "#/components/schemas/UpdatedAtColumn"}, {"$ref": "#/components/schemas/FilesColumn"}]}, "TextColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["text"]}, "format": {"$ref": "#/components/schemas/TextColumnFormat"}, "isLong": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "TextColumnFormat": {"type": "string", "enum": ["text", "email", "phone", "url", "location"]}, "AIColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ai"]}, "prompt": {"type": "string"}, "maxWordOutput": {"type": "number"}, "attachmentColumnIds": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "prompt", "title", "type"]}, "UUIDColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["uuid"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "NumberColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["number"]}, "format": {"$ref": "#/components/schemas/NumberColumnFormat"}, "currency": {"type": "string"}, "divideBy": {"type": "number"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "NumberColumnFormat": {"type": "string", "enum": ["Number", "percentage", "currency"]}, "CheckboxColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["checkbox"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "SelectColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["select"]}, "optionIds": {"type": "array", "items": {"type": "string"}}, "optionsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/SelectOption"}}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "optionIds", "optionsMap", "title", "type"]}, "SelectOption": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "color": {"$ref": "#/components/schemas/Color"}}, "additionalProperties": false, "required": ["color", "id", "title"]}, "PersonColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["person"]}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "CreatedByColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["created-by"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "UpdatedByColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["updated-by"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "LinkedColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["linked"]}, "databaseId": {"type": "string"}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["databaseId", "id", "title", "type"]}, "SummarizeColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["summarize"]}, "targetColumnId": {"type": "string"}, "linkedDisplayColumnId": {"type": "string"}, "aggregateBy": {"$ref": "#/components/schemas/AggregateFunction"}, "title": {"type": "string"}, "format": {"enum": ["Number", "absolute", "currency", "email", "location", "percentage", "phone", "relative", "text", "url"], "type": "string"}, "withTime": {"type": "boolean"}, "currency": {"type": "string"}, "divideBy": {"type": "number"}, "id": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["aggregateBy", "id", "linkedDisplayColumnId", "targetColumnId", "title", "type"]}, "AggregateFunction": {"enum": ["average", "count_all", "count_checked", "count_empty", "count_not_empty", "count_unchecked", "count_unique", "count_values", "date_range", "earliest_date", "latest_date", "max", "min", "percent_checked", "percent_empty", "percent_not_checked", "percent_not_empty", "range", "show_original", "show_unique", "sum"], "type": "string"}, "DerivedColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["derived"]}, "derivation": {"type": "string"}, "returnType": {"enum": ["checkbox", "number", "text"], "type": "string"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "ButtonGroupColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["button-group"]}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}, "buttons": {"type": "array", "items": {"$ref": "#/components/schemas/ButtonAction"}}}, "additionalProperties": false, "required": ["id", "title", "type", "buttons"]}, "ButtonAction": {"type": "object", "properties": {"id": {"type": "string"}, "actionType": {"type": "string", "enum": ["sendEmail", "openUrl", "updateRecord", "deleteRecord", "showConfirmation", "requestInput", "callWorkflow", "expandRecord"]}, "label": {"type": "string"}, "labelFormula": {"type": "string"}, "confirmationMessage": {"type": "string"}, "confirmationMessageFormula": {"type": "string"}, "parameters": {"type": "object", "properties": {"url": {"type": "string"}, "urlFormula": {"type": "string"}, "to": {"type": "string"}, "toFormula": {"type": "string"}, "subject": {"type": "string"}, "subjectFormula": {"type": "string"}, "body": {"type": "string"}, "bodyFormula": {"type": "string"}, "updates": {"type": "string"}, "updatesFormula": {"type": "string"}}, "additionalProperties": true}, "visibleFormula": {"type": "string"}, "enabledFormula": {"type": "string"}}, "additionalProperties": false, "required": ["id", "actionType"]}, "DateColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["date"]}, "format": {"$ref": "#/components/schemas/DateColumnFormat"}, "withTime": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "DateColumnFormat": {"type": "string", "enum": ["relative", "absolute"]}, "CreatedAtColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["created-at"]}, "title": {"type": "string"}, "description": {"type": "string"}, "format": {"$ref": "#/components/schemas/DateColumnFormat"}, "id": {"type": "string"}, "withTime": {"type": "boolean"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "UpdatedAtColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["updated-at"]}, "title": {"type": "string"}, "description": {"type": "string"}, "format": {"$ref": "#/components/schemas/DateColumnFormat"}, "id": {"type": "string"}, "withTime": {"type": "boolean"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "FilesColumn": {"type": "object", "properties": {"type": {"type": "string", "enum": ["files"]}, "isMulti": {"type": "boolean"}, "id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "meta": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false, "required": ["id", "title", "type"]}, "ApiResponseBody__database_PermissibleDatabase___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"database": {"$ref": "#/components/schemas/PermissibleDatabase"}}, "additionalProperties": false, "required": ["database"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "PermissibleDatabase": {"type": "object", "properties": {"database": {"$ref": "#/components/schemas/Database"}, "page": {"$ref": "#/components/schemas/Page"}, "views": {"type": "array", "items": {"$ref": "#/components/schemas/View"}}, "accessLevel": {"$ref": "#/components/schemas/AccessLevel"}}, "additionalProperties": false, "required": ["accessLevel", "database", "page", "views"]}, "CreateDatabaseRequestData": {"type": "object", "properties": {"definition": {"$ref": "#/components/schemas/DatabaseDefinition"}, "name": {"type": "string"}, "visibility": {"$ref": "#/components/schemas/Visibility"}}, "additionalProperties": false, "required": ["name"]}, "ApiResponseBody__database_PermissibleDatabaseWithPermissions___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"database": {"$ref": "#/components/schemas/PermissibleDatabaseWithPermissions"}}, "additionalProperties": false, "required": ["database"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "ApiResponseBody__records_Record_____": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/Record"}}}, "additionalProperties": false, "required": ["records"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "Record": {"type": "object", "properties": {"id": {"type": "string"}, "databaseId": {"type": "string"}, "createdById": {"type": "string"}, "updatedById": {"type": "string"}, "recordValues": {"$ref": "#/components/schemas/RecordValues"}, "uniqueValue": {"type": "string"}, "summaryJSON": {"type": "object", "properties": {}, "additionalProperties": true}, "summaryText": {"type": "string"}, "title": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["createdAt", "createdById", "databaseId", "deletedAt", "id", "meta", "recordValues", "summaryJSON", "summaryText", "title", "uniqueValue", "updatedAt", "updatedById"]}, "RecordValues": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DatabaseColumnDbValue"}}, "DatabaseColumnDbValue": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/FileItem"}}, {"type": "array", "items": {"type": "string"}}, {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}]}, "FileItem": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "link": {"type": "string"}, "name": {"type": "string"}, "size": {"type": "number"}}, "additionalProperties": false, "required": ["id", "link", "name", "type"]}, "ApiResponseBody__records_Record______1": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/Record"}}}, "additionalProperties": false, "required": ["records"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "CreateRecordsRequestData": {"type": "object", "properties": {"valuesList": {"type": "array", "items": {"$ref": "#/components/schemas/RecordValues"}}, "onDuplicate": {"$ref": "#/components/schemas/OnDuplicateAction"}}, "additionalProperties": false, "required": ["onDuplicate", "valuesList"]}, "OnDuplicateAction": {"type": "string", "enum": ["ignore", "update", "reject"]}, "UpdateRecordRequestData": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "values": {"$ref": "#/components/schemas/RecordValues"}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["ids", "values"]}, "DeleteRecordsRequestData": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["ids"]}, "ApiResponseBody__settings_WorkspaceMemberSettings___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"settings": {"$ref": "#/components/schemas/WorkspaceMemberSettings"}}, "additionalProperties": false, "required": ["settings"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "WorkspaceMemberSettings": {"type": "object", "properties": {"id": {"type": "number"}, "userId": {"type": "string"}, "workspaceId": {"type": "string"}, "settings": {"$ref": "#/components/schemas/KeyValueStore"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "id", "settings", "updatedAt", "userId", "workspaceId"]}, "ApiResponseBody__campaign_Campaign___": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"campaign": {"$ref": "#/components/schemas/Campaign"}}, "additionalProperties": false, "required": ["campaign"]}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "Campaign": {"type": "object", "properties": {"id": {"type": "string"}, "parentId": {"type": "string"}, "subject": {"type": "string"}, "contentText": {"type": "string"}, "cc": {"type": "array", "items": {"type": "string"}}, "bcc": {"type": "array", "items": {"type": "string"}}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignAttachment"}}, "type": {"$ref": "#/components/schemas/CampaignType"}, "workspaceId": {"type": "string"}, "createdById": {"type": "string"}, "updatedById": {"type": "string"}, "databaseId": {"type": "string"}, "targetDatabaseId": {"type": "string"}, "targetColId": {"type": "string"}, "recordIds": {"type": "array", "items": {"type": "string"}}, "targetRecords": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignTarget"}}, "targetRecordIdsToSkip": {"type": "array", "items": {"type": "string"}}, "sequenceRecordFilter": {"$ref": "#/components/schemas/DbRecordFilter"}, "sequenceTarget": {"$ref": "#/components/schemas/SequenceTarget"}, "status": {"$ref": "#/components/schemas/CampaignStatus"}, "failedReason": {"type": "string"}, "senderId": {"type": "string"}, "deliverAt": {"type": "string", "format": "date-time"}, "deliveredAt": {"type": "string", "format": "date-time"}, "deliveryHeartbeatAt": {"type": "string", "format": "date-time"}, "sendAtLocalTime": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "auditLog": {"type": "array", "items": {"type": "string"}}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["attachments", "auditLog", "bcc", "cc", "contentText", "createdAt", "createdById", "databaseId", "deletedAt", "deliverAt", "deliveredAt", "deliveryHeartbeatAt", "failedReason", "id", "meta", "parentId", "recordIds", "sendAtLocalTime", "senderId", "sequenceRecord<PERSON>ilt<PERSON>", "sequenceTarget", "status", "subject", "targetColId", "targetDatabaseId", "targetRecordIdsToSkip", "targetRecords", "type", "updatedAt", "updatedById", "workspaceId"]}, "CampaignAttachment": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "link": {"type": "string"}, "type": {"type": "string"}, "size": {"type": "number"}, "sizeReadable": {"type": "string"}}, "additionalProperties": false, "required": ["id", "link", "size", "sizeReadable", "title", "type"]}, "CampaignType": {"type": "string", "enum": ["campaign", "sequence"]}, "CampaignTarget": {"type": "object", "properties": {"recordId": {"type": "string"}, "targetRecordId": {"type": "string"}}, "additionalProperties": false, "required": ["recordId", "targetRecordId"]}, "SequenceTarget": {"type": "string", "enum": ["all", "opened", "clicked", "did_not_open", "did_not_click"]}, "CampaignStatus": {"type": "string", "enum": ["draft", "in_review", "published", "scheduled", "queued", "sending", "failed", "sent", "partial_delivery"]}, "CreateCampaignData": {"type": "object", "properties": {"databaseId": {"type": "string"}, "targetColId": {"type": "string"}, "recordIds": {"type": "array", "items": {"type": "string"}}, "targetEntityScope": {"$ref": "#/components/schemas/TargetEntityScope"}}, "additionalProperties": false, "required": ["databaseId", "recordIds"]}, "TargetEntityScope": {"type": "string", "enum": ["all", "first"]}, "ApiResponseBody_GetCampaignResponse_": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ApiResponseStatus"}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/GetCampaignResponse"}}, "additionalProperties": false, "required": ["data", "message", "status"]}, "GetCampaignResponse": {"type": "object", "properties": {"campaign": {"$ref": "#/components/schemas/Campaign"}, "statsMap": {"$ref": "#/components/schemas/CampaignStatsMap"}, "emailsMap": {"$ref": "#/components/schemas/CampaignEmailsMap"}, "analytics": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignAnalytic"}}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/Record"}}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "database": {"$ref": "#/components/schemas/Database"}, "adjacentDatabases": {"$ref": "#/components/schemas/AdjacentDatabases"}}, "additionalProperties": false, "required": ["adjacentDatabases", "analytics", "campaign", "database", "emailsMap", "links", "records", "statsMap"]}, "CampaignStatsMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/CampaignStats"}}, "CampaignStats": {"type": "object", "properties": {"campaign": {"$ref": "#/components/schemas/Campaign"}, "emails": {"type": "number"}, "sent": {"type": "number"}, "opened": {"type": "number"}, "clicked": {"type": "number"}, "bounced": {"type": "number"}, "unsubscribed": {"type": "number"}}, "additionalProperties": false, "required": ["bounced", "campaign", "clicked", "emails", "opened", "sent", "unsubscribed"]}, "CampaignEmailsMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignEmail"}}}, "CampaignEmail": {"type": "object", "properties": {"id": {"type": "number"}, "campaignId": {"type": "string"}, "targetRecordId": {"type": "string"}, "recordId": {"type": "string"}, "status": {"$ref": "#/components/schemas/CampaignEmailStatus"}, "skippedReason": {"type": "string"}, "emailTo": {"$ref": "#/components/schemas/EmailUser"}, "emailFrom": {"$ref": "#/components/schemas/EmailUser"}, "cc": {"type": "array", "items": {"type": "string"}}, "bcc": {"type": "array", "items": {"type": "string"}}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignAttachment"}}, "subject": {"type": "string"}, "contentText": {"type": "string"}, "messageId": {"type": "string"}, "sentAt": {"type": "string", "format": "date-time"}, "isSent": {"type": "boolean"}, "bouncedAt": {"type": "string", "format": "date-time"}, "deliveredAt": {"type": "string", "format": "date-time"}, "spamReportedAt": {"type": "string", "format": "date-time"}, "isSkipped": {"type": "boolean"}, "isBounced": {"type": "boolean"}, "isUnsubscribed": {"type": "boolean"}, "isDelivered": {"type": "boolean"}, "isSpamReported": {"type": "boolean"}, "unsubscribedAt": {"type": "string", "format": "date-time"}, "unsubscribeReason": {"type": "string"}, "bounceReason": {"type": "string"}, "deliveryException": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "auditLog": {"type": "array", "items": {"type": "string"}}, "meta": {"$ref": "#/components/schemas/KeyValueStore"}}, "additionalProperties": false, "required": ["attachments", "auditLog", "bcc", "bounceReason", "bouncedAt", "campaignId", "cc", "contentText", "createdAt", "deletedAt", "deliveredAt", "deliveryException", "emailFrom", "emailTo", "id", "isBounced", "isDelivered", "isSent", "isSkipped", "isSpamReported", "isUnsubscribed", "messageId", "meta", "recordId", "sentAt", "skippedReason", "spamReportedAt", "status", "subject", "targetRecordId", "unsubscribeReason", "unsubscribedAt", "updatedAt"]}, "CampaignEmailStatus": {"type": "string", "enum": ["in_review", "queued", "sending", "failed", "sent", "skipped"]}, "EmailUser": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}}, "additionalProperties": false, "required": ["email", "name"]}, "CampaignAnalytic": {"type": "object", "properties": {"id": {"type": "number"}, "campaignId": {"type": "string"}, "emailId": {"type": "number"}, "linkId": {"type": "number"}, "openCount": {"type": "number"}, "clickCount": {"type": "number"}, "eventAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["campaignId", "clickCount", "createdAt", "deletedAt", "emailId", "eventAt", "id", "linkId", "openCount", "updatedAt"]}, "Link": {"type": "object", "properties": {"id": {"type": "number"}, "linkUrl": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "required": ["createdAt", "deletedAt", "id", "linkUrl", "updatedAt"]}, "AdjacentDatabases": {"type": "object", "additionalProperties": {"type": "object", "properties": {"database": {"$ref": "#/components/schemas/Database"}, "page": {"$ref": "#/components/schemas/Page"}, "recordsMap": {"$ref": "#/components/schemas/RecordIdMap"}, "error": {"type": "string"}}, "additionalProperties": false}}, "RecordIdMap": {"type": "object", "additionalProperties": {"type": "object", "properties": {"record": {"$ref": "#/components/schemas/Record"}, "processedRecord": {"$ref": "#/components/schemas/ProcessedDbRecord"}}, "additionalProperties": false, "required": ["record"]}}, "ProcessedDbRecord": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string"}]}, "updatedAt": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string"}]}, "createdBy": {"$ref": "#/components/schemas/Person"}, "updatedBy": {"$ref": "#/components/schemas/Person"}, "uniqueValue": {"type": "string"}, "recordValues": {"$ref": "#/components/schemas/RecordValues"}, "valuesText": {"type": "string"}, "processedRecordValues": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DatabaseColumnReturnValue"}}}, "additionalProperties": false, "required": ["createdAt", "created<PERSON>y", "id", "processedRecordValues", "recordValues", "uniqueValue", "updatedAt", "updatedBy", "valuesText"]}, "Person": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "image": {"$ref": "#/components/schemas/Image"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}, "additionalProperties": false, "required": ["firstName", "id", "image", "lastName", "title"]}, "DatabaseColumnReturnValue": {"anyOf": [{"type": "string", "format": "date-time"}, {"$ref": "#/components/schemas/Person"}, {"type": "array", "items": {"$ref": "#/components/schemas/FileItem"}}, {"type": "array", "items": {"$ref": "#/components/schemas/SelectOption"}}, {"type": "array", "items": {"$ref": "#/components/schemas/Person"}}, {"type": "array", "items": {"$ref": "#/components/schemas/LinkedItem"}}, {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}]}, "LinkedItem": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}}, "additionalProperties": false, "required": ["id", "title"]}}, "securitySchemes": {"jwt": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "servers": [{"url": "http://localhost:3030/api/v1", "description": "Localhost"}, {"url": "https://api.opendashboard.app/api/v1", "description": "Prod"}, {"url": "https://api.stage.opendashboard.app/api/v1", "description": "Stage"}]}