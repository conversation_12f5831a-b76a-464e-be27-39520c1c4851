import {Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {AddOnsQuota, UsageLimits} from "../businessLogic/subscription";

export enum SubscriptionStatus {
    Active = 'active',
    Cancelled = 'canceled',
    Past_Due = 'past_due',
    Unpaid = 'unpaid',
    Incomplete = 'incomplete',
    Incomplete_expired = 'incomplete_expired',
    Trialing = 'trailing',
    Paused = 'paused'
}

@Entity()
export class Subscription {

    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({type: "varchar"})
    workspaceId: string

    @Column({type: "varchar", nullable: false, unique: true})
    stripeSubscriptionId: string

    @Index()
    @Column({type: 'varchar'})
    status: SubscriptionStatus

    @Column({type: "varchar", nullable: true})
    planId: string

    @Column({type: "varchar", nullable: true})
    priceId: string

    @Column({type: "int", default: 0})
    anchorDay: number

    @Column({type: "int", default: 0})
    addOnUsers: number

    @Column({type: "timestamp", nullable: true})
    startsAt: Date

    @Column({type: "timestamp", nullable: true})
    endsAt: Date

    @Column({type: "timestamp", nullable: true})
    endedAt: Date

    @CreateDateColumn({type: 'timestamp', nullable: false})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', nullable: false})
    updatedAt: Date

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

    @Column({type: "json", nullable: true})
    meta: object

}