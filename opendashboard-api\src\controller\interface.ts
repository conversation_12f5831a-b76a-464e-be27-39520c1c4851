export enum ApiResponseStatus {
    Ok = 'ok', Error = 'error'
}

export interface GenericApiResponseBody extends ApiResponseBody<any> {

}

export interface ApiResponseBody<T> {
    status: ApiResponseStatus,
    message: string
    data: T
}


export enum ApiMessage {
    EmailVerifiedSuccessfully = 'Your email was verifed successfully',
    VerifiedSuccessfully = 'The requested entity was verified successfully',
    PasswordResetEmailSent = 'Check your email for instructions on how to reset your password',
    PasswordResetSuccessful = 'Your password was reset successful',
    ActionSuccessful = 'The requested action is successful',
    PasswordUpdatedSuccessfully = 'Your password was updated successfully',
    NoMessage = '',
    DuplicateEntryFoundWhileAdding = 'Duplicate entry for unique field was found while adding records',
    DuplicateEntryFoundWhileUpdating = 'Duplicate entry for unique field was found while updating record',
    WorkspaceSetupAlreadyCompleted = 'Workspace setup previously completed',
}
