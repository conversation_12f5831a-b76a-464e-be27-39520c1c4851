import {getRepository} from '../connection/db';
import {BaseService} from './service';
import {Repository} from 'typeorm/repository/Repository';
import {ServerProcessingError, UniqueConstraintError} from "../errors/AppError";
import {WorkspaceInvitation} from "../entity/WorkspaceInvitation";
import {Brackets, MoreThanOrEqual} from "typeorm";

export interface CreateWorkspaceInvitation extends Pick<WorkspaceInvitation, 'invitedById' | 'userId' | 'email' | 'role' | 'workspaceId' | 'token' | 'expiresAt'> {
}

export class WorkspaceInvitationService extends BaseService<WorkspaceInvitation> {

    initRepository = (): Repository<WorkspaceInvitation> => {
        return getRepository(WorkspaceInvitation);
    }

    // createWorkspaceInvitation = async (data: CreateWorkspaceInvitation) => {
    //     try {
    //         return await this.insert(data);
    //     } catch (err) {
    //         if (err.message.includes("Duplicate entry")) throw new UniqueConstraintError('userId')
    //         throw new ServerProcessingError(err.message)
    //     }
    // }

    findInvitation = async (workspaceId: string, userId: string, email: string, token: string): Promise<WorkspaceInvitation> => {
        const repo = this.getRepository()
        const qB = repo.createQueryBuilder().select().where({ workspaceId, token, expiresAt: MoreThanOrEqual(new Date()) })
        qB.andWhere(
            new Brackets(qb => {
                qb.orWhere({ userId });
                qb.orWhere({ email });
            })
        );

        return await qB.getOne();
    }


}




