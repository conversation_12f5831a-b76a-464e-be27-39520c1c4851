import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";


@Entity()
export class TemplateReview {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    templateId: string

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    templateReleaseId: number

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    templateListingId: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Index()
    @Column({type: 'bool', default: false})
    isPublishedAsCreator: boolean

    @Index()
    @Column({type: 'int', nullable: true, unsigned: true})
    parentId: number

    @Index()
    @Column({type: 'int', nullable: true, unsigned: true})
    rating: number

    @Column({type: 'text', nullable: true})
    reviewText: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}