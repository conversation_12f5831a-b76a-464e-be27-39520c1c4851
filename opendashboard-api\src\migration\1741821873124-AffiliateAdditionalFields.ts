import { MigrationInterface, QueryRunner } from "typeorm";

export class AffiliateAdditionalFields1741821873124 implements MigrationInterface {
    name = 'AffiliateAdditionalFields1741821873124'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_5e2e1401ba70e158555b8e15e7\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_15b198166952da6cf5154c82ff\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_92de5823d883a06ba9383ff54e\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_f6ada61cfd4bb7f1edc44fe7a8\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_d57a4b8a4a6914802a2ea7c560\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_fc57b171414d4cd2e783714de6\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_c9ce31d72a490e812b3dfeeba3\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_647ab0e7d9363af9be1e4759e9\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_28e95f456534221e6b1d9642b5\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_6d3fce6dc92dd3238d9d980f3a\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_05494dfb86f7fa0c9859761f30\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_a32a4cb845116991803f96a25e\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_43b05a3ea81284157096f906ff\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_cbf9ea3b24eca3261319fcd215\` ON \`affiliate_earning\``);
        await queryRunner.query(`CREATE TABLE \`affiliate_analytic\` (\`id\` int NOT NULL AUTO_INCREMENT, \`affiliateId\` varchar(255) NOT NULL, \`locationId\` varchar(255) NULL, \`clickCount\` int NOT NULL DEFAULT '0', \`eventAt\` datetime NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_cbcc4fd23422e54b4f757b7bc8\` (\`eventAt\`), INDEX \`IDX_0c21d11d3301dc0b557829980d\` (\`createdAt\`), INDEX \`IDX_64d7a2088d56968af4934bb802\` (\`updatedAt\`), INDEX \`IDX_59507764cd50cd5781b312ecb9\` (\`deletedAt\`), UNIQUE INDEX \`IDX_b4974344e5c8d33354af2cd5c8\` (\`affiliateId\`, \`locationId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`affiliate_earning\` ADD \`billingCycleId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`affiliate_earning\` ADD UNIQUE INDEX \`IDX_611aad83edbdfb10032500fb5b\` (\`billingCycleId\`)`);
        await queryRunner.query(`ALTER TABLE \`affiliate\` ADD \`approvedAt\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`affiliate\` ADD \`approvedByUserId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` ADD \`amountPaidInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000'`);
        await queryRunner.query(`ALTER TABLE \`payout_method\` ADD UNIQUE INDEX \`IDX_7e29635803df96b4a86063a270\` (\`creatorId\`)`);
        await queryRunner.query(`ALTER TABLE \`payout_method\` ADD UNIQUE INDEX \`IDX_782cb76e6128fec55d9de98b16\` (\`affiliateId\`)`);
        await queryRunner.query(`ALTER TABLE \`affiliate_earning\` ADD UNIQUE INDEX \`IDX_cbf9ea3b24eca3261319fcd215\` (\`workspaceCreditId\`)`);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` CHANGE \`costInCents\` \`costInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000'`);
        await queryRunner.query(`CREATE INDEX \`IDX_8387275e75be9b7e8db2a58331\` ON \`payout_method\` (\`payoutProvider\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_da81fd04d414fb18bc9740bb2d\` ON \`payout_method\` (\`payoutProviderReference\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_a789fb8bb32911a86075f32893\` ON \`payout_method\` (\`isVerified\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_fa929d8a86d4e5a49540ff021a\` ON \`payout_method\` (\`currency\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_f670e5f30a00b0840d8eb5c0a1\` ON \`payout\` (\`creatorId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_b51f49f8d9e319507537cd8a39\` ON \`payout\` (\`affiliateId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_0ada6814999bc601d06558dcc4\` ON \`payout\` (\`payoutProvider\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_71b2c0bb67f9f46bca15185aab\` ON \`payout\` (\`payoutProviderReference\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_e14fce2a74a24c3158b24aab45\` ON \`payout\` (\`amountInCents\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_04431ad2f7f84fd347a2ed474a\` ON \`payout\` (\`amountInLocalCurrency\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_bfe8abdf0d935ec49d41098450\` ON \`payout\` (\`currency\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_da15853e5c12fc6a619daafa49\` ON \`payout\` (\`payoutStatus\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_01674bfd62b92ce1d5bf210825\` ON \`affiliate\` (\`isApproved\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c19b09c549e55d82cf67ec20e6\` ON \`affiliate\` (\`approvedAt\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_8d8f818a30c0dcd07be7ef062d\` ON \`affiliate\` (\`approvedByUserId\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_8d8f818a30c0dcd07be7ef062d\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_c19b09c549e55d82cf67ec20e6\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_01674bfd62b92ce1d5bf210825\` ON \`affiliate\``);
        await queryRunner.query(`DROP INDEX \`IDX_da15853e5c12fc6a619daafa49\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_bfe8abdf0d935ec49d41098450\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_04431ad2f7f84fd347a2ed474a\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_e14fce2a74a24c3158b24aab45\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_71b2c0bb67f9f46bca15185aab\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_0ada6814999bc601d06558dcc4\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_b51f49f8d9e319507537cd8a39\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_f670e5f30a00b0840d8eb5c0a1\` ON \`payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_fa929d8a86d4e5a49540ff021a\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_a789fb8bb32911a86075f32893\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_da81fd04d414fb18bc9740bb2d\` ON \`payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_8387275e75be9b7e8db2a58331\` ON \`payout_method\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` CHANGE \`costInCents\` \`costInCents\` decimal(10,0) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`affiliate_earning\` DROP INDEX \`IDX_cbf9ea3b24eca3261319fcd215\``);
        await queryRunner.query(`ALTER TABLE \`payout_method\` DROP INDEX \`IDX_782cb76e6128fec55d9de98b16\``);
        await queryRunner.query(`ALTER TABLE \`payout_method\` DROP INDEX \`IDX_7e29635803df96b4a86063a270\``);
        await queryRunner.query(`ALTER TABLE \`workspace_credit\` DROP COLUMN \`amountPaidInCents\``);
        await queryRunner.query(`ALTER TABLE \`affiliate\` DROP COLUMN \`approvedByUserId\``);
        await queryRunner.query(`ALTER TABLE \`affiliate\` DROP COLUMN \`approvedAt\``);
        await queryRunner.query(`ALTER TABLE \`affiliate_earning\` DROP INDEX \`IDX_611aad83edbdfb10032500fb5b\``);
        await queryRunner.query(`ALTER TABLE \`affiliate_earning\` DROP COLUMN \`billingCycleId\``);
        await queryRunner.query(`DROP INDEX \`IDX_b4974344e5c8d33354af2cd5c8\` ON \`affiliate_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_59507764cd50cd5781b312ecb9\` ON \`affiliate_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_64d7a2088d56968af4934bb802\` ON \`affiliate_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_0c21d11d3301dc0b557829980d\` ON \`affiliate_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_cbcc4fd23422e54b4f757b7bc8\` ON \`affiliate_analytic\``);
        await queryRunner.query(`DROP TABLE \`affiliate_analytic\``);
        await queryRunner.query(`CREATE INDEX \`IDX_cbf9ea3b24eca3261319fcd215\` ON \`affiliate_earning\` (\`workspaceCreditId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_43b05a3ea81284157096f906ff\` ON \`payout\` (\`payoutProvider\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_a32a4cb845116991803f96a25e\` ON \`payout\` (\`payoutProviderReference\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_05494dfb86f7fa0c9859761f30\` ON \`payout\` (\`amountInCents\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_6d3fce6dc92dd3238d9d980f3a\` ON \`payout\` (\`amountInLocalCurrency\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_28e95f456534221e6b1d9642b5\` ON \`payout\` (\`currency\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_647ab0e7d9363af9be1e4759e9\` ON \`payout\` (\`payoutStatus\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`payout\` (\`creatorId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c9ce31d72a490e812b3dfeeba3\` ON \`payout\` (\`affiliateId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_fc57b171414d4cd2e783714de6\` ON \`payout_method\` (\`payoutProvider\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_d57a4b8a4a6914802a2ea7c560\` ON \`payout_method\` (\`payoutProviderReference\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_f6ada61cfd4bb7f1edc44fe7a8\` ON \`payout_method\` (\`isVerified\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_92de5823d883a06ba9383ff54e\` ON \`payout_method\` (\`currency\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_15b198166952da6cf5154c82ff\` ON \`payout_method\` (\`creatorId\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_5e2e1401ba70e158555b8e15e7\` ON \`payout_method\` (\`affiliateId\`)`);
    }

}
