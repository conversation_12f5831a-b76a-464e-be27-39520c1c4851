import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import {AccessLevel} from "./common"


@Entity()
@Index(["workspaceId", "pageId", "userId"], {unique: true})
export class PagePermission {

    @PrimaryGeneratedColumn()
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false, length: 100})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: false, length: 100})
    pageId: string

    @Index()
    @Column({type: 'varchar', nullable: false, length: 100})
    userId: string

    @Column({type: 'varchar', nullable: true, default: AccessLevel.Edit, length: 20})
    accessLevel: AccessLevel

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}