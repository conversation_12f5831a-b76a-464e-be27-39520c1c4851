import {WorkspaceService} from "../service/workspace";
import {WorkspaceMember, WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {In, IsNull, Like, Not} from "typeorm";
import {Database} from "../entity/Database";
import {Page} from "../entity/Page";
import {Record} from "../entity/Record";
import {WorkspaceDomain} from "../entity/WorkspaceDomain";
import {WorkspaceSenderEmail} from "../entity/WorkspaceSenderEmail";
import {WorkspaceStatsService} from "../service/workspaceStats";
import {WorkspaceStats} from "../entity/WorkspaceStats";
import {DomainService} from "../service/domain";

export const RecalculateWorkspaceStats = async (workspaceId: string) => {
    const domain = await new DomainService().findOne({domain: 'opendashboard.site'})
    const s = new WorkspaceService()
    const repo = s.getRepository()
    const qB = repo.createQueryBuilder("w")
        .select('id')
        .addSelect(qb => {
            return qb.select("count(DISTINCT userId)")
                .from(WorkspaceMember, "wM")
                .where({
                    role: In([WorkspaceMemberRole.Admin, WorkspaceMemberRole.Owner, WorkspaceMemberRole.Member]),
                    workspaceId
                })
        }, "users")
        .addSelect(qb => {
            return qb.select("count(DISTINCT id)")
                .from(WorkspaceMember, "wM")
                .where({
                    role: In([WorkspaceMemberRole.Collaborator]),
                    workspaceId
                })
        }, "collaborators")
        .addSelect(qb => {
            return qb.select("count(id)")
                .from(Database, "dB")
                .where({workspaceId, deletedAt: IsNull()})
        }, "databases")
        .addSelect(qb => {
            return qb.select("count(id)")
                .from(Page, "pG")
                .where({workspaceId, deletedAt: IsNull()})
        }, "pages")
        .addSelect(qb => {
            return qb.select("MAX(recordCount)")
                .from(subQuery => {
                    return subQuery.select("databaseId")
                        .addSelect("count(id)", "recordCount")
                        .from(Record, "r")
                        .where("databaseId IN (SELECT id FROM `database` WHERE workspaceId=:workspaceId AND deletedAt IS NULL) AND deletedAt IS NULL", {workspaceId})
                        .groupBy("databaseId")
                }, 'sub')
        }, 'records')
        .addSelect(qb => {
            return qb.select("count(id)")
                .from(WorkspaceDomain, 'wD')
                .where({workspaceId, deletedAt: IsNull(), domainId: Not(domain?.id || 0)})
        }, 'sendingDomains')
        .addSelect(qb => {
            return qb.select("count(id)")
                .from(WorkspaceSenderEmail, 'wSE')
                .where({workspaceId, deletedAt: IsNull(), email: Not(Like('%@opendashboard.site'))})
        }, 'sendingEmails')
    const rawResult = await qB.getRawMany()

    const data = rawResult[0]

    const stats = {
        users: data['users'],
        collaborators: data['collaborators'],
        records: data['records'],
        sendingDomains: data['sendingDomains'],
        sendingEmails: data['sendingEmails'],
        workspaceId
    }

    const s2 = new WorkspaceStatsService()
    const qB2 = s2.getRepository()
        .createQueryBuilder()
        .insert()
        .into(WorkspaceStats)
        .values([stats])
        .orIgnore()

    let [query, parameters] = qB2.getQueryAndParameters()
    const updateList = []

    updateList.push("users=VALUES(users)")
    updateList.push("collaborators=VALUES(collaborators)")
    updateList.push("records=VALUES(records)")
    updateList.push("sendingDomains=VALUES(sendingDomains)")
    updateList.push("sendingEmails=VALUES(sendingEmails)")

    const orUpdateQuery = updateList.join(", ");
    if (orUpdateQuery) {
        query += ` ON DUPLICATE KEY UPDATE ${orUpdateQuery}`;
    }
    const result = await repo.manager.query(query, parameters);


    return {
        stats, result
    }
}

export const GetWorkspaceStats = async (workspaceId: string): Promise<WorkspaceStats> => {
    const s = new WorkspaceStatsService()
    return (await s.findOne({workspaceId}))
}

