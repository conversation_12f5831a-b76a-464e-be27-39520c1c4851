import { MigrationInterface, QueryRunner } from "typeorm";

export class TemplatesMigration1734913719985 implements MigrationInterface {
    name = 'TemplatesMigration1734913719985'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`admin_member\` (\`id\` int NOT NULL AUTO_INCREMENT, \`userId\` varchar(255) NOT NULL, \`role\` varchar(255) NOT NULL, \`invitedById\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, UNIQUE INDEX \`IDX_97ff8121ada1561ca0edf5d910\` (\`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`category\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL, \`description\` varchar(255) NULL, \`slug\` varchar(255) NOT NULL, \`type\` varchar(255) NOT NULL DEFAULT 'all', \`coverImage\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_cb73208f151aa71cdd78f662d7\` (\`slug\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`creator\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`domain\` varchar(255) NOT NULL, \`stripeAccountId\` varchar(255) NULL, \`canBeEmailed\` tinyint NOT NULL DEFAULT 0, \`contactEmail\` varchar(255) NULL, \`shortDescription\` varchar(500) NULL DEFAULT 'I create beautiful and functional Opendashboard templates', \`links\` json NULL, \`logo\` varchar(255) NULL, \`coverImage\` varchar(255) NULL, \`createdById\` varchar(255) NOT NULL, \`ownerId\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`deletionReason\` varchar(255) NULL, \`timezone\` varchar(255) NOT NULL DEFAULT 'UTC', \`isSupportAccessEnabled\` tinyint NOT NULL DEFAULT 0, \`isFunctionalityLimited\` tinyint NOT NULL DEFAULT 0, \`meta\` json NULL, INDEX \`IDX_87e0d3c711d81af02a228c9c38\` (\`stripeAccountId\`), INDEX \`IDX_51f45959f9182350fc0150fee2\` (\`canBeEmailed\`), INDEX \`IDX_54783472c3455a47281d3419a9\` (\`ownerId\`), INDEX \`IDX_e229c0f476ae8011ff4ac9fc08\` (\`timezone\`), INDEX \`IDX_f95942cef40fbb722f03a6f45a\` (\`isSupportAccessEnabled\`), UNIQUE INDEX \`IDX_518d013fa0147414561fdd4bd2\` (\`domain\`), UNIQUE INDEX \`IDX_d5b9164239598ab47adc670acf\` (\`workspaceId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`creator_member\` (\`id\` int NOT NULL AUTO_INCREMENT, \`creatorId\` varchar(255) NOT NULL, \`userId\` varchar(255) NOT NULL, \`role\` varchar(255) NOT NULL, \`invitedById\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_5cddff2c8983cd4b57874a2a25\` (\`creatorId\`), INDEX \`IDX_b112b9f957d8eb4453c8db092b\` (\`userId\`), UNIQUE INDEX \`IDX_0ca624a904a8230ce4ab72930d\` (\`creatorId\`, \`userId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_purchase\` (\`id\` int NOT NULL AUTO_INCREMENT, \`templateId\` varchar(255) NOT NULL, \`templateReleaseId\` int UNSIGNED NOT NULL DEFAULT '0', \`templateListingId\` int UNSIGNED NOT NULL DEFAULT '0', \`purchasedById\` varchar(255) NOT NULL, \`purchasedAt\` timestamp NULL, \`workspaceLicense\` varchar(255) NOT NULL, \`purchaseStatus\` varchar(255) NULL, \`workspaceId\` varchar(255) NULL, \`amountPaidInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`amountInLocalCurrency\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`currency\` varchar(255) NULL, \`creatorEarningsInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`paymentProcessFeesInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`paymentProcessFeesInLocalCurrency\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`platformProfitInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`paymentProcessorReference\` varchar(255) NULL, \`paymentProcessor\` varchar(255) NULL, \`meta\` json NULL, \`payoutId\` int NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_008ad534e63348dc3353132e3e\` (\`templateId\`), INDEX \`IDX_fda2282401ee51a264cc1f9d87\` (\`templateReleaseId\`), INDEX \`IDX_c7b88df5c1fc24c689559eb20b\` (\`templateListingId\`), INDEX \`IDX_f42e683a6b5b7b1e9e1450917f\` (\`purchasedById\`), INDEX \`IDX_072df857c81b570525305e98a1\` (\`workspaceLicense\`), INDEX \`IDX_f24a94c847047148393de85327\` (\`purchaseStatus\`), INDEX \`IDX_254c86a3867c7a5a4926347b50\` (\`workspaceId\`), INDEX \`IDX_34e83582a9047fb5f424cc4e86\` (\`amountPaidInCents\`), INDEX \`IDX_9a37ca9ba06b427c4dbcf014ca\` (\`currency\`), INDEX \`IDX_3f0926be43c973598b3b0cdf32\` (\`creatorEarningsInCents\`), INDEX \`IDX_15d47805a81d9e65fcae285e4f\` (\`paymentProcessFeesInCents\`), INDEX \`IDX_c5d6050f8bdde85b96300bd291\` (\`paymentProcessFeesInLocalCurrency\`), INDEX \`IDX_209d2c7dcb935d6e5e28b786e9\` (\`platformProfitInCents\`), INDEX \`IDX_db2ae30ed73073c91faddf0b34\` (\`paymentProcessorReference\`), INDEX \`IDX_a9327c8fe19d25aab86a44a76c\` (\`paymentProcessor\`), INDEX \`IDX_67d45978a1fdf30be9abb18335\` (\`payoutId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`creator_payout\` (\`id\` int NOT NULL AUTO_INCREMENT, \`creatorId\` varchar(255) NOT NULL, \`payoutProvider\` varchar(255) NULL, \`payoutProviderReference\` varchar(255) NULL, \`amountInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`amountInLocalCurrency\` decimal(12,4) NOT NULL DEFAULT '0.0000', \`currency\` varchar(255) NULL, \`payoutStatus\` varchar(255) NULL, \`meta\` json NULL, \`auditLog\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_43b05a3ea81284157096f906ff\` (\`payoutProvider\`), INDEX \`IDX_a32a4cb845116991803f96a25e\` (\`payoutProviderReference\`), INDEX \`IDX_05494dfb86f7fa0c9859761f30\` (\`amountInCents\`), INDEX \`IDX_6d3fce6dc92dd3238d9d980f3a\` (\`amountInLocalCurrency\`), INDEX \`IDX_28e95f456534221e6b1d9642b5\` (\`currency\`), INDEX \`IDX_647ab0e7d9363af9be1e4759e9\` (\`payoutStatus\`), UNIQUE INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` (\`creatorId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`creator_payout_method\` (\`id\` int NOT NULL AUTO_INCREMENT, \`creatorId\` varchar(255) NOT NULL, \`payoutProvider\` varchar(255) NULL, \`payoutProviderReference\` varchar(255) NULL, \`isVerified\` tinyint NOT NULL DEFAULT 0, \`currency\` varchar(255) NULL, \`meta\` json NULL, \`auditLog\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_fc57b171414d4cd2e783714de6\` (\`payoutProvider\`), INDEX \`IDX_d57a4b8a4a6914802a2ea7c560\` (\`payoutProviderReference\`), INDEX \`IDX_f6ada61cfd4bb7f1edc44fe7a8\` (\`isVerified\`), INDEX \`IDX_92de5823d883a06ba9383ff54e\` (\`currency\`), UNIQUE INDEX \`IDX_15b198166952da6cf5154c82ff\` (\`creatorId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`ip_record\` (\`id\` int NOT NULL AUTO_INCREMENT, \`ipAddress\` varchar(255) NOT NULL, \`city\` varchar(255) NOT NULL, \`state\` varchar(255) NOT NULL, \`country\` varchar(255) NOT NULL, \`timezone\` varchar(255) NOT NULL, \`coordinates\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_7236d9c9ec8fda8fb76963a14d\` (\`ipAddress\`), INDEX \`IDX_ad1117f818b9efea957e137c7c\` (\`createdAt\`), INDEX \`IDX_f0dd2c1459c24ee7ccd1aaee33\` (\`updatedAt\`), INDEX \`IDX_7f16c661fb92cf3a85ebb6f13c\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`tag\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL, \`description\` varchar(255) NULL, \`slug\` varchar(255) NOT NULL, \`coverImage\` varchar(255) NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`hits\` int NOT NULL DEFAULT '1', INDEX \`IDX_c974f5f41100d1f721776bf633\` (\`hits\`), UNIQUE INDEX \`IDX_3413aed3ecde54f832c4f44f04\` (\`slug\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template\` (\`id\` varchar(36) NOT NULL, \`creatorId\` varchar(255) NOT NULL, \`createdById\` varchar(255) NOT NULL, \`slug\` varchar(255) NULL, \`marketplaceReleaseId\` int NULL, \`marketplaceListingId\` int NULL, \`isListedInMarketplace\` tinyint NOT NULL DEFAULT 0, \`listedAt\` timestamp NULL, \`listingUpdatedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_b0dcd53261ead0ec614577e1ec\` (\`creatorId\`), INDEX \`IDX_55980b6e1dfe1c22667845f3b9\` (\`createdById\`), INDEX \`IDX_d20ce59851706e2f0161115af0\` (\`marketplaceReleaseId\`), INDEX \`IDX_f431a22e366be52d742afeeafe\` (\`marketplaceListingId\`), INDEX \`IDX_87c15c207faa0eda3eb7209e54\` (\`isListedInMarketplace\`), UNIQUE INDEX \`IDX_fe6443288b146678da01f3a5d2\` (\`creatorId\`, \`slug\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_install\` (\`id\` int NOT NULL AUTO_INCREMENT, \`templateId\` varchar(255) NOT NULL, \`templateReleaseId\` int UNSIGNED NOT NULL DEFAULT '0', \`objectMappings\` json NULL, \`prepareData\` json NULL, \`installedById\` varchar(255) NOT NULL, \`installStatus\` varchar(255) NULL, \`upgradedFromReleaseId\` int UNSIGNED NOT NULL DEFAULT '0', \`workspaceId\` varchar(255) NOT NULL, \`completedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_b659ba23916a94faa43d2edeb0\` (\`templateId\`), INDEX \`IDX_a5e051d65c2593b75e3e20b503\` (\`templateReleaseId\`), INDEX \`IDX_cc38cd83f262ffc6b7bc687cf2\` (\`installedById\`), INDEX \`IDX_94f4bb3e6f6bc6f554322d5a1d\` (\`installStatus\`), INDEX \`IDX_9164ac29c20f26b509f46b4341\` (\`upgradedFromReleaseId\`), INDEX \`IDX_aa5ef665ba0cbec06b298291b5\` (\`workspaceId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_listing\` (\`id\` int NOT NULL AUTO_INCREMENT, \`templateId\` varchar(255) NOT NULL, \`name\` varchar(255) NULL, \`shortDescription\` varchar(300) NULL, \`fullDescription\` text NULL, \`images\` json NULL, \`versionNumber\` int UNSIGNED NOT NULL DEFAULT '0', \`createdById\` varchar(255) NOT NULL, \`categoryId\` varchar(255) NULL, \`tagIds\` json NULL, \`isFeaturedInMarketplace\` tinyint NOT NULL DEFAULT 0, \`isPaid\` tinyint NOT NULL DEFAULT 0, \`isMultiLicensePricing\` tinyint NOT NULL DEFAULT 0, \`singleLicensePrice\` int UNSIGNED NOT NULL DEFAULT '0', \`multiLicensePrice\` int UNSIGNED NOT NULL DEFAULT '0', \`tagsText\` varchar(255) NULL, \`featuredAt\` timestamp NULL, \`replacedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_fa941763739b7afb800a22f5e4\` (\`templateId\`), INDEX \`IDX_1602dedbb8b8d0c50cc073e638\` (\`name\`), INDEX \`IDX_be2613db126af02ef58f98be19\` (\`shortDescription\`), INDEX \`IDX_3cafeec8bb77155873ba713cea\` (\`createdById\`), INDEX \`IDX_43a26008ca21da89e0544cb497\` (\`categoryId\`), INDEX \`IDX_e3c592e018ade01ca39b3e55c8\` (\`isFeaturedInMarketplace\`), INDEX \`IDX_29d00b8634cd6dae2b5929d1b8\` (\`isPaid\`), INDEX \`IDX_733a720b3cab94438f1b4e755a\` (\`isMultiLicensePricing\`), UNIQUE INDEX \`IDX_c83e33952b9daff2b0d3cee7fb\` (\`templateId\`, \`versionNumber\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_release\` (\`id\` int NOT NULL AUTO_INCREMENT, \`templateId\` varchar(255) NOT NULL, \`versionNumber\` int UNSIGNED NOT NULL DEFAULT '0', \`createdById\` varchar(255) NOT NULL, \`dbProcessingOrder\` json NULL, \`dependencyMap\` json NULL, \`objectMappings\` json NULL, \`prepareData\` json NULL, \`isReady\` tinyint NOT NULL DEFAULT 0, \`isFeaturedInMarketplace\` tinyint NOT NULL DEFAULT 0, \`releaseNote\` text NULL, \`featuredAt\` timestamp NULL, \`replacedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_4a26c754e1b9678cf0e3e3f293\` (\`templateId\`), INDEX \`IDX_6ea86daec9589070e1adabd3f7\` (\`createdById\`), INDEX \`IDX_095c181cf69238daa69ae3fb2f\` (\`isReady\`), INDEX \`IDX_81ec0bea07488e4cc04ccd1999\` (\`isFeaturedInMarketplace\`), UNIQUE INDEX \`IDX_61bd21f9aa18ad4fb3ecbb8e58\` (\`templateId\`, \`versionNumber\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_review\` (\`id\` int NOT NULL AUTO_INCREMENT, \`templateId\` varchar(255) NOT NULL, \`templateReleaseId\` int UNSIGNED NOT NULL DEFAULT '0', \`templateListingId\` int UNSIGNED NOT NULL DEFAULT '0', \`userId\` varchar(255) NOT NULL, \`isPublishedAsCreator\` tinyint NOT NULL DEFAULT 0, \`parentId\` int UNSIGNED NULL, \`rating\` int UNSIGNED NULL, \`reviewText\` text NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_1bd25ba7398b0a3ad51f73f22a\` (\`templateId\`), INDEX \`IDX_bb34006ba9d483391e574015c9\` (\`templateReleaseId\`), INDEX \`IDX_66f9f11c7925d29e0fd8b8b846\` (\`templateListingId\`), INDEX \`IDX_aa957ad3a940cb42dfea183eae\` (\`userId\`), INDEX \`IDX_220715b85ac333fad32370ac5b\` (\`isPublishedAsCreator\`), INDEX \`IDX_645bfb0af8d78b248b3c40d66e\` (\`parentId\`), INDEX \`IDX_1edf8a7facae1e13a634184042\` (\`rating\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_submission\` (\`id\` int NOT NULL AUTO_INCREMENT, \`creatorId\` varchar(255) NOT NULL, \`templateId\` varchar(255) NOT NULL, \`releaseId\` int UNSIGNED NOT NULL DEFAULT '0', \`listingId\` int UNSIGNED NOT NULL DEFAULT '0', \`createdById\` varchar(255) NOT NULL, \`reviewedBy\` varchar(255) NULL, \`reviewResult\` varchar(255) NULL, \`reviewNote\` text NULL, \`reviewInternalNote\` text NULL, \`reviewedAt\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_0322110517f4c029ce916fcd32\` (\`creatorId\`), INDEX \`IDX_eae86d09660c6c84352220febe\` (\`templateId\`), INDEX \`IDX_6cbeaac771739ba26f0f547258\` (\`releaseId\`), INDEX \`IDX_07f4611bfff237da2e9b544d29\` (\`listingId\`), INDEX \`IDX_8dd656eb1d7db4abe93d18d661\` (\`createdById\`), INDEX \`IDX_999edd10d33dc169435fe43930\` (\`reviewedBy\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`template_top_pick\` (\`id\` int NOT NULL AUTO_INCREMENT, \`templateId\` varchar(255) NOT NULL, \`addedById\` varchar(255) NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_64dda2bf8c680f9d6047a48619\` (\`templateId\`), INDEX \`IDX_8f2fe15dcfa93af9dc059e9de3\` (\`addedById\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`database\` ADD \`templateReleaseId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`database\` ADD \`templateInstallId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`document\` ADD \`templateReleaseId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`page\` ADD \`templateReleaseId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`page\` ADD \`templateInstallId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`record\` ADD \`templateReleaseId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`record\` ADD \`templateInstallId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`view\` ADD \`templateReleaseId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`view\` ADD \`templateInstallId\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_upload\` ADD \`creatorId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`usersQuota\` \`usersQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))) STORED NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.users') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.users') AS SIGNED))","GENERATED_COLUMN","usersQuota","opendb-app-stage","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`collaboratorsQuota\` \`collaboratorsQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))) STORED NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.collaborators') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.collaborators') AS SIGNED))","GENERATED_COLUMN","collaboratorsQuota","opendb-app-stage","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`recordsQuota\` \`recordsQuota\` int AS ((CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))) STORED NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["(CAST(JSON_UNQUOTE(cyclePlanQuota->'$.records') AS SIGNED) + CAST(JSON_UNQUOTE(addOnsQuota->'$.records') AS SIGNED))","GENERATED_COLUMN","recordsQuota","opendb-app-stage","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`workspace_upload\` CHANGE \`workspaceId\` \`workspaceId\` varchar(255) NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_719e1d18453fa3b94da1e3a274\` ON \`database\` (\`templateReleaseId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c7fe29e25a1744d3cc56fb6aac\` ON \`database\` (\`templateInstallId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_96fcabb5763dc4c19bebc360c4\` ON \`document\` (\`templateReleaseId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_3ea1f032e55513dc1c3dc434f6\` ON \`page\` (\`templateReleaseId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_996c4781eb7204131f99bb6fb2\` ON \`page\` (\`templateInstallId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_187629986fc5b8f6365edd69fc\` ON \`record\` (\`templateReleaseId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c5530971348f6955465b52b7ed\` ON \`record\` (\`templateInstallId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_ef935c685420bc7816bfde3966\` ON \`view\` (\`templateReleaseId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_242e79d12b4729365deba789fc\` ON \`view\` (\`templateInstallId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_e02ee6db00839818a0adef39a1\` ON \`workspace_upload\` (\`creatorId\`)`);

        await queryRunner.query(`
            
INSERT INTO \`category\` (\`id\`, \`name\`, \`description\`, \`slug\`, \`type\`, \`coverImage\`, \`createdAt\`, \`updatedAt\`) VALUES
(25, 'Project Management', NULL, 'project-management', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:03.351867'),
(26, 'Sales and CRM', NULL, 'sales-crm', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:08.277176'),
(27, 'Marketing', NULL, 'marketing', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-02 05:14:48.838093'),
(28, 'Finance and Accounting', NULL, 'finance-accounting', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:16.826563'),
(29, 'HR and Recruiting', NULL, 'hr-recruiting', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:21.108348'),
(30, 'Operations', NULL, 'operations', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-02 05:14:48.838093'),
(31, 'Content Creation and Management', NULL, 'content-creation', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:03:13.075608'),
(32, 'Customer Support', NULL, 'customer-support', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:39.975619'),
(33, 'Product Development', NULL, 'product-development', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:44.672370'),
(34, 'Data Analysis', NULL, 'data-analysis', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:02:48.711965'),
(35, 'Event Management And Planning', NULL, 'event-planning', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:03:22.661418'),
(36, 'Personal Productivity', NULL, 'personal-productivity', 'all', NULL, '2024-12-02 05:14:48.838093', '2024-12-07 03:03:00.172784');

        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_e02ee6db00839818a0adef39a1\` ON \`workspace_upload\``);
        await queryRunner.query(`DROP INDEX \`IDX_242e79d12b4729365deba789fc\` ON \`view\``);
        await queryRunner.query(`DROP INDEX \`IDX_ef935c685420bc7816bfde3966\` ON \`view\``);
        await queryRunner.query(`DROP INDEX \`IDX_c5530971348f6955465b52b7ed\` ON \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_187629986fc5b8f6365edd69fc\` ON \`record\``);
        await queryRunner.query(`DROP INDEX \`IDX_996c4781eb7204131f99bb6fb2\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_3ea1f032e55513dc1c3dc434f6\` ON \`page\``);
        await queryRunner.query(`DROP INDEX \`IDX_96fcabb5763dc4c19bebc360c4\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_c7fe29e25a1744d3cc56fb6aac\` ON \`database\``);
        await queryRunner.query(`DROP INDEX \`IDX_719e1d18453fa3b94da1e3a274\` ON \`database\``);
        await queryRunner.query(`ALTER TABLE \`workspace_upload\` CHANGE \`workspaceId\` \`workspaceId\` varchar(255) NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["","GENERATED_COLUMN","recordsQuota","opendb-app-stage","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`recordsQuota\` \`recordsQuota\` int NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["","GENERATED_COLUMN","collaboratorsQuota","opendb-app-stage","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`collaboratorsQuota\` \`collaboratorsQuota\` int NOT NULL`);
        await queryRunner.query(`UPDATE \`typeorm_metadata\` SET \`value\` = ? WHERE \`type\` = ? AND \`name\` = ? AND \`schema\` = ? AND \`table\` = ?`, ["","GENERATED_COLUMN","usersQuota","opendb-app-stage","billing_cycle"]);
        await queryRunner.query(`ALTER TABLE \`billing_cycle\` CHANGE \`usersQuota\` \`usersQuota\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_upload\` DROP COLUMN \`creatorId\``);
        await queryRunner.query(`ALTER TABLE \`view\` DROP COLUMN \`templateInstallId\``);
        await queryRunner.query(`ALTER TABLE \`view\` DROP COLUMN \`templateReleaseId\``);
        await queryRunner.query(`ALTER TABLE \`record\` DROP COLUMN \`templateInstallId\``);
        await queryRunner.query(`ALTER TABLE \`record\` DROP COLUMN \`templateReleaseId\``);
        await queryRunner.query(`ALTER TABLE \`page\` DROP COLUMN \`templateInstallId\``);
        await queryRunner.query(`ALTER TABLE \`page\` DROP COLUMN \`templateReleaseId\``);
        await queryRunner.query(`ALTER TABLE \`document\` DROP COLUMN \`templateReleaseId\``);
        await queryRunner.query(`ALTER TABLE \`database\` DROP COLUMN \`templateInstallId\``);
        await queryRunner.query(`ALTER TABLE \`database\` DROP COLUMN \`templateReleaseId\``);
        await queryRunner.query(`DROP INDEX \`IDX_8f2fe15dcfa93af9dc059e9de3\` ON \`template_top_pick\``);
        await queryRunner.query(`DROP INDEX \`IDX_64dda2bf8c680f9d6047a48619\` ON \`template_top_pick\``);
        await queryRunner.query(`DROP TABLE \`template_top_pick\``);
        await queryRunner.query(`DROP INDEX \`IDX_999edd10d33dc169435fe43930\` ON \`template_submission\``);
        await queryRunner.query(`DROP INDEX \`IDX_8dd656eb1d7db4abe93d18d661\` ON \`template_submission\``);
        await queryRunner.query(`DROP INDEX \`IDX_07f4611bfff237da2e9b544d29\` ON \`template_submission\``);
        await queryRunner.query(`DROP INDEX \`IDX_6cbeaac771739ba26f0f547258\` ON \`template_submission\``);
        await queryRunner.query(`DROP INDEX \`IDX_eae86d09660c6c84352220febe\` ON \`template_submission\``);
        await queryRunner.query(`DROP INDEX \`IDX_0322110517f4c029ce916fcd32\` ON \`template_submission\``);
        await queryRunner.query(`DROP TABLE \`template_submission\``);
        await queryRunner.query(`DROP INDEX \`IDX_1edf8a7facae1e13a634184042\` ON \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_645bfb0af8d78b248b3c40d66e\` ON \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_220715b85ac333fad32370ac5b\` ON \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_aa957ad3a940cb42dfea183eae\` ON \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_66f9f11c7925d29e0fd8b8b846\` ON \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_bb34006ba9d483391e574015c9\` ON \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_1bd25ba7398b0a3ad51f73f22a\` ON \`template_review\``);
        await queryRunner.query(`DROP TABLE \`template_review\``);
        await queryRunner.query(`DROP INDEX \`IDX_61bd21f9aa18ad4fb3ecbb8e58\` ON \`template_release\``);
        await queryRunner.query(`DROP INDEX \`IDX_81ec0bea07488e4cc04ccd1999\` ON \`template_release\``);
        await queryRunner.query(`DROP INDEX \`IDX_095c181cf69238daa69ae3fb2f\` ON \`template_release\``);
        await queryRunner.query(`DROP INDEX \`IDX_6ea86daec9589070e1adabd3f7\` ON \`template_release\``);
        await queryRunner.query(`DROP INDEX \`IDX_4a26c754e1b9678cf0e3e3f293\` ON \`template_release\``);
        await queryRunner.query(`DROP TABLE \`template_release\``);
        await queryRunner.query(`DROP INDEX \`IDX_c83e33952b9daff2b0d3cee7fb\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_733a720b3cab94438f1b4e755a\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_29d00b8634cd6dae2b5929d1b8\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_e3c592e018ade01ca39b3e55c8\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_43a26008ca21da89e0544cb497\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_3cafeec8bb77155873ba713cea\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_be2613db126af02ef58f98be19\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_1602dedbb8b8d0c50cc073e638\` ON \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_fa941763739b7afb800a22f5e4\` ON \`template_listing\``);
        await queryRunner.query(`DROP TABLE \`template_listing\``);
        await queryRunner.query(`DROP INDEX \`IDX_aa5ef665ba0cbec06b298291b5\` ON \`template_install\``);
        await queryRunner.query(`DROP INDEX \`IDX_9164ac29c20f26b509f46b4341\` ON \`template_install\``);
        await queryRunner.query(`DROP INDEX \`IDX_94f4bb3e6f6bc6f554322d5a1d\` ON \`template_install\``);
        await queryRunner.query(`DROP INDEX \`IDX_cc38cd83f262ffc6b7bc687cf2\` ON \`template_install\``);
        await queryRunner.query(`DROP INDEX \`IDX_a5e051d65c2593b75e3e20b503\` ON \`template_install\``);
        await queryRunner.query(`DROP INDEX \`IDX_b659ba23916a94faa43d2edeb0\` ON \`template_install\``);
        await queryRunner.query(`DROP TABLE \`template_install\``);
        await queryRunner.query(`DROP INDEX \`IDX_fe6443288b146678da01f3a5d2\` ON \`template\``);
        await queryRunner.query(`DROP INDEX \`IDX_87c15c207faa0eda3eb7209e54\` ON \`template\``);
        await queryRunner.query(`DROP INDEX \`IDX_f431a22e366be52d742afeeafe\` ON \`template\``);
        await queryRunner.query(`DROP INDEX \`IDX_d20ce59851706e2f0161115af0\` ON \`template\``);
        await queryRunner.query(`DROP INDEX \`IDX_55980b6e1dfe1c22667845f3b9\` ON \`template\``);
        await queryRunner.query(`DROP INDEX \`IDX_b0dcd53261ead0ec614577e1ec\` ON \`template\``);
        await queryRunner.query(`DROP TABLE \`template\``);
        await queryRunner.query(`DROP INDEX \`IDX_3413aed3ecde54f832c4f44f04\` ON \`tag\``);
        await queryRunner.query(`DROP INDEX \`IDX_c974f5f41100d1f721776bf633\` ON \`tag\``);
        await queryRunner.query(`DROP TABLE \`tag\``);
        await queryRunner.query(`DROP INDEX \`IDX_7f16c661fb92cf3a85ebb6f13c\` ON \`ip_record\``);
        await queryRunner.query(`DROP INDEX \`IDX_f0dd2c1459c24ee7ccd1aaee33\` ON \`ip_record\``);
        await queryRunner.query(`DROP INDEX \`IDX_ad1117f818b9efea957e137c7c\` ON \`ip_record\``);
        await queryRunner.query(`DROP INDEX \`IDX_7236d9c9ec8fda8fb76963a14d\` ON \`ip_record\``);
        await queryRunner.query(`DROP TABLE \`ip_record\``);
        await queryRunner.query(`DROP INDEX \`IDX_15b198166952da6cf5154c82ff\` ON \`creator_payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_92de5823d883a06ba9383ff54e\` ON \`creator_payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_f6ada61cfd4bb7f1edc44fe7a8\` ON \`creator_payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_d57a4b8a4a6914802a2ea7c560\` ON \`creator_payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_fc57b171414d4cd2e783714de6\` ON \`creator_payout_method\``);
        await queryRunner.query(`DROP TABLE \`creator_payout_method\``);
        await queryRunner.query(`DROP INDEX \`IDX_42d0aa2f577325d822b1b2f05f\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_647ab0e7d9363af9be1e4759e9\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_28e95f456534221e6b1d9642b5\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_6d3fce6dc92dd3238d9d980f3a\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_05494dfb86f7fa0c9859761f30\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_a32a4cb845116991803f96a25e\` ON \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_43b05a3ea81284157096f906ff\` ON \`creator_payout\``);
        await queryRunner.query(`DROP TABLE \`creator_payout\``);
        await queryRunner.query(`DROP INDEX \`IDX_67d45978a1fdf30be9abb18335\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_a9327c8fe19d25aab86a44a76c\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_db2ae30ed73073c91faddf0b34\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_209d2c7dcb935d6e5e28b786e9\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_c5d6050f8bdde85b96300bd291\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_15d47805a81d9e65fcae285e4f\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_3f0926be43c973598b3b0cdf32\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_9a37ca9ba06b427c4dbcf014ca\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_34e83582a9047fb5f424cc4e86\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_254c86a3867c7a5a4926347b50\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_f24a94c847047148393de85327\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_072df857c81b570525305e98a1\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_f42e683a6b5b7b1e9e1450917f\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_c7b88df5c1fc24c689559eb20b\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_fda2282401ee51a264cc1f9d87\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_008ad534e63348dc3353132e3e\` ON \`template_purchase\``);
        await queryRunner.query(`DROP TABLE \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_0ca624a904a8230ce4ab72930d\` ON \`creator_member\``);
        await queryRunner.query(`DROP INDEX \`IDX_b112b9f957d8eb4453c8db092b\` ON \`creator_member\``);
        await queryRunner.query(`DROP INDEX \`IDX_5cddff2c8983cd4b57874a2a25\` ON \`creator_member\``);
        await queryRunner.query(`DROP TABLE \`creator_member\``);
        await queryRunner.query(`DROP INDEX \`IDX_d5b9164239598ab47adc670acf\` ON \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_518d013fa0147414561fdd4bd2\` ON \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_f95942cef40fbb722f03a6f45a\` ON \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_e229c0f476ae8011ff4ac9fc08\` ON \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_54783472c3455a47281d3419a9\` ON \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_51f45959f9182350fc0150fee2\` ON \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_87e0d3c711d81af02a228c9c38\` ON \`creator\``);
        await queryRunner.query(`DROP TABLE \`creator\``);
        await queryRunner.query(`DROP INDEX \`IDX_cb73208f151aa71cdd78f662d7\` ON \`category\``);
        await queryRunner.query(`DROP TABLE \`category\``);
        await queryRunner.query(`DROP INDEX \`IDX_97ff8121ada1561ca0edf5d910\` ON \`admin_member\``);
        await queryRunner.query(`DROP TABLE \`admin_member\``);
    }

}
