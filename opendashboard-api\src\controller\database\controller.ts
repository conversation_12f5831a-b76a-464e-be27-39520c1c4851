import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {ActivateMessaging, CreateDatabase, GetMyDatabases, MatchAndCreateRecords, UploadRecordImage} from "../../businessLogic/database";

export class DatabaseController {

    async getDatabases(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {databases} = await GetMyDatabases(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                databases
            },
        }
        return response.json(
            responseData
        )
    }

    async createDatabase(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {database} = await CreateDatabase(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                database
            },
        }
        return response.json(
            responseData
        )
    }

    async matchRecords(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {matchMap} = await MatchAndCreateRecords(authInfo.userId, request.params.id, request.params.databaseId, request.body, true)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {matchMap},
        }
        return response.json(
            responseData
        )
    }

    async activateMessaging(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await ActivateMessaging(authInfo.userId, request.params.id, request.params.databaseId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async uploadRecordCoverImage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request);
        const recordId = request.params.recordId;

        await UploadRecordImage(authInfo.userId, request.params.id, request.params.databaseId, recordId, request, 'cover');

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        };

        return response.json(responseData);
    }

    async uploadRecordProfileImage(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request);
        const recordId = request.params.recordId;

        await UploadRecordImage(authInfo.userId, request.params.id, request.params.databaseId, recordId, request, 'profile');


        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        };

        return response.json(responseData);
    }
}