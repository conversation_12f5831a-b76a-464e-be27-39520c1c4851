import {SecretType, WorkspaceSecret} from "../entity/workspaceSecret"
import { BaseService } from "./service"
import {ServerProcessingError, UniqueConstraintError} from "../errors/AppError";
import { Repository} from "typeorm";
import {getRepository} from "../connection/db";


export interface CreateWorkspaceSecret extends Pick<WorkspaceSecret, 'workspaceId' | 'createdById' | 'type' | 'name' | 'value'> { };


export class WorkspaceSecretService extends BaseService<WorkspaceSecret> {

    initRepository = (): Repository<WorkspaceSecret> => {
        return getRepository(WorkspaceSecret)
    }

    addWorkspaceSecret = async (data: CreateWorkspaceSecret): Promise<WorkspaceSecret> => {
        const repo = this.getRepository()
        const qB = repo.createQueryBuilder()
            .insert()
            .into(WorkspaceSecret)
            .values(data)

        try {
            await qB.execute()
            return data as WorkspaceSecret;
        } catch (err) {
            if (err.message.includes("Duplicate entry")) throw new UniqueConstraintError('name')
            throw new ServerProcessingError(err.message)
        }
    }

    getWorkspaceVariables = async (workspaceId: string): Promise<WorkspaceSecret[]> => {
        const repo = this.getRepository();

        const qB = repo.createQueryBuilder("wS")
            .select("wS")
            .addSelect('wS.value')
            .where({ workspaceId, type: SecretType.Variable })

        return await qB.getMany()
    }

    getWorkspaceSecrets = async (workspaceId: string): Promise<WorkspaceSecret[]> => {
        return this.find({ workspaceId, type: SecretType.Secret })
    }

    requestVariablesAndSecretsWithValue = async (workspaceId: string): Promise<WorkspaceSecret[]> => {
        const repo = this.getRepository();

        const qB = repo.createQueryBuilder("wS")
            .select("wS")
            .addSelect('wS.value')
            .where({ workspaceId })

        return await qB.getMany()
    }



}
