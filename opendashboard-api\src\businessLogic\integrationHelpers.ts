import {KeyValueStore} from "../entity/WorkspaceMemberSettings";
import {ConnectionCredentials, WorkspaceIntegrationConnection} from "../entity/workspaceIntegrationConnection";
import {WorkspaceIntegrationConnectionService} from "../service/workspaceIntegrationConnection";
import {BadRequestError, NotfoundError} from "../errors/AppError";
import {apiUrl} from "../config";
import {IntegrationDefinition, IntegrationFile, IntegrationFilesAPI, IntegrationParams, IntegrationStoreAPI, IntegrationStoreScope} from "@opendashboard-inc/integration-core";
import {UploadBuffer} from "./upload";
import {SystemParams} from "./database";
import {WorkspaceIntegrationStoreService} from "../service/workspaceIntegrationStore";
import {WorkspaceIntegrationStore} from "../entity/workspaceIntegrationStore";
import {ConnectionAPI, getIntegrationVar, IntegrationCredentials, OAuth2Configs} from '@opendashboard-inc/integration-core-api';
import {getCoreApi} from "./integration";
import {strReplaceAll} from "opendb-app-db-utils/lib";
import {handleError} from "../config/error";

const fs = require('fs');
const path = require('path');

// const {createCoreApi, loadIntegration} = integrationCore.default;

const integrationsPath = process.env['INTEGRATIONS_PATH'] || '';
const enabledIntegrations = (process.env['ENABLED_INTEGRATIONS'] || '').split(',').map(i => i.trim()).filter(i => i.length > 0);


interface ConnectionParams {
    integration: string
    workspaceId: string
    name: string
    credentials: ConnectionCredentials
    meta?: KeyValueStore
    id?: string
}

export const connectionToIntegrationCredentials = (connection: WorkspaceIntegrationConnection): IntegrationCredentials => {
    return {
        id: connection.id,
        name: connection.name,
        integration: connection.integration,
        credentials: connection.credentials,
    }
}

export const saveConnection = async (params: ConnectionParams): Promise<WorkspaceIntegrationConnection> => {
    const s = new WorkspaceIntegrationConnectionService()

    if (params.id) {
        const existing = await s.findOne({id: params.id, integration: params.integration})
        if (!existing || existing.workspaceId !== params.workspaceId) {
            throw new NotfoundError(`Connection with id ${params.id} not found`)
        }
        const update: Partial<WorkspaceIntegrationConnection> = {
            credentials: params.credentials,
            name: params.name,
        }
        await s.update({id: params.id}, update)

        const connection = {...existing, ...update}
        delete connection.credentials
        return connection
    }
    const connection = await s.insert({
        integration: params.integration,
        workspaceId: params.workspaceId,
        name: params.name,
        credentials: params.credentials,
    })
    delete connection.credentials
    return connection
}

export const getConnection = async (params: { integration: string, id: string, workspaceId: string }, withCredentials = false): Promise<WorkspaceIntegrationConnection | null> => {
    const s = new WorkspaceIntegrationConnectionService()
    const qB = s.getRepository().createQueryBuilder('c')
        .select("c")
        .where(params)
    if (withCredentials) {
        qB.addSelect("c.credentials")
    }
    return qB.getOne()
}

export const getConnections = async (params: { integration: string, workspaceId: string }): Promise<WorkspaceIntegrationConnection[]> => {
    const s = new WorkspaceIntegrationConnectionService()
    return await s.find(params)
}

export const saveInStore = async (params: StoreAPIOperationParams) => {
    const s = new WorkspaceIntegrationStoreService()

    // insert on duplicate key update
    const qB = s.getRepository().createQueryBuilder()
        .insert()
        .into(WorkspaceIntegrationStore)
        .values({
            integration: params.integration,
            workspaceId: params.workspaceId,
            key: params.key,
            scope: params.scope,
            value: JSON.stringify(params.value),
        })

    let [query, parameters] = qB.getQueryAndParameters()

    const updateQuery: string[] = []
    updateQuery.push(`value = VALUES(value)`)

    const orUpdateQuery = updateQuery.join(", ")
    query += ` ON DUPLICATE KEY UPDATE ${orUpdateQuery}`
    await s.getRepository().query(query, parameters)
}

interface StoreAPIOperationParams {
    integration: string
    workspaceId: string | null
    workflowId: string | null
    key: string
    scope: IntegrationStoreScope
    value: string
}

export const getInStore = async (params: StoreAPIOperationParams) => {
    const s = new WorkspaceIntegrationStoreService()
    const rawItem = await s.findOne(params)
    if (!rawItem) return null
    const item = {...rawItem}
    item.value = JSON.parse(item.value)
    return item
}

export const deleteInStore = async (params: StoreAPIOperationParams) => {
    const s = new WorkspaceIntegrationStoreService()
    return s.hardRemove(params)
}

export const deleteConnection = async (params: { integration: string, id: string, workspaceId: string }): Promise<void> => {
    const s = new WorkspaceIntegrationConnectionService()
    await s.remove(params)
}

const maxFileSize = 1024 * 1024 * 100; // 100Mb

export const getFilesAPI = (workspaceId: string) => {
    const filesAPI: IntegrationFilesAPI = {
        async write({fileName, data}: { fileName: string, data: Buffer }): Promise<IntegrationFile> {

            if (data.length > maxFileSize) {
                throw new BadRequestError(`File size exceeds the limit of ${maxFileSize / 1024 / 1024} MB`);
            }
            const upload = await UploadBuffer(SystemParams.UserId, workspaceId, data, {
                name: fileName,
                type: 'application/octet-stream',
                size: data.length,
            })
            return {
                name: fileName,
                url: upload.upload.finalUrl,
            };
        }
    };
    return filesAPI
}

export const getConnectionAPI = (workspaceId: string) => {
    const connectionAPI: ConnectionAPI = {
        OAuth2Configs: async (d) => getOAuth2Configs(d),
        delete: (integration: string, id: string): Promise<void> => deleteConnection({integration, id, workspaceId}),
        get: async (integration: string, id: string): Promise<IntegrationCredentials | null> => {
            const connection = await getConnection({integration, id, workspaceId}, true)

            // console.log(`Connection requested for`, {workspaceId, integration, id}, {connection})

            return connection
        },
        save: async (data: IntegrationCredentials): Promise<void> => {
            const connection = await saveConnection({...data, workspaceId})
        }
    }
    return connectionAPI
}

export const getWebhookURL = (workflowId: string, integration: string, trigger: string, isTest = false) => {
    // /:id/integrations/:integration/:trigger/dispatch
    const url = apiUrl(`/v0/webhooks/${workflowId}/integrations/${integration}/${trigger}/dispatch`)
    if (isTest) {
        return `${url}/test`
    }
    return url
}

// const buildRunStepParams = async (partial: {
//     propsValue: any;
//     payload?: any;
//     integration: IntegrationParams;
//     type: 'action' | 'trigger';
//     name: string;
//     mode?: 'run' | 'test';
//     connectionId?: string;
// }): Promise<RunStepParams<any>> => {
//     const integration = await core.getIntegration(partial.integration);
//     return {
//         ...partial,
//         store: storeAPI(integration.displayName),
//         files: filesAPI,
//         log: logsAPI,
//         webhookUrl: partial.type === 'trigger' ? getWebhookUrl(integration, partial.name, partial.connectionId) : undefined,
//         connection: connectionAPI,
//     }
// }


// export const buildRunParams = (workspaceId:string, workflowId:string, integration: string,  partial: { integration: string }) => {
//     const params: RunStepParams<void> = {
//         connection: getConnectionAPI(workflow.workspaceId),
//         files: getFilesAPI(workflow.workspaceId),
//         integration: {name: integration},
//         log: (...args: any[]) => consoleLog(`Logs from integration on activated (${integration}${trigger}) `, ...args),
//         propsValue: propsValue,
//         store: getStoreAPI(workflow.workspaceId, integration),
//         type: 'trigger',
//         connectionId,
//         name: trigger,
//         webhookUrl,
//     }
//
//     return params
// }

export const getStoreAPI = (workspaceId: string, integration: string, workflowId: string | null) => {
    const storeAPI: IntegrationStoreAPI = {
        get: async (scope: IntegrationStoreScope, key: string): Promise<any> => {
            const params: StoreAPIOperationParams = {value: null, workflowId: workflowId || null, integration, key, scope, workspaceId}
            if (scope === 'global') {
                params.workspaceId = null
                params.workflowId = null
            }
            if (scope === 'workspace') {
                params.workflowId = null
            }
            const data = await getInStore(params)
            return data?.value || null
        },
        set: async (scope: IntegrationStoreScope, key: string, value: any): Promise<void> => {
            const params: StoreAPIOperationParams = {value: value, workflowId: workflowId || null, integration, key, scope, workspaceId}
            if (scope === 'global') {
                params.workspaceId = null
                params.workflowId = null
            }
            if (scope === 'workspace') {
                params.workflowId = null
            }
            return await saveInStore(params);
        },
        delete: async (scope: IntegrationStoreScope, key?: string): Promise<void> => {
            const params: StoreAPIOperationParams = {value: null, workflowId: workflowId || null, integration, key, scope, workspaceId}
            if (scope === 'global') {
                params.workspaceId = null
                params.workflowId = null
            }
            if (scope === 'workspace') {
                params.workflowId = null
            }
            await deleteInStore(params)
        }
    }
    return storeAPI
}

const moduleCache: { [key: string]: { path: string, loadedAt: number } } = {};
const RELOAD_INTERVAL = 10 * 60 * 1000; // 10 minutes

export const getIntegrationEntryPath = async (params: IntegrationParams): Promise<string> => {
    const name = strReplaceAll('@opendashboard-inc/integration-', params.name || '', '');

    const fullPath = path.resolve(path.join(integrationsPath, name, 'index.js'));

    // const integrationPath = process.env['INTEGRATIONS_PATH'] + `/${name}/index.js`;
    // const currentTime = Date.now();
    //
    // if (moduleCache[integrationPath] && (currentTime - moduleCache[integrationPath].loadedAt < RELOAD_INTERVAL)) {
    //     return moduleCache[integrationPath].path;
    // }
    //
    // delete require.cache[require.resolve(integrationPath)];
    // moduleCache[integrationPath] = {path: integrationPath, loadedAt: currentTime};
    //
    // console.log("Integration Paths: ", {integrationPath});
    //
    // return integrationPath;
    return fullPath
}

export const getOAuth2Configs = async (integration: string, {clientId, clientSecret}: { clientId?: string, clientSecret?: string } = {}): Promise<OAuth2Configs> => {
    const credentials = {
        clientId: clientId ? clientId : getIntegrationVar(integration, 'CLIENT_ID'),
        clientSecret: clientSecret ? clientSecret : getIntegrationVar(integration, 'CLIENT_SECRET'),
    };
    let redirectUri = apiUrl(`/v0/oauth2/callback`)
    return {
        credentials,
        redirectUri
    }
}

const getEnabledIntegrations = async () => {
    // // Force Node to add your root node_modules to the lookup paths
    // const appNodeModules = path.resolve(rootPath(), 'node_modules');
    // const Module = require('module');
    // const globalPaths = Module.globalPaths;
    //
    // if (!globalPaths.includes(appNodeModules)) {
    //     globalPaths.push(appNodeModules);
    // }


    if (!fs.existsSync(integrationsPath)) {
        return [];
    }
    const core = getCoreApi()
    const integrations: IntegrationDefinition[] = []

    const entries = fs.readdirSync(integrationsPath, {withFileTypes: true});

    for (const entry of entries) {
        if (entry.isDirectory() && enabledIntegrations.includes(entry.name)) {
            const fullPath = path.resolve(path.join(integrationsPath, entry.name, 'index.js'));

            // console.log(`Loading integration from ${fullPath}`);
            try {
                const integration = await core.getIntegration({name: entry.name});
                // const module = await import(fullPath);
                // const integration = module?.default;

                // const module = require(fullPath);
                // const integration = module?.default;

                // const integration = await loadIntegration(fullPath)
                integrations.push(integration)
            } catch (e) {
                handleError(e)
            }
        }
    }
    return integrations
}

const integrationCache = {
    data: null as IntegrationDefinition[] | null,
    timestamp: 0
};
const CACHE_DURATION = RELOAD_INTERVAL

export const getIntegrations = async () => {
    const currentTime = Date.now();
    if (integrationCache.data && (currentTime - integrationCache.timestamp < CACHE_DURATION)) {
        return integrationCache.data;
    }

    const integrations = await getEnabledIntegrations();
    integrationCache.data = integrations;
    integrationCache.timestamp = currentTime;

    return integrations;
}

export const getIntegration = async (name: string) => {
    const integrations = await getIntegrations()
    return integrations.find(i => i.name === name)
}






