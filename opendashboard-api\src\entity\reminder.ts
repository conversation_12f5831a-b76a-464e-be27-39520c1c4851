import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {KeyValueStore} from "./WorkspaceMemberSettings";

@Entity()
export class Reminder {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    title: string

    @Column({type: 'text', nullable: true})
    description: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    databaseId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    recordId: string

    @Index()
    @Column({type: 'bool', default: false})
    isResolved: boolean

    @Index()
    @Column({type: 'varchar', nullable: true})
    resolvedById: string

    @Column({type: 'timestamp', nullable: true})
    resolvedAt: Date

    @Column({type: 'timestamp', nullable: true})
    nextNotifyAt: Date

    @Column({type: 'json', nullable: true})
    notifyDates: Date[]

    // @Column({type: 'json', nullable: true})
    // notifyTimes: (string|NotifyTime)[]

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    assignedToUserIds: string

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    taggedRecordIds: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    updatedById: string

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}
