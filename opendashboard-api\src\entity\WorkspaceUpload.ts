import {
    Column,
    CreateDateColumn, DeleteDateColumn,
    Entity, Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"

@Entity()
export class WorkspaceUpload {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    creatorId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Column({type: "varchar", nullable: true})
    mimeType: string

    @Index()
    @Column({type: "varchar", nullable: true})
    type: string

    @Column({type: "varchar", nullable: true})
    finalUrl: string

    @Column({type: "varchar", nullable: true})
    doSpaceKey: string

    @Column({type: "varchar", nullable: true})
    thumbnailUrl: string

    @Column({type: "bigint", default: 0})
    size: number

    @Column({type: "int", default: 0})
    width: number

    @Column({type: "int", default: 0})
    height: number

    @Index()
    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date

    @Index()
    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true})
    meta: object


}
