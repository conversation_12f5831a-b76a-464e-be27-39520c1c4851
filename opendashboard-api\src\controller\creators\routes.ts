import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {CreatorsController} from "./controller";

export const creatorsRoutes: Routes = {
    basePath: '/creators',
    middleware: [verifyJWT],
    routes: {
        "/": {
            post: {controller: CreatorsController, action: "create"},
            get: {controller: CreatorsController, action: "getAll"},
        },
        '/:id': {
            get: {controller: CreatorsController, action: "getCreator"},
            patch: {controller: CreatorsController, action: "update"},
        },
        '/:id/stats': {
            get: {controller: CreatorsController, action: "getCreatorStats"},
        },
        '/:id/cover-photo': {
            patch: {controller: CreatorsController, action: "updateCover"},
        },
        '/:id/profile-photo': {
            patch: {controller: CreatorsController, action: "updatePhoto"},
        },
        '/:id/members': {
            get: {controller: C<PERSON>sController, action: "getMembers"},
            post: {controller: C<PERSON><PERSON><PERSON><PERSON>roller, action: "addMembers"},
            patch: {controller: Creator<PERSON>Controller, action: "updateMember"},
            delete: {controller: CreatorsController, action: "removeMember"},
        },
        '/:id/settings/onboarding-link': {
            get: {controller: CreatorsController, action: "getOnboardingLink"},
        },
        '/:id/settings/dashboard-link': {
            get: {controller: CreatorsController, action: "getDashboardLink"},
        },
        '/:id/purchases': {
            get: {controller: CreatorsController, action: "getPurchases"},
            post: {controller: CreatorsController, action: "addPurchase"},
        },
        '/:id/discounts': {
            get: {controller: CreatorsController, action: "getDiscounts"},
            post: {controller: CreatorsController, action: "addDiscount"},
            patch: {controller: CreatorsController, action: "updateDiscount"},
            delete: {controller: CreatorsController, action: "deleteDiscount"},
        },
        '/:id/payouts': {
            get: {controller: CreatorsController, action: "getPayouts"},
        },
        '/:id/templates': {
            get: {controller: CreatorsController, action: "getTemplates"},
            post: {controller: CreatorsController, action: "createTemplate"},
        },
        '/:id/templates/:templateId': {
            get: {controller: CreatorsController, action: "getTemplate"},
        },
        '/:id/templates/:templateId/stats': {
            get: {controller: CreatorsController, action: "getTemplateStats"},
        },
        '/:id/shared-resources': {
            get: {controller: CreatorsController, action: "getSharedResources"},
        },
        '/:id/templates/:templateId/releases': {
            get: {controller: CreatorsController, action: "getReleases"},
            post: {controller: CreatorsController, action: "prepareNewRelease"},
            delete: {controller: CreatorsController, action: "discardRelease"},
            patch: {controller: CreatorsController, action: "updateRelease"},
        },
        '/:id/templates/:templateId/releases/:releaseId': {
            get: {controller: CreatorsController, action: "getRelease"},
        },
        '/:id/templates/:templateId/releases/build-dependencies': {
            post: {controller: CreatorsController, action: "buildNewReleaseDependencies"},
        },
        '/:id/templates/:templateId/listings': {
            get: {controller: CreatorsController, action: "getListings"},
            patch: {controller: CreatorsController, action: "updateListing"},
            delete: {controller: CreatorsController, action: "deleteListing"},
        },
        '/:id/templates/:templateId/submission': {
            post: {controller: CreatorsController, action: "submitForReview"},
            delete: {controller: CreatorsController, action: "cancelSubmission"}
        },
        '/:id/uploads': {
            post: {controller: CreatorsController, action: "uploadNewFile"}
        },


    }
}
