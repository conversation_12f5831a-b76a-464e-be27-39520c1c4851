import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

@Entity()
export class Document {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Column({type: 'json', nullable: true})
    contentJSON: object

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    contentText: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    viewId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    databaseId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    recordId: string

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    assignedToUserIds: string

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    taggedRecordIds: string

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateReleaseId: number

    @Index()
    @Column({type: 'varchar', nullable: true})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    updatedById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}
