import {Database} from "../../entity/Database";
import {ProcessedDbRecord} from "opendb-app-db-utils/lib/typings/db";
import {WorkflowService} from "../../service/workflow";
import {OpendashboardRecordInWorkflow, OpendashboardRecordTriggerWorkflowNode_Configs, WorkflowStatus, WorkflowTriggerType} from "opendb-app-db-utils/lib/typings/workflow";
import {getTriggerObjectId} from "../workflow";
import {FilterableRecord, filterRecords, LinkedDatabases, transformRawRecords} from "opendb-app-db-utils/lib/utils/db";
import {CountWorkflows, StartWorkflowRuns, WorkflowRunData} from "../runStarter/runStarter";
import {AdjacentDatabases, getRecordsNoAuth} from "../database";
import {getWorkspaceMemberNoCacheNoAuth} from "../workspace";
import {membersToPersons} from "../../utility/workspace";
import {recordToWorkflowJSON} from "opendb-app-db-utils/lib/utils/workflow";
import {Person} from "opendb-app-db-utils/lib/typings/common";

export const ExecuteOnDatabaseActivityWorkflowHook = async (activity: 'created' | 'updated' | 'deleted', workspaceId: string, database: Database, ids: string[]) => {
    let triggerType: WorkflowTriggerType
    if (activity === 'created') triggerType = WorkflowTriggerType.Opendashboard_OnRecordCreated
    else if (activity === 'updated') triggerType = WorkflowTriggerType.Opendashboard_OnRecordUpdated
    else if (activity === 'deleted') triggerType = WorkflowTriggerType.Opendashboard_OnRecordDeleted
    else return

    const databaseId = database.id
    const triggerObjectId = getTriggerObjectId("database", databaseId)

    const hooks = await CountWorkflows(workspaceId, triggerType, triggerObjectId)

    if (hooks === 0) return

    const recordIds = ids

    const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId);
    const result = await getRecordsNoAuth(database, recordIds)

    const persons = membersToPersons(members)

    const linkedDatabases: LinkedDatabases = adjacentDatabasesToLinkedDatabases(result.adjacentDatabases)
    // for (let [id, value] of Object.entries(result.adjacentDatabases)) {
    //     if (!value.database || !value.recordsMap) continue
    //     linkedDatabases[id] = {
    //         definition: value.database.definition, id, recordsMap: {}, srcPackageName: value.database.srcPackageName
    //     }
    //     for (let r of Object.values(value.recordsMap)) {
    //         linkedDatabases[id].recordsMap[r.record.id] = r.record
    //     }
    // }
    const transformResult = transformRawRecords(database.definition, result.records, persons, linkedDatabases)

    await OnRecordActivityWorkflowHooks(activity, workspaceId, database, transformResult, persons)
}

export const adjacentDatabasesToLinkedDatabases = (adjacentDatabases: AdjacentDatabases) => {
    const linkedDatabases: LinkedDatabases = {}
    for (let [id, value] of Object.entries(adjacentDatabases)) {
        if (!value.database || !value.recordsMap) continue
        linkedDatabases[id] = {
            definition: value.database.definition, id, recordsMap: {}, srcPackageName: value.database.srcPackageName
        }
        for (let r of Object.values(value.recordsMap)) {
            linkedDatabases[id].recordsMap[r.record.id] = r.record
        }
    }
    return linkedDatabases
}

export const OnRecordActivityWorkflowHooks = async (activity: 'created' | 'updated' | 'deleted', workspaceId: string, database: Database, records: ProcessedDbRecord[], persons: Person[]) => {
    const s = new WorkflowService()

    let triggerType: WorkflowTriggerType
    if (activity === 'created') triggerType = WorkflowTriggerType.Opendashboard_OnRecordCreated
    else if (activity === 'updated') triggerType = WorkflowTriggerType.Opendashboard_OnRecordUpdated
    else if (activity === 'deleted') triggerType = WorkflowTriggerType.Opendashboard_OnRecordDeleted
    else return

    const databaseId = database.id

    const triggerObjectId = getTriggerObjectId("database", databaseId)

    const workflows = await s.find({
        workspaceId,
        triggerType,
        triggerObjectId,
        status: WorkflowStatus.Published
    })
    const runDatas: WorkflowRunData[] = []

    const {recordsInWorkflow} = recordToWorkflowJSON(database, records, persons)
    const recordInWorkflowMap: { [key: string]: OpendashboardRecordInWorkflow } = {}
    for (let opendashboardRecordInWorkflow of recordsInWorkflow) {
        recordInWorkflowMap[opendashboardRecordInWorkflow.id] = opendashboardRecordInWorkflow
    }

    for (let workflow of workflows) {
        const definition = workflow.definition
        const triggerNode = definition.map[definition.triggerId]

        const config = triggerNode.data.configs as OpendashboardRecordTriggerWorkflowNode_Configs

        let filteredRecords = [...records]

        if (config.filter) {
            const filterableRecords: FilterableRecord[] = []
            for (let record of records) {
                const r: FilterableRecord = {
                    createdAt: record.createdAt,
                    createdById: record.createdBy.id,
                    databaseId: databaseId,
                    id: record.id,
                    recordValues: record.recordValues,
                    uniqueValue: record.uniqueValue,
                    updatedAt: record.updatedAt,
                    updatedById: record.updatedBy.id
                }
                filterableRecords.push(r)
            }
            const filterResult = filterRecords(filterableRecords, records, config.filter, database.definition)
            filteredRecords = filterResult.processedRecords
        }
        for (const r of filteredRecords) {
            const recordInWorkflow = recordInWorkflowMap[r.id]
            runDatas.push({
                workflow,
                // workflowId: workflow.id,
                triggerOutput: {record: recordInWorkflow},
                triggerNodeId: definition.triggerId,
                databaseId: databaseId,
                recordId: r.id,
            })
        }
    }
    if (records.length === 0) return
    await StartWorkflowRuns(runDatas)
}
