import { MigrationInterface, QueryRunner } from "typeorm";

export class CampaignTrackingMigration1745486344895 implements MigrationInterface {
    name = 'CampaignTrackingMigration1745486344895'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_bb6e8f56ccb50624a2a6fce50e\` ON \`campaign_analytic\``);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` CHANGE \`campaignId\` \`campaignId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` CHANGE \`workflowId\` \`workflowId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` CHANGE \`emailId\` \`emailId\` int NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_bb6e8f56ccb50624a2a6fce50e\` ON \`campaign_analytic\` (\`campaignId\`, \`workflowId\`, \`emailId\`, \`linkId\`, \`eventAt\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_bb6e8f56ccb50624a2a6fce50e\` ON \`campaign_analytic\``);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` CHANGE \`emailId\` \`emailId\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` CHANGE \`workflowId\` \`workflowId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` CHANGE \`campaignId\` \`campaignId\` varchar(255) NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_bb6e8f56ccb50624a2a6fce50e\` ON \`campaign_analytic\` (\`campaignId\`, \`workflowId\`, \`emailId\`, \`linkId\`, \`eventAt\`)`);
    }

}
