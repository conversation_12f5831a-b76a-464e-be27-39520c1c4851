import {Routes} from "../../routes";
import {SharedController} from "./controller";


export const sharedRoutes: Routes = {
    basePath: '/shared',
    middleware: [],
    routes: {
        '/:viewId': {
            get: {controller: SharedController, action: "getView"},
        },
        '/:viewId/form-responses': {
            post: {controller: SharedController, action: "submitFormResponses"},
        },
        '/:viewId/form-upload': {
            post: {controller: SharedController, action: "uploadFormFile"},
        },
    }
}