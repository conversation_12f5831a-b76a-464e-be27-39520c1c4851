# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Deploy To Stage

on:
  push:
    branches: [dev]

jobs:
  deploy-node:
    uses: Opendashboard-Inc/opendashboard-workflow/.github/workflows/deploy-node.yaml@main
    with:
      NAME: opendashboard-api
      PROJECT_PATH: ${{ vars.STAGE_PATH }}
      ENV_FILE_PATH: ${{ vars.STAGE_ENV_PATH }}
      RUN_MIGRATION: true
    secrets:
      SSH_HOST: ${{ vars.STAGE_HOST }}
      SSH_KEY: ${{ secrets.SSH_KEY }}
      SSH_USER: ${{ secrets.SSH_USER }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}

  tag-version:
    needs: deploy-node
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: '0'
      - uses: anothrNick/github-tag-action@1.67.0
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          WITH_V: false
          CUSTOM_TAG: dev-1.0.${{github.run_number}}



