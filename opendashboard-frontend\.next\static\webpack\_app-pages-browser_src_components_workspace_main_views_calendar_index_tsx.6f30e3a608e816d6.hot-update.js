"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, selectedEvent, onClick, onDragStart, canEditData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute left-2 right-2 px-3 py-2 rounded-md text-xs shadow-sm border cursor-pointer\", \"transition-all duration-200 hover:shadow-md\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700\"),\n        style: style,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium truncate leading-tight\",\n                children: event.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-[10px] mt-0.5 opacity-75\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CalendarEventItem;\nconst TimeSlot = (param)=>{\n    let { hour, date, children, onDragOver, onDrop, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        className: \"flex-1 relative border-b border-gray-100 min-h-[60px] cursor-pointer\",\n        style: {\n            height: \"60px\"\n        },\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TimeSlot;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, onEventDrop } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), selectedDate));\n    const currentTimePosition = (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? {\n        hour: new Date().getHours(),\n        minutes: new Date().getMinutes()\n    } : null;\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Create a simple, visible drag image\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"300px\";\n        dragImg.style.height = \"60px\";\n        dragImg.style.background = \"white\";\n        dragImg.style.border = \"1px solid #d1d5db\";\n        dragImg.style.borderRadius = \"6px\";\n        dragImg.style.padding = \"12px\";\n        dragImg.style.boxShadow = \"0 4px 6px -1px rgb(0 0 0 / 0.1)\";\n        dragImg.style.fontSize = \"12px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"#1f2937\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.opacity = \"0.9\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; margin-bottom: 4px; color: #1f2937; line-height: 1.2;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 10px; color: #6b7280; line-height: 1.2;\">\\n        ').concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        // Set drag image with proper offset\n        e.dataTransfer.setDragImage(dragImg, 150, 30);\n        // Clean up after a short delay\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (hour, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the time slot\n        const timeSlotHeight = 60; // height of time slot in pixels\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const minutes = Math.floor(relativeY / timeSlotHeight * 60);\n        // Create new date with calculated minutes\n        const newDate = new Date(selectedDate);\n        newDate.setHours(hour, minutes, 0, 0);\n        // Check if the new date is the same as the original\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    if (dayEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b bg-secondary sticky top-0 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events scheduled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"No events planned for \".concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \".\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    const newDate = new Date(selectedDate);\n                                    newDate.setHours(new Date().getHours(), 0, 0, 0);\n                                    openAddEventForm(newDate);\n                                },\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-secondary sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto relative bg-white\",\n                id: \"day-view-container\",\n                children: [\n                    hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex border-b border-gray-50 hover:bg-gray-25 transition-colors\",\n                            style: {\n                                height: \"60px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 sticky left-0 bg-white z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), isMobile ? \"h\" : \"h\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[10px] text-gray-400\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), \"a\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                    hour: hour,\n                                    date: selectedDate,\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(hour, e),\n                                    onClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(selectedDate);\n                                            newDate.setHours(hour, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: dayEvents.filter((event)=>{\n                                        const eventHour = new Date(event.start).getHours();\n                                        return eventHour === hour;\n                                    }).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                            event: event,\n                                            selectedEvent: selectedEvent,\n                                            style: {\n                                                top: \"\".concat(new Date(event.start).getMinutes() / 60 * 100, \"%\"),\n                                                height: \"\".concat(Math.max(30, (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventDurationInMinutes)(event) / 60 * 100), \"%\"),\n                                                zIndex: selectedEvent === event.id ? 20 : 10\n                                            },\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                const container = document.getElementById(\"day-view-container\");\n                                                if (container) {\n                                                    savedScrollTop.current = container.scrollTop;\n                                                }\n                                                setSelectedEvent(event.id);\n                                                handleEventClick(event);\n                                            },\n                                            onDragStart: handleDragStart,\n                                            canEditData: canEditData\n                                        }, event.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined)),\n                    currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-0 flex items-center z-30 pointer-events-none\", isMobile ? \"left-14\" : \"left-20\"),\n                        style: {\n                            top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize\n    ];\n});\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"TimeSlot\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ })

});