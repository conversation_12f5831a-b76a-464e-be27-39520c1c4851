import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"
import {PaymentCurrency} from "./templatePurchase";
import {KeyValueStore} from "./WorkspaceMemberSettings";

enum PayoutProcessor {
    Paystack="paystack"
}

@Entity()
export class PayoutMethod {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Column({type: 'varchar', nullable: false, unique: true})
    creatorId: string

    @Column({type: 'varchar', nullable: false, unique: true})
    affiliateId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    payoutProvider: PayoutProcessor

    @Index()
    @Column({type: 'varchar', nullable: true})
    payoutProviderReference: string

    @Index()
    @Column({type: 'bool', default: false})
    isVerified: boolean

    @Index()
    @Column({type: "varchar", nullable: true})
    currency: PaymentCurrency

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

