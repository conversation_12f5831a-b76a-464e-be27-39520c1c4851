import {NextFunction, Request, Response} from "express"
import {GetUpload} from "../../businessLogic/upload";
import {BadRequestError, ErrorMessage, NotfoundError} from "../../errors/AppError";
import {WorkspaceUploadService} from "../../service/workspaceUpload";

const crypto = require("crypto")

export class AttachmentController {

    async getAttachment(request: Request, response: Response, next: NextFunction) {
        const workspaceId = request.params.workspaceId
        const attachmentId = request.params.attachmentId
        const connectTs = Number(request.params.connectTs)
        const hash = request.params.hash

        const expectedHash = crypto.createHash('sha256').update(`${workspaceId}|${attachmentId}|${connectTs}`).digest('hex')
        console.log(expectedHash, hash)
        if (expectedHash !== hash) {
            throw new BadRequestError(ErrorMessage.LinkIsInvalidOrExpired)
        }

        const date = new Date(connectTs).getTime()
        const currentTime = new Date().getTime()
        const fiveHoursInMs = 5 * 60 * 60 * 1000; // 5 hours in milliseconds

        if (currentTime - date > fiveHoursInMs) {
            throw new BadRequestError(ErrorMessage.LinkIsExpired);
        }

        const service = new WorkspaceUploadService()
        const upload = await service.findOne({id: Number(attachmentId)})

        // @Todo: Investigate why all anonymous upload are from the workspace: 41576ddc-996b-465f-b4c7-f73c0614bc38
        // @Todo: Above issue is fixed, revert code later
        // const upload = await GetUpload(workspaceId, attachmentId)
        if (!upload) throw new NotfoundError(ErrorMessage.EntityNotFound)

        return response.redirect(upload.finalUrl);
    }


}