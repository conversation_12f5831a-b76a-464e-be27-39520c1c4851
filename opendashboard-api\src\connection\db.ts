import {DataSource, EntityTarget, QueryRunner, Repository} from "typeorm"
import dataSourceOption = require("../config/typeorm-config");

export const dbDataSource = new DataSource(dataSourceOption)
export default DataSource

let queryRunner: QueryRunner = null;

export const setGlobalQueryRunner = (runner: QueryRunner) => {
	queryRunner = runner;
}

export const getGlobalQueryRunner = () => {
	return queryRunner
}

export const getRepository = <Entity>(target: EntityTarget<Entity>): Repository<Entity> => {
	const runner = getGlobalQueryRunner();
	const manager = runner ? runner.manager : null
	return (manager || dbDataSource).getRepository(target);
};
