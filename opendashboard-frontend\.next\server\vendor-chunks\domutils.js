"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domutils";
exports.ids = ["vendor-chunks/domutils"];
exports.modules = {

/***/ "(ssr)/./node_modules/domutils/lib/feeds.js":
/*!********************************************!*\
  !*** ./node_modules/domutils/lib/feeds.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getFeed = getFeed;\nvar stringify_js_1 = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/domutils/lib/stringify.js\");\nvar legacy_js_1 = __webpack_require__(/*! ./legacy.js */ \"(ssr)/./node_modules/domutils/lib/legacy.js\");\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n    var feedRoot = getOneElement(isValidFeed, doc);\n    return !feedRoot\n        ? null\n        : feedRoot.name === \"feed\"\n            ? getAtomFeed(feedRoot)\n            : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n    var _a;\n    var childs = feedRoot.children;\n    var feed = {\n        type: \"atom\",\n        items: (0, legacy_js_1.getElementsByTagName)(\"entry\", childs).map(function (item) {\n            var _a;\n            var children = item.children;\n            var entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"id\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            var href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n            if (href) {\n                entry.link = href;\n            }\n            var description = fetch(\"summary\", children) || fetch(\"content\", children);\n            if (description) {\n                entry.description = description;\n            }\n            var pubDate = fetch(\"updated\", children);\n            if (pubDate) {\n                entry.pubDate = new Date(pubDate);\n            }\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"id\", \"id\", childs);\n    addConditionally(feed, \"title\", \"title\", childs);\n    var href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n    if (href) {\n        feed.link = href;\n    }\n    addConditionally(feed, \"description\", \"subtitle\", childs);\n    var updated = fetch(\"updated\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"email\", childs, true);\n    return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n    var _a, _b;\n    var childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n    var feed = {\n        type: feedRoot.name.substr(0, 3),\n        id: \"\",\n        items: (0, legacy_js_1.getElementsByTagName)(\"item\", feedRoot.children).map(function (item) {\n            var children = item.children;\n            var entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"guid\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            addConditionally(entry, \"link\", \"link\", children);\n            addConditionally(entry, \"description\", \"description\", children);\n            var pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n            if (pubDate)\n                entry.pubDate = new Date(pubDate);\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"title\", \"title\", childs);\n    addConditionally(feed, \"link\", \"link\", childs);\n    addConditionally(feed, \"description\", \"description\", childs);\n    var updated = fetch(\"lastBuildDate\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n    return feed;\n}\nvar MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nvar MEDIA_KEYS_INT = [\n    \"fileSize\",\n    \"bitrate\",\n    \"framerate\",\n    \"samplingrate\",\n    \"channels\",\n    \"duration\",\n    \"height\",\n    \"width\",\n];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n    return (0, legacy_js_1.getElementsByTagName)(\"media:content\", where).map(function (elem) {\n        var attribs = elem.attribs;\n        var media = {\n            medium: attribs[\"medium\"],\n            isDefault: !!attribs[\"isDefault\"],\n        };\n        for (var _i = 0, MEDIA_KEYS_STRING_1 = MEDIA_KEYS_STRING; _i < MEDIA_KEYS_STRING_1.length; _i++) {\n            var attrib = MEDIA_KEYS_STRING_1[_i];\n            if (attribs[attrib]) {\n                media[attrib] = attribs[attrib];\n            }\n        }\n        for (var _a = 0, MEDIA_KEYS_INT_1 = MEDIA_KEYS_INT; _a < MEDIA_KEYS_INT_1.length; _a++) {\n            var attrib = MEDIA_KEYS_INT_1[_a];\n            if (attribs[attrib]) {\n                media[attrib] = parseInt(attribs[attrib], 10);\n            }\n        }\n        if (attribs[\"expression\"]) {\n            media.expression = attribs[\"expression\"];\n        }\n        return media;\n    });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n    return (0, legacy_js_1.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse) {\n    if (recurse === void 0) { recurse = false; }\n    return (0, stringify_js_1.textContent)((0, legacy_js_1.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse) {\n    if (recurse === void 0) { recurse = false; }\n    var val = fetch(tagName, where, recurse);\n    if (val)\n        obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n    return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}\n//# sourceMappingURL=feeds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/feeds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/helpers.js":
/*!**********************************************!*\
  !*** ./node_modules/domutils/lib/helpers.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocumentPosition = void 0;\nexports.removeSubsets = removeSubsets;\nexports.compareDocumentPosition = compareDocumentPosition;\nexports.uniqueSort = uniqueSort;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n    var idx = nodes.length;\n    /*\n     * Check if each node (or one of its ancestors) is already contained in the\n     * array.\n     */\n    while (--idx >= 0) {\n        var node = nodes[idx];\n        /*\n         * Remove the node if it is not unique.\n         * We are going through the array from the end, so we only\n         * have to check nodes that preceed the node under consideration in the array.\n         */\n        if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n            nodes.splice(idx, 1);\n            continue;\n        }\n        for (var ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n            if (nodes.includes(ancestor)) {\n                nodes.splice(idx, 1);\n                break;\n            }\n        }\n    }\n    return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n    DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n    DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n    DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n    DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n    DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (exports.DocumentPosition = DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n    var aParents = [];\n    var bParents = [];\n    if (nodeA === nodeB) {\n        return 0;\n    }\n    var current = (0, domhandler_1.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n    while (current) {\n        aParents.unshift(current);\n        current = current.parent;\n    }\n    current = (0, domhandler_1.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n    while (current) {\n        bParents.unshift(current);\n        current = current.parent;\n    }\n    var maxIdx = Math.min(aParents.length, bParents.length);\n    var idx = 0;\n    while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n        idx++;\n    }\n    if (idx === 0) {\n        return DocumentPosition.DISCONNECTED;\n    }\n    var sharedParent = aParents[idx - 1];\n    var siblings = sharedParent.children;\n    var aSibling = aParents[idx];\n    var bSibling = bParents[idx];\n    if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n        if (sharedParent === nodeB) {\n            return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n        }\n        return DocumentPosition.FOLLOWING;\n    }\n    if (sharedParent === nodeA) {\n        return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n    }\n    return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n    nodes = nodes.filter(function (node, i, arr) { return !arr.includes(node, i + 1); });\n    nodes.sort(function (a, b) {\n        var relative = compareDocumentPosition(a, b);\n        if (relative & DocumentPosition.PRECEDING) {\n            return -1;\n        }\n        else if (relative & DocumentPosition.FOLLOWING) {\n            return 1;\n        }\n        return 0;\n    });\n    return nodes;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/domutils/lib/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hasChildren = exports.isDocument = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = void 0;\n__exportStar(__webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/domutils/lib/stringify.js\"), exports);\n__exportStar(__webpack_require__(/*! ./traversal.js */ \"(ssr)/./node_modules/domutils/lib/traversal.js\"), exports);\n__exportStar(__webpack_require__(/*! ./manipulation.js */ \"(ssr)/./node_modules/domutils/lib/manipulation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./querying.js */ \"(ssr)/./node_modules/domutils/lib/querying.js\"), exports);\n__exportStar(__webpack_require__(/*! ./legacy.js */ \"(ssr)/./node_modules/domutils/lib/legacy.js\"), exports);\n__exportStar(__webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/domutils/lib/helpers.js\"), exports);\n__exportStar(__webpack_require__(/*! ./feeds.js */ \"(ssr)/./node_modules/domutils/lib/feeds.js\"), exports);\n/** @deprecated Use these methods from `domhandler` directly. */\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nObject.defineProperty(exports, \"isTag\", ({ enumerable: true, get: function () { return domhandler_1.isTag; } }));\nObject.defineProperty(exports, \"isCDATA\", ({ enumerable: true, get: function () { return domhandler_1.isCDATA; } }));\nObject.defineProperty(exports, \"isText\", ({ enumerable: true, get: function () { return domhandler_1.isText; } }));\nObject.defineProperty(exports, \"isComment\", ({ enumerable: true, get: function () { return domhandler_1.isComment; } }));\nObject.defineProperty(exports, \"isDocument\", ({ enumerable: true, get: function () { return domhandler_1.isDocument; } }));\nObject.defineProperty(exports, \"hasChildren\", ({ enumerable: true, get: function () { return domhandler_1.hasChildren; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/legacy.js":
/*!*********************************************!*\
  !*** ./node_modules/domutils/lib/legacy.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.testElement = testElement;\nexports.getElements = getElements;\nexports.getElementById = getElementById;\nexports.getElementsByTagName = getElementsByTagName;\nexports.getElementsByClassName = getElementsByClassName;\nexports.getElementsByTagType = getElementsByTagType;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nvar querying_js_1 = __webpack_require__(/*! ./querying.js */ \"(ssr)/./node_modules/domutils/lib/querying.js\");\n/**\n * A map of functions to check nodes against.\n */\nvar Checks = {\n    tag_name: function (name) {\n        if (typeof name === \"function\") {\n            return function (elem) { return (0, domhandler_1.isTag)(elem) && name(elem.name); };\n        }\n        else if (name === \"*\") {\n            return domhandler_1.isTag;\n        }\n        return function (elem) { return (0, domhandler_1.isTag)(elem) && elem.name === name; };\n    },\n    tag_type: function (type) {\n        if (typeof type === \"function\") {\n            return function (elem) { return type(elem.type); };\n        }\n        return function (elem) { return elem.type === type; };\n    },\n    tag_contains: function (data) {\n        if (typeof data === \"function\") {\n            return function (elem) { return (0, domhandler_1.isText)(elem) && data(elem.data); };\n        }\n        return function (elem) { return (0, domhandler_1.isText)(elem) && elem.data === data; };\n    },\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n    if (typeof value === \"function\") {\n        return function (elem) { return (0, domhandler_1.isTag)(elem) && value(elem.attribs[attrib]); };\n    }\n    return function (elem) { return (0, domhandler_1.isTag)(elem) && elem.attribs[attrib] === value; };\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n    return function (elem) { return a(elem) || b(elem); };\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n    var funcs = Object.keys(options).map(function (key) {\n        var value = options[key];\n        return Object.prototype.hasOwnProperty.call(Checks, key)\n            ? Checks[key](value)\n            : getAttribCheck(key, value);\n    });\n    return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n    var test = compileTest(options);\n    return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit) {\n    if (limit === void 0) { limit = Infinity; }\n    var test = compileTest(options);\n    return test ? (0, querying_js_1.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse) {\n    if (recurse === void 0) { recurse = true; }\n    if (!Array.isArray(nodes))\n        nodes = [nodes];\n    return (0, querying_js_1.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return (0, querying_js_1.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/legacy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/manipulation.js":
/*!***************************************************!*\
  !*** ./node_modules/domutils/lib/manipulation.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.removeElement = removeElement;\nexports.replaceElement = replaceElement;\nexports.appendChild = appendChild;\nexports.append = append;\nexports.prependChild = prependChild;\nexports.prepend = prepend;\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n    if (elem.prev)\n        elem.prev.next = elem.next;\n    if (elem.next)\n        elem.next.prev = elem.prev;\n    if (elem.parent) {\n        var childs = elem.parent.children;\n        var childsIndex = childs.lastIndexOf(elem);\n        if (childsIndex >= 0) {\n            childs.splice(childsIndex, 1);\n        }\n    }\n    elem.next = null;\n    elem.prev = null;\n    elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n    var prev = (replacement.prev = elem.prev);\n    if (prev) {\n        prev.next = replacement;\n    }\n    var next = (replacement.next = elem.next);\n    if (next) {\n        next.prev = replacement;\n    }\n    var parent = (replacement.parent = elem.parent);\n    if (parent) {\n        var childs = parent.children;\n        childs[childs.lastIndexOf(elem)] = replacement;\n        elem.parent = null;\n    }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n    removeElement(child);\n    child.next = null;\n    child.parent = parent;\n    if (parent.children.push(child) > 1) {\n        var sibling = parent.children[parent.children.length - 2];\n        sibling.next = child;\n        child.prev = sibling;\n    }\n    else {\n        child.prev = null;\n    }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n    removeElement(next);\n    var parent = elem.parent;\n    var currNext = elem.next;\n    next.next = currNext;\n    next.prev = elem;\n    elem.next = next;\n    next.parent = parent;\n    if (currNext) {\n        currNext.prev = next;\n        if (parent) {\n            var childs = parent.children;\n            childs.splice(childs.lastIndexOf(currNext), 0, next);\n        }\n    }\n    else if (parent) {\n        parent.children.push(next);\n    }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n    removeElement(child);\n    child.parent = parent;\n    child.prev = null;\n    if (parent.children.unshift(child) !== 1) {\n        var sibling = parent.children[1];\n        sibling.prev = child;\n        child.next = sibling;\n    }\n    else {\n        child.next = null;\n    }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n    removeElement(prev);\n    var parent = elem.parent;\n    if (parent) {\n        var childs = parent.children;\n        childs.splice(childs.indexOf(elem), 0, prev);\n    }\n    if (elem.prev) {\n        elem.prev.next = prev;\n    }\n    prev.parent = parent;\n    prev.prev = elem.prev;\n    prev.next = elem;\n    elem.prev = prev;\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/manipulation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/querying.js":
/*!***********************************************!*\
  !*** ./node_modules/domutils/lib/querying.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.filter = filter;\nexports.find = find;\nexports.findOneChild = findOneChild;\nexports.findOne = findOne;\nexports.existsOne = existsOne;\nexports.findAll = findAll;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse, limit) {\n    if (recurse === void 0) { recurse = true; }\n    if (limit === void 0) { limit = Infinity; }\n    return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n    var result = [];\n    /** Stack of the arrays we are looking at. */\n    var nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    /** Stack of the indices within the arrays. */\n    var indexStack = [0];\n    for (;;) {\n        // First, check if the current array has any more elements to look at.\n        if (indexStack[0] >= nodeStack[0].length) {\n            // If we have no more arrays to look at, we are done.\n            if (indexStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        var elem = nodeStack[0][indexStack[0]++];\n        if (test(elem)) {\n            result.push(elem);\n            if (--limit <= 0)\n                return result;\n        }\n        if (recurse && (0, domhandler_1.hasChildren)(elem) && elem.children.length > 0) {\n            /*\n             * Add the children to the stack. We are depth-first, so this is\n             * the next array we look at.\n             */\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n    return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse) {\n    if (recurse === void 0) { recurse = true; }\n    var searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n    for (var i = 0; i < searchedNodes.length; i++) {\n        var node = searchedNodes[i];\n        if ((0, domhandler_1.isTag)(node) && test(node)) {\n            return node;\n        }\n        if (recurse && (0, domhandler_1.hasChildren)(node) && node.children.length > 0) {\n            var found = findOne(test, node.children, true);\n            if (found)\n                return found;\n        }\n    }\n    return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n    return (Array.isArray(nodes) ? nodes : [nodes]).some(function (node) {\n        return ((0, domhandler_1.isTag)(node) && test(node)) ||\n            ((0, domhandler_1.hasChildren)(node) && existsOne(test, node.children));\n    });\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n    var result = [];\n    var nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    var indexStack = [0];\n    for (;;) {\n        if (indexStack[0] >= nodeStack[0].length) {\n            if (nodeStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        var elem = nodeStack[0][indexStack[0]++];\n        if ((0, domhandler_1.isTag)(elem) && test(elem))\n            result.push(elem);\n        if ((0, domhandler_1.hasChildren)(elem) && elem.children.length > 0) {\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n//# sourceMappingURL=querying.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG9tdXRpbHMvbGliL3F1ZXJ5aW5nLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZCxZQUFZO0FBQ1osb0JBQW9CO0FBQ3BCLGVBQWU7QUFDZixpQkFBaUI7QUFDakIsZUFBZTtBQUNmLG1CQUFtQixtQkFBTyxDQUFDLGdFQUFZO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUIsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCO0FBQ0Esb0JBQW9CLDBCQUEwQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RvbXV0aWxzL2xpYi9xdWVyeWluZy5qcz8yODUyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5maWx0ZXIgPSBmaWx0ZXI7XG5leHBvcnRzLmZpbmQgPSBmaW5kO1xuZXhwb3J0cy5maW5kT25lQ2hpbGQgPSBmaW5kT25lQ2hpbGQ7XG5leHBvcnRzLmZpbmRPbmUgPSBmaW5kT25lO1xuZXhwb3J0cy5leGlzdHNPbmUgPSBleGlzdHNPbmU7XG5leHBvcnRzLmZpbmRBbGwgPSBmaW5kQWxsO1xudmFyIGRvbWhhbmRsZXJfMSA9IHJlcXVpcmUoXCJkb21oYW5kbGVyXCIpO1xuLyoqXG4gKiBTZWFyY2ggYSBub2RlIGFuZCBpdHMgY2hpbGRyZW4gZm9yIG5vZGVzIHBhc3NpbmcgYSB0ZXN0IGZ1bmN0aW9uLiBJZiBgbm9kZWAgaXMgbm90IGFuIGFycmF5LCBpdCB3aWxsIGJlIHdyYXBwZWQgaW4gb25lLlxuICpcbiAqIEBjYXRlZ29yeSBRdWVyeWluZ1xuICogQHBhcmFtIHRlc3QgRnVuY3Rpb24gdG8gdGVzdCBub2RlcyBvbi5cbiAqIEBwYXJhbSBub2RlIE5vZGUgdG8gc2VhcmNoLiBXaWxsIGJlIGluY2x1ZGVkIGluIHRoZSByZXN1bHQgc2V0IGlmIGl0IG1hdGNoZXMuXG4gKiBAcGFyYW0gcmVjdXJzZSBBbHNvIGNvbnNpZGVyIGNoaWxkIG5vZGVzLlxuICogQHBhcmFtIGxpbWl0IE1heGltdW0gbnVtYmVyIG9mIG5vZGVzIHRvIHJldHVybi5cbiAqIEByZXR1cm5zIEFsbCBub2RlcyBwYXNzaW5nIGB0ZXN0YC5cbiAqL1xuZnVuY3Rpb24gZmlsdGVyKHRlc3QsIG5vZGUsIHJlY3Vyc2UsIGxpbWl0KSB7XG4gICAgaWYgKHJlY3Vyc2UgPT09IHZvaWQgMCkgeyByZWN1cnNlID0gdHJ1ZTsgfVxuICAgIGlmIChsaW1pdCA9PT0gdm9pZCAwKSB7IGxpbWl0ID0gSW5maW5pdHk7IH1cbiAgICByZXR1cm4gZmluZCh0ZXN0LCBBcnJheS5pc0FycmF5KG5vZGUpID8gbm9kZSA6IFtub2RlXSwgcmVjdXJzZSwgbGltaXQpO1xufVxuLyoqXG4gKiBTZWFyY2ggYW4gYXJyYXkgb2Ygbm9kZXMgYW5kIHRoZWlyIGNoaWxkcmVuIGZvciBub2RlcyBwYXNzaW5nIGEgdGVzdCBmdW5jdGlvbi5cbiAqXG4gKiBAY2F0ZWdvcnkgUXVlcnlpbmdcbiAqIEBwYXJhbSB0ZXN0IEZ1bmN0aW9uIHRvIHRlc3Qgbm9kZXMgb24uXG4gKiBAcGFyYW0gbm9kZXMgQXJyYXkgb2Ygbm9kZXMgdG8gc2VhcmNoLlxuICogQHBhcmFtIHJlY3Vyc2UgQWxzbyBjb25zaWRlciBjaGlsZCBub2Rlcy5cbiAqIEBwYXJhbSBsaW1pdCBNYXhpbXVtIG51bWJlciBvZiBub2RlcyB0byByZXR1cm4uXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgcGFzc2luZyBgdGVzdGAuXG4gKi9cbmZ1bmN0aW9uIGZpbmQodGVzdCwgbm9kZXMsIHJlY3Vyc2UsIGxpbWl0KSB7XG4gICAgdmFyIHJlc3VsdCA9IFtdO1xuICAgIC8qKiBTdGFjayBvZiB0aGUgYXJyYXlzIHdlIGFyZSBsb29raW5nIGF0LiAqL1xuICAgIHZhciBub2RlU3RhY2sgPSBbQXJyYXkuaXNBcnJheShub2RlcykgPyBub2RlcyA6IFtub2Rlc11dO1xuICAgIC8qKiBTdGFjayBvZiB0aGUgaW5kaWNlcyB3aXRoaW4gdGhlIGFycmF5cy4gKi9cbiAgICB2YXIgaW5kZXhTdGFjayA9IFswXTtcbiAgICBmb3IgKDs7KSB7XG4gICAgICAgIC8vIEZpcnN0LCBjaGVjayBpZiB0aGUgY3VycmVudCBhcnJheSBoYXMgYW55IG1vcmUgZWxlbWVudHMgdG8gbG9vayBhdC5cbiAgICAgICAgaWYgKGluZGV4U3RhY2tbMF0gPj0gbm9kZVN0YWNrWzBdLmxlbmd0aCkge1xuICAgICAgICAgICAgLy8gSWYgd2UgaGF2ZSBubyBtb3JlIGFycmF5cyB0byBsb29rIGF0LCB3ZSBhcmUgZG9uZS5cbiAgICAgICAgICAgIGlmIChpbmRleFN0YWNrLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBPdGhlcndpc2UsIHJlbW92ZSB0aGUgY3VycmVudCBhcnJheSBmcm9tIHRoZSBzdGFjay5cbiAgICAgICAgICAgIG5vZGVTdGFjay5zaGlmdCgpO1xuICAgICAgICAgICAgaW5kZXhTdGFjay5zaGlmdCgpO1xuICAgICAgICAgICAgLy8gTG9vcCBiYWNrIHRvIHRoZSBzdGFydCB0byBjb250aW51ZSB3aXRoIHRoZSBuZXh0IGFycmF5LlxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGVsZW0gPSBub2RlU3RhY2tbMF1baW5kZXhTdGFja1swXSsrXTtcbiAgICAgICAgaWYgKHRlc3QoZWxlbSkpIHtcbiAgICAgICAgICAgIHJlc3VsdC5wdXNoKGVsZW0pO1xuICAgICAgICAgICAgaWYgKC0tbGltaXQgPD0gMClcbiAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICB9XG4gICAgICAgIGlmIChyZWN1cnNlICYmICgwLCBkb21oYW5kbGVyXzEuaGFzQ2hpbGRyZW4pKGVsZW0pICYmIGVsZW0uY2hpbGRyZW4ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgLypcbiAgICAgICAgICAgICAqIEFkZCB0aGUgY2hpbGRyZW4gdG8gdGhlIHN0YWNrLiBXZSBhcmUgZGVwdGgtZmlyc3QsIHNvIHRoaXMgaXNcbiAgICAgICAgICAgICAqIHRoZSBuZXh0IGFycmF5IHdlIGxvb2sgYXQuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGluZGV4U3RhY2sudW5zaGlmdCgwKTtcbiAgICAgICAgICAgIG5vZGVTdGFjay51bnNoaWZ0KGVsZW0uY2hpbGRyZW4pO1xuICAgICAgICB9XG4gICAgfVxufVxuLyoqXG4gKiBGaW5kcyB0aGUgZmlyc3QgZWxlbWVudCBpbnNpZGUgb2YgYW4gYXJyYXkgdGhhdCBtYXRjaGVzIGEgdGVzdCBmdW5jdGlvbi4gVGhpcyBpcyBhbiBhbGlhcyBmb3IgYEFycmF5LnByb3RvdHlwZS5maW5kYC5cbiAqXG4gKiBAY2F0ZWdvcnkgUXVlcnlpbmdcbiAqIEBwYXJhbSB0ZXN0IEZ1bmN0aW9uIHRvIHRlc3Qgbm9kZXMgb24uXG4gKiBAcGFyYW0gbm9kZXMgQXJyYXkgb2Ygbm9kZXMgdG8gc2VhcmNoLlxuICogQHJldHVybnMgVGhlIGZpcnN0IG5vZGUgaW4gdGhlIGFycmF5IHRoYXQgcGFzc2VzIGB0ZXN0YC5cbiAqIEBkZXByZWNhdGVkIFVzZSBgQXJyYXkucHJvdG90eXBlLmZpbmRgIGRpcmVjdGx5LlxuICovXG5mdW5jdGlvbiBmaW5kT25lQ2hpbGQodGVzdCwgbm9kZXMpIHtcbiAgICByZXR1cm4gbm9kZXMuZmluZCh0ZXN0KTtcbn1cbi8qKlxuICogRmluZHMgb25lIGVsZW1lbnQgaW4gYSB0cmVlIHRoYXQgcGFzc2VzIGEgdGVzdC5cbiAqXG4gKiBAY2F0ZWdvcnkgUXVlcnlpbmdcbiAqIEBwYXJhbSB0ZXN0IEZ1bmN0aW9uIHRvIHRlc3Qgbm9kZXMgb24uXG4gKiBAcGFyYW0gbm9kZXMgTm9kZSBvciBhcnJheSBvZiBub2RlcyB0byBzZWFyY2guXG4gKiBAcGFyYW0gcmVjdXJzZSBBbHNvIGNvbnNpZGVyIGNoaWxkIG5vZGVzLlxuICogQHJldHVybnMgVGhlIGZpcnN0IG5vZGUgdGhhdCBwYXNzZXMgYHRlc3RgLlxuICovXG5mdW5jdGlvbiBmaW5kT25lKHRlc3QsIG5vZGVzLCByZWN1cnNlKSB7XG4gICAgaWYgKHJlY3Vyc2UgPT09IHZvaWQgMCkgeyByZWN1cnNlID0gdHJ1ZTsgfVxuICAgIHZhciBzZWFyY2hlZE5vZGVzID0gQXJyYXkuaXNBcnJheShub2RlcykgPyBub2RlcyA6IFtub2Rlc107XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBzZWFyY2hlZE5vZGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIHZhciBub2RlID0gc2VhcmNoZWROb2Rlc1tpXTtcbiAgICAgICAgaWYgKCgwLCBkb21oYW5kbGVyXzEuaXNUYWcpKG5vZGUpICYmIHRlc3Qobm9kZSkpIHtcbiAgICAgICAgICAgIHJldHVybiBub2RlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChyZWN1cnNlICYmICgwLCBkb21oYW5kbGVyXzEuaGFzQ2hpbGRyZW4pKG5vZGUpICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgdmFyIGZvdW5kID0gZmluZE9uZSh0ZXN0LCBub2RlLmNoaWxkcmVuLCB0cnVlKTtcbiAgICAgICAgICAgIGlmIChmb3VuZClcbiAgICAgICAgICAgICAgICByZXR1cm4gZm91bmQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG51bGw7XG59XG4vKipcbiAqIENoZWNrcyBpZiBhIHRyZWUgb2Ygbm9kZXMgY29udGFpbnMgYXQgbGVhc3Qgb25lIG5vZGUgcGFzc2luZyBhIHRlc3QuXG4gKlxuICogQGNhdGVnb3J5IFF1ZXJ5aW5nXG4gKiBAcGFyYW0gdGVzdCBGdW5jdGlvbiB0byB0ZXN0IG5vZGVzIG9uLlxuICogQHBhcmFtIG5vZGVzIEFycmF5IG9mIG5vZGVzIHRvIHNlYXJjaC5cbiAqIEByZXR1cm5zIFdoZXRoZXIgYSB0cmVlIG9mIG5vZGVzIGNvbnRhaW5zIGF0IGxlYXN0IG9uZSBub2RlIHBhc3NpbmcgdGhlIHRlc3QuXG4gKi9cbmZ1bmN0aW9uIGV4aXN0c09uZSh0ZXN0LCBub2Rlcykge1xuICAgIHJldHVybiAoQXJyYXkuaXNBcnJheShub2RlcykgPyBub2RlcyA6IFtub2Rlc10pLnNvbWUoZnVuY3Rpb24gKG5vZGUpIHtcbiAgICAgICAgcmV0dXJuICgoMCwgZG9taGFuZGxlcl8xLmlzVGFnKShub2RlKSAmJiB0ZXN0KG5vZGUpKSB8fFxuICAgICAgICAgICAgKCgwLCBkb21oYW5kbGVyXzEuaGFzQ2hpbGRyZW4pKG5vZGUpICYmIGV4aXN0c09uZSh0ZXN0LCBub2RlLmNoaWxkcmVuKSk7XG4gICAgfSk7XG59XG4vKipcbiAqIFNlYXJjaCBhbiBhcnJheSBvZiBub2RlcyBhbmQgdGhlaXIgY2hpbGRyZW4gZm9yIGVsZW1lbnRzIHBhc3NpbmcgYSB0ZXN0IGZ1bmN0aW9uLlxuICpcbiAqIFNhbWUgYXMgYGZpbmRgLCBidXQgbGltaXRlZCB0byBlbGVtZW50cyBhbmQgd2l0aCBsZXNzIG9wdGlvbnMsIGxlYWRpbmcgdG8gcmVkdWNlZCBjb21wbGV4aXR5LlxuICpcbiAqIEBjYXRlZ29yeSBRdWVyeWluZ1xuICogQHBhcmFtIHRlc3QgRnVuY3Rpb24gdG8gdGVzdCBub2RlcyBvbi5cbiAqIEBwYXJhbSBub2RlcyBBcnJheSBvZiBub2RlcyB0byBzZWFyY2guXG4gKiBAcmV0dXJucyBBbGwgbm9kZXMgcGFzc2luZyBgdGVzdGAuXG4gKi9cbmZ1bmN0aW9uIGZpbmRBbGwodGVzdCwgbm9kZXMpIHtcbiAgICB2YXIgcmVzdWx0ID0gW107XG4gICAgdmFyIG5vZGVTdGFjayA9IFtBcnJheS5pc0FycmF5KG5vZGVzKSA/IG5vZGVzIDogW25vZGVzXV07XG4gICAgdmFyIGluZGV4U3RhY2sgPSBbMF07XG4gICAgZm9yICg7Oykge1xuICAgICAgICBpZiAoaW5kZXhTdGFja1swXSA+PSBub2RlU3RhY2tbMF0ubGVuZ3RoKSB7XG4gICAgICAgICAgICBpZiAobm9kZVN0YWNrLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBPdGhlcndpc2UsIHJlbW92ZSB0aGUgY3VycmVudCBhcnJheSBmcm9tIHRoZSBzdGFjay5cbiAgICAgICAgICAgIG5vZGVTdGFjay5zaGlmdCgpO1xuICAgICAgICAgICAgaW5kZXhTdGFjay5zaGlmdCgpO1xuICAgICAgICAgICAgLy8gTG9vcCBiYWNrIHRvIHRoZSBzdGFydCB0byBjb250aW51ZSB3aXRoIHRoZSBuZXh0IGFycmF5LlxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGVsZW0gPSBub2RlU3RhY2tbMF1baW5kZXhTdGFja1swXSsrXTtcbiAgICAgICAgaWYgKCgwLCBkb21oYW5kbGVyXzEuaXNUYWcpKGVsZW0pICYmIHRlc3QoZWxlbSkpXG4gICAgICAgICAgICByZXN1bHQucHVzaChlbGVtKTtcbiAgICAgICAgaWYgKCgwLCBkb21oYW5kbGVyXzEuaGFzQ2hpbGRyZW4pKGVsZW0pICYmIGVsZW0uY2hpbGRyZW4ubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgaW5kZXhTdGFjay51bnNoaWZ0KDApO1xuICAgICAgICAgICAgbm9kZVN0YWNrLnVuc2hpZnQoZWxlbS5jaGlsZHJlbik7XG4gICAgICAgIH1cbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1xdWVyeWluZy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/querying.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/stringify.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/stringify.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getOuterHTML = getOuterHTML;\nexports.getInnerHTML = getInnerHTML;\nexports.getText = getText;\nexports.textContent = textContent;\nexports.innerText = innerText;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\nvar dom_serializer_1 = __importDefault(__webpack_require__(/*! dom-serializer */ \"(ssr)/./node_modules/dom-serializer/lib/index.js\"));\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(ssr)/./node_modules/domelementtype/lib/index.js\");\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n    return (0, dom_serializer_1.default)(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n    return (0, domhandler_1.hasChildren)(node)\n        ? node.children.map(function (node) { return getOuterHTML(node, options); }).join(\"\")\n        : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n    if (Array.isArray(node))\n        return node.map(getText).join(\"\");\n    if ((0, domhandler_1.isTag)(node))\n        return node.name === \"br\" ? \"\\n\" : getText(node.children);\n    if ((0, domhandler_1.isCDATA)(node))\n        return getText(node.children);\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n    if (Array.isArray(node))\n        return node.map(textContent).join(\"\");\n    if ((0, domhandler_1.hasChildren)(node) && !(0, domhandler_1.isComment)(node)) {\n        return textContent(node.children);\n    }\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n    if (Array.isArray(node))\n        return node.map(innerText).join(\"\");\n    if ((0, domhandler_1.hasChildren)(node) && (node.type === domelementtype_1.ElementType.Tag || (0, domhandler_1.isCDATA)(node))) {\n        return innerText(node.children);\n    }\n    if ((0, domhandler_1.isText)(node))\n        return node.data;\n    return \"\";\n}\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/domutils/lib/traversal.js":
/*!************************************************!*\
  !*** ./node_modules/domutils/lib/traversal.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getChildren = getChildren;\nexports.getParent = getParent;\nexports.getSiblings = getSiblings;\nexports.getAttributeValue = getAttributeValue;\nexports.hasAttrib = hasAttrib;\nexports.getName = getName;\nexports.nextElementSibling = nextElementSibling;\nexports.prevElementSibling = prevElementSibling;\nvar domhandler_1 = __webpack_require__(/*! domhandler */ \"(ssr)/./node_modules/domhandler/lib/index.js\");\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n    return (0, domhandler_1.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n    return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n    var _a, _b;\n    var parent = getParent(elem);\n    if (parent != null)\n        return getChildren(parent);\n    var siblings = [elem];\n    var prev = elem.prev, next = elem.next;\n    while (prev != null) {\n        siblings.unshift(prev);\n        (_a = prev, prev = _a.prev);\n    }\n    while (next != null) {\n        siblings.push(next);\n        (_b = next, next = _b.next);\n    }\n    return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n    var _a;\n    return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n    return (elem.attribs != null &&\n        Object.prototype.hasOwnProperty.call(elem.attribs, name) &&\n        elem.attribs[name] != null);\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n    return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n    var _a;\n    var next = elem.next;\n    while (next !== null && !(0, domhandler_1.isTag)(next))\n        (_a = next, next = _a.next);\n    return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n    var _a;\n    var prev = elem.prev;\n    while (prev !== null && !(0, domhandler_1.isTag)(prev))\n        (_a = prev, prev = _a.prev);\n    return prev;\n}\n//# sourceMappingURL=traversal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/domutils/lib/traversal.js\n");

/***/ })

};
;