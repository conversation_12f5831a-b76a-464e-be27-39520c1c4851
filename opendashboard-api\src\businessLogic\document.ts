import {ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, UnauthorizedError} from "../errors/AppError";
import {ViewType} from "opendb-app-db-utils/lib/typings/view";
import {AccessLevel} from "../entity/common";
import {ViewService} from "../service/view";
import {resolvePageAccessLevel} from "./page";
import {DocumentService} from "../service/document";
import {broadcastDocumentAdded, broadcastDocumentDeleted, broadcastDocumentUpdated, broadcastWorkspaceNote, broadcastWorkspaceNoteDeleted} from "../socketio/workspace";
import {GetMyWorkspace} from "./workspace";
import {DatabaseService} from "../service/database";
import {RecordService} from "../service/record";
import {Brackets, FindOptionsWhere, In, IsNull} from "typeorm";
import {Record} from "../entity/Record";
import {Document} from "../entity/Document";
import {Database} from "../entity/Database";
import {DocumentHistoryService} from "../service/documentHistory";
import {DocumentHistory, DocumentType} from "../entity/documentHistory";

export const CreatePageViewDocument = async (userId: string, workspaceId: string, pageId: string, viewId: string) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view || view.type !== ViewType.Document) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const s2 = new DocumentService()
    const document = await s2.insert({
        viewId,
        workspaceId,
        updatedById: userId,
        createdById: userId,
        contentJSON: [],
        contentText: ''
    })

    broadcastDocumentAdded(workspaceId, resolve.page, document)
    return {
        page: resolve.page, view, document
    }
}

export const UpdatePageViewDocument = async (userId: string, workspaceId: string, pageId: string, viewId: string, documentId: string, data: {
    name: string
}) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const name = data.name || ''
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view || view.type !== ViewType.Document) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const s2 = new DocumentService()
    const document = await s2.findOne({
        viewId,
        workspaceId,
        id: documentId
    })
    if (!document) throw new NotfoundError(ErrorMessage.EntityNotFound)

    await s2.update({viewId, workspaceId, id: documentId}, {name})

    broadcastDocumentUpdated(workspaceId, resolve.page, view.id, documentId, {name})

    const hS = new DocumentHistoryService()
    hS.saveHistory({
        name: data.name,
        createdById: userId,
        workspaceId,
        documentId: document.id,
        contentText: document.contentText,
        contentJSON: document.contentJSON,
        type: DocumentType.Document
    }).then()

    return {
        page: resolve.page, document, view
    }
}

export const UpdatePageViewDocumentContent = async (userId: string, workspaceId: string, pageId: string, viewId: string, documentId: string, data: {
    contentJSON: object
    contentText: string
}) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const contentJSON = data.contentJSON
    const contentText = data.contentText || ''

    if (!contentJSON) throw new RequiredParameterError("contentJSON")
    if (typeof contentJSON !== 'object') throw new InvalidParameterError("contentJSON")

    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view || view.type !== ViewType.Document) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const s2 = new DocumentService()
    const document = await s2.findOne({
        viewId,
        workspaceId,
        id: documentId
    })
    if (!document) throw new NotfoundError(ErrorMessage.EntityNotFound)

    await s2.update({viewId, workspaceId, id: documentId}, {contentJSON, contentText})

    broadcastDocumentUpdated(workspaceId, resolve.page, view.id, documentId, {contentJSON, contentText})
    return {
        page: resolve.page, document, view
    }
}

export const DeletePageViewDocumentContent = async (userId: string, workspaceId: string, pageId: string, viewId: string, documentId: string) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s2 = new DocumentService()
    const document = await s2.remove({
        viewId,
        workspaceId,
        id: documentId
    })
    broadcastDocumentDeleted(workspaceId, resolve.page, viewId, documentId)
    return {
        page: resolve.page, document
    }
}

export const GetPageViewDocuments = async (userId: string, workspaceId: string, pageId: string, viewId: string) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit, AccessLevel.View].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view || view.type !== ViewType.Document) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const s2 = new DocumentService()
    const documents = await s2.find({
        viewId,
        workspaceId,
    })
    return {
        page: resolve.page, documents, view
    }
}

export interface CreateNoteData {
    name?: string
    databaseId?: string
    recordId?: string
}

export const CreateNote = async (userId: string, workspaceId: string, data: CreateNoteData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    let record: Record = undefined
    let database: Database = undefined
    if (data.databaseId || data.recordId) {
        if (!data.databaseId || !data.recordId) {
            throw new RequiredParameterError("databaseId|recordId")
        }
        const dS = new DatabaseService()
        database = await dS.findOne({id: data.databaseId, workspaceId})
        if (!database) throw new NotfoundError(ErrorMessage.EntityNotFound)

        const rS = new RecordService()
        record = await rS.findOne({id: data.recordId, databaseId: data.databaseId})
        if (!record) throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const s2 = new DocumentService()
    const document = await s2.insert({
        name: data.name,
        workspaceId,
        updatedById: userId,
        createdById: userId,
        contentJSON: [],
        contentText: '',
        databaseId: data.databaseId,
        recordId: data.recordId
    })
    const note: WorkspaceNote = {
        record, document, database
    }
    broadcastWorkspaceNote(workspaceId, note)
    return {
        note
    }
}

export interface PaginationParams {
    page?: number
    perPage?: number
    query?: string
}

export const resolvePaginationParams = (params: PaginationParams) => {
    let offset = 0
    let query = (params.query || '').trim()
    let page = Number(params.page)
    const limit = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 20
    if (Number.isNaN(page) || page < 1) page = 1
    if (page > 1) {
        offset = (page - 1) * limit
    }
    return {
        offset, page, limit, query
    }
}

export interface GetNotesParams extends PaginationParams {
    databaseId?: string
    recordId?: string
    type?: 'record' | 'user'
}

export interface WorkspaceNote {
    document: Document
    record?: Record
    database?: Database
}

export const GetNotes = async (userId: string, workspaceId: string, params: GetNotesParams) => {
    const member = userId ? await GetMyWorkspace(userId, workspaceId) : null
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const notes: WorkspaceNote[] = []
    const {offset, page, limit} = resolvePaginationParams(params)

    const brackets = new Brackets(qb => {
        qb.where("d.createdById=:userId", {userId});
        qb.orWhere("d.updatedById=:userId", {userId});
        qb.orWhere("d.assignedToUserIds LIKE :likeUserId", {likeUserId: `%${userId}%`});
    })
    const qB = new DocumentService()
        .getRepository()
        .createQueryBuilder("d")
        .select()
        .addSelect("r")
        .where({workspaceId})
        .leftJoin(Record, "r", "d.recordId=r.id AND d.databaseId=r.databaseId")
        .limit(limit)
        .offset(offset)
        .orderBy({'d.updatedAt': 'DESC'})
    if (params.type === 'record' && params.databaseId && params.recordId) {
        qB.andWhere("d.databaseId=:databaseId AND d.recordId=:recordId", {databaseId: params.databaseId, recordId: params.recordId})
    } else if (params.type === 'user') {
        qB.andWhere(brackets)
    } else {
        return {notes}
    }
    if (params.query && !!params.query.trim()) {
        qB.andWhere("d.name LIKE :query", {query: `%${params.query.trim()}%`})
    }
    const rawResults = await qB.getRawMany()

    for (const r of rawResults) {
        const note: WorkspaceNote = {
            document: {} as Document
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "d") {
                note.document[field] = r[key]
            } else if (pre === "r") {
                if (!note.record) note.record = {} as Record
                note.record[field] = r[key]
            }
        }
        notes.push(note)
    }
    const databaseIds = notes.map(n => n.document.databaseId).filter(i => !!i)
    const databases = databaseIds.length > 0 ? await new DatabaseService().find({workspaceId, id: In(databaseIds)}) : []
    const databasesMap: { [id: string]: Database } = {}
    for (let database of databases) {
        databasesMap[database.id] = database
    }
    for (let note of notes) {
        if (note.document.databaseId && databasesMap[note.document.databaseId]) {
            note.database = databasesMap[note.document.databaseId]
        }
    }
    return {notes}
}

export interface DeleteNoteParams {
    id: string
}

export const DeleteNote = async (userId: string, workspaceId: string, params: DeleteNoteParams) => {
    const member = userId ? await GetMyWorkspace(userId, workspaceId) : null
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    const s2 = new DocumentService()
    const document = await s2.findOne({id: params.id, workspaceId, viewId: IsNull()})
    if (!document) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (document.createdById !== userId) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    await s2.remove({id: document.id})

    broadcastWorkspaceNoteDeleted(workspaceId, {document})

    return true
}

export interface UpdateNoteParams {
    id: string
    name?: string
}

export const UpdateNote = async (userId: string, workspaceId: string, params: UpdateNoteParams) => {
    const member = userId ? await GetMyWorkspace(userId, workspaceId) : null
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    const s2 = new DocumentService()
    const document = await s2.findOne({id: params.id, workspaceId, viewId: IsNull()})
    if (!document) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (document.createdById !== userId) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    let record: Record = undefined
    let database: Database = undefined

    if (document.databaseId || document.recordId) {
        if (!document.databaseId || !document.recordId) {
            throw new RequiredParameterError("databaseId|recordId")
        }
        const dS = new DatabaseService()
        database = await dS.findOne({id: document.databaseId, workspaceId})
        if (!database) throw new NotfoundError(ErrorMessage.EntityNotFound)

        const rS = new RecordService()
        record = await rS.findOne({id: document.recordId, databaseId: document.databaseId})
        if (!record) throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    await s2.update({id: params.id}, {name: params.name})

    const note: WorkspaceNote = {
        record, document: {...document, name: params.name}, database
    }
    broadcastWorkspaceNote(workspaceId, note)

    const hS = new DocumentHistoryService()
    hS.saveHistory({
        name: params.name,
        createdById: userId,
        workspaceId,
        documentId: document.id,
        contentText: document.contentText,
        contentJSON: document.contentJSON,
        type: DocumentType.Document
    }).then()

    return {note}
}

export interface GetDocumentHistoryParams extends PaginationParams {
    documentId: string
    recordId?: string
    createdById?: string
    type?: DocumentType
}

export const GetDocumentHistory = async (userId: string, workspaceId: string, params: GetDocumentHistoryParams) => {
    const {documentId, recordId, createdById} = params

    if (!documentId && !recordId) throw new RequiredParameterError("documentId|recordId")
    const type = params.type || DocumentType.Document

    const {offset, limit} = resolvePaginationParams(params)
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const s = new DocumentHistoryService()
    const where: FindOptionsWhere<DocumentHistory> = {workspaceId, type}
    if (documentId) where.documentId = documentId
    if (recordId) where.recordId = recordId
    if (createdById) where.createdById = createdById
    const histories = await s.find(where, {createdAt: "DESC"}, limit, offset)

    return {histories}
}

