import {GetMyWorkspace, RequestWorkspaceVariablesSecretsSubstitutionVars} from "./workspace";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, ServerProcessingError, UnauthorizedError} from "../errors/AppError";
import {WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {PaginationParams, resolvePaginationParams} from "./document";
import {CreateNewWorkflowData, WorkflowService} from "../service/workflow";
import {Workflow} from "../entity/workflow";
import {IWorkflowDefinition, IWorkflowNodeData, OpendashboardGenerateAIContentWorkflowNode_Configs, OpendashboardSendHTTPActionWorkflowNode_Configs, ScheduleTriggerWorkflowNode_Configs, ScheduleType, WorkflowActionType, WorkflowInstanceStatus, WorkflowNodeCategory, WorkflowStatus, WorkflowTaskStatus, WorkflowTriggerType} from "opendb-app-db-utils/lib/typings/workflow";
import {WorkflowInstanceService} from "../service/workflowInstance";
import {WorkflowInstance} from "../entity/workflowInstance";
import {WorkflowTaskService} from "../service/workflowTask";
import {DocumentHistoryService} from "../service/documentHistory";
import {DocumentType} from "../entity/documentHistory";
import {broadcastWorkflowUpdated} from "../socketio/workspace";
import {In} from "typeorm";
import {substituteTags} from "../utility/template";
import {sendHTTPRequest} from "../utility/http";
import {getNextScheduledDates} from "./runStarter/getNextScheduledDates";
import {consoleLog} from "./logtail";
import {arrayDeDuplicate, initDateWithTimezone, isDateObjValid, substituteVarsInObjects} from "opendb-app-db-utils/lib";
import {StartWorkflowRuns, triggerWorkflowRunner, WorkflowRunData} from "./runStarter/runStarter";
import {LinkService} from "../service/link";
import {CampaignAnalyticsData, CampaignAnalyticService} from "../service/campaignAnalyticService";
import {FinalizeWorkflowInstanceApproval} from "./webhook";
import {MarkerNodeTypes} from "opendb-app-db-utils/lib/utils/workflow";
import {AIProvider, generateAIContentAndLog, WORKFLOW_SYSTEM_PROMPT} from "./ai/ai";
import {getCoreApi} from "./integration";
import {RunStepParams} from "@opendashboard-inc/integration-core-api/dist/types";
import {getConnectionAPI, getFilesAPI, getIntegration, getStoreAPI, getWebhookURL} from "./integrationHelpers";
import {handleError} from "../config/error";
import {datePlusMinutes} from "opendb-app-db-utils/lib/methods/date";

const crypto = require("crypto")


export interface GetWorkflowsParams extends PaginationParams {
    id?: string
    triggerType?: WorkflowTriggerType
    triggerTypes?: WorkflowTriggerType[]
    triggerObjectId?: string
    status?: WorkflowStatus
}

const verifyPermission = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const isCollaborator = member.workspaceMember.role === WorkspaceMemberRole.Collaborator
    if (isCollaborator) throw new UnauthorizedError(ErrorMessage.UnableToFetchEntity)
    return {member}
}

export const GetWorkflows = async (userId: string, workspaceId: string, params: GetWorkflowsParams = {}) => {
    await verifyPermission(userId, workspaceId)

    const {offset, limit, query} = resolvePaginationParams(params)

    const s = new WorkflowService()
    const qB = s.getRepository()
        .createQueryBuilder("w")
        .where({workspaceId})

    if (query) {
        qB.andWhere(`w.name LIKE :query`, {query: `%${query}%`})
    }
    if (params.id) {
        qB.andWhere({id: params.id})
    }
    if (params.triggerType) {
        const triggerType = getTriggerType(params.triggerType)
        qB.andWhere({triggerType})
    }
    if (params.triggerTypes) {
        const triggerTypes = params.triggerTypes.map(t => getTriggerType(t))
        qB.andWhere({triggerType: In(triggerTypes)})
    }
    if (params.triggerObjectId) {
        qB.andWhere({triggerObjectId: params.triggerObjectId})
    }
    if (params.status) {
        qB.andWhere({status: params.status})
    }
    qB.limit(limit)
    qB.offset(offset)
    qB.orderBy("w.createdAt", "DESC")
    return qB.getMany()
}

export const GetWorkflow = async (userId: string, workspaceId: string, id: string) => {
    const workflows = await GetWorkflows(userId, workspaceId, {id})
    if (workflows.length === 0) throw new NotfoundError(ErrorMessage.EntityNotFound)
    return workflows[0]
}

export interface CreateWorkflowData {
    name?: string
}

export const CreateWorkflow = async (userId: string, workspaceId: string, requestData: CreateWorkflowData) => {
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()

    const data: CreateNewWorkflowData = {
        name: requestData.name,
        createdById: userId,
        updatedById: userId,
        workspaceId,
    }
    return await s.insert(data)
}

export interface UpdateWorkflowData {
    name?: string
    description?: string
    id: string
}

export const UpdateWorkflow = async (userId: string, workspaceId: string, requestData: UpdateWorkflowData) => {
    if (!requestData.name || !requestData.name.trim()) throw new RequiredParameterError("name")
    if (!requestData.id) throw new RequiredParameterError("id")
    const id = requestData.id

    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    await s.update({id}, {name: requestData.name, description: requestData.description})
    const nWorkflow: Workflow = {...workflow, name: requestData.name, description: requestData.description, updatedAt: new Date()}

    broadcastWorkflowUpdated(workspaceId, nWorkflow)

    return nWorkflow
}

export interface DeleteWorkflowData {
    id: string
}

export const DeleteWorkflow = async (userId: string, workspaceId: string, requestData: DeleteWorkflowData) => {
    const id = requestData.id
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    await s.remove({id, workspaceId})
}

export interface PublishWorkflowData {
    publish: boolean
}

export const PublishWorkflow = async (userId: string, workspaceId: string, id: string, requestData: PublishWorkflowData) => {
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    let update: Partial<Workflow>

    const isPreviouslyPublished = workflow.status === WorkflowStatus.Published
    let newTriggerActivated = false
    let oldTriggerDeactivated = false

    if (requestData.publish) {
        const draftDefinition = workflow.draftDefinition || workflow.definition
        if (!draftDefinition) throw new NotfoundError(ErrorMessage.WorkflowHasNoStepsYet)

        let actionCount = 0
        const nodes = Object.values(draftDefinition.map)
        if (nodes.length < 2) throw new NotfoundError(ErrorMessage.WorkflowHasNoStepsYet)

        for (let node of nodes) {
            if (node.category === WorkflowNodeCategory.TERMINAL || MarkerNodeTypes.includes(node.data?.type as any)) continue
            if (!node.isReady) throw new NotfoundError(ErrorMessage.WorkflowHasStepsThatAreNotConfiguredProperly)
            if (node.category === WorkflowNodeCategory.ACTION) {
                actionCount++
            }
        }
        if (actionCount === 0) throw new NotfoundError(ErrorMessage.WorkflowHasNoActionSteps)
        const trigger = draftDefinition.map[draftDefinition.triggerId]
        if (!trigger) throw new NotfoundError(ErrorMessage.WorkflowTriggerNotFound)

        let triggerObjectId: string | null = null
        if ([WorkflowTriggerType.Opendashboard_OnRecordCreated, WorkflowTriggerType.Opendashboard_OnRecordUpdated, WorkflowTriggerType.Opendashboard_OnRecordDeleted].includes(trigger.data.type as WorkflowTriggerType)) {
            triggerObjectId = getTriggerObjectId('database', trigger.data.configs?.databaseId)
        }
        const triggerType = getTriggerType(trigger.data.type as string)

        let nextRunAt: Date | null = null
        if (trigger.data.type === WorkflowTriggerType.Schedule_OnSchedule) {
            const config = trigger.data.configs as ScheduleTriggerWorkflowNode_Configs
            if (config.type === ScheduleType.Recurring) {
                try {
                    nextRunAt = getNextScheduledDates(config, new Date(), 1)[0]
                } catch (e) {
                    consoleLog("Error getting next run at", e)
                }
            } else {
                nextRunAt = initDateWithTimezone(config.date || '', config.timezone)
                if (nextRunAt && isDateObjValid(nextRunAt) && nextRunAt.getTime() < new Date().getTime()) {
                    nextRunAt = null
                }
            }
        } else {
            const typeSplit = triggerType.split(':')
            if (typeSplit.length > 1) {
                const integration = typeSplit[0]
                const trigger = typeSplit[1]

                const integrationInfo = await getIntegration(integration)
                if (integrationInfo && Object.keys(integrationInfo.triggers || {}).includes(trigger)) {
                    const triggerInfo = (integrationInfo.triggers || {})[trigger]
                    if (triggerInfo.type === "WEBHOOK") {
                        nextRunAt = datePlusMinutes(new Date(), 1)
                    }
                }
            }
        }


        update = {
            definition: draftDefinition,
            draftDefinition: null,
            status: WorkflowStatus.Published,
            publishedAt: new Date(),
            triggerType,
            errorRateWarningAt: null,
            nextRunAt,
            triggerObjectId
        }

        const hS = new DocumentHistoryService()
        hS.saveHistory({
            createdById: userId,
            workspaceId,
            documentId: workflow.id,
            contentText: '',
            name: workflow.name,
            contentJSON: draftDefinition,
            type: DocumentType.Workflow
        }).then()

        // Simplified state transition logic
        if (isPreviouslyPublished) {
            // Only activate new trigger if the type changed
            newTriggerActivated = workflow.triggerType !== triggerType
            // Only deactivate old trigger if the type changed
            oldTriggerDeactivated = workflow.triggerType !== triggerType
        } else {
            // New publish = new trigger activated
            newTriggerActivated = true
            oldTriggerDeactivated = false
        }
    } else {
        update = {
            status: WorkflowStatus.Draft
        }
        oldTriggerDeactivated = isPreviouslyPublished
        newTriggerActivated = false
    }
    if (!update) throw new ServerProcessingError(ErrorMessage.UnableToProcessRequest)

    await s.update({id}, update)
    const nWorkflow: Workflow = {...workflow, ...update, updatedAt: new Date()}

    if (newTriggerActivated) {
        triggerIntegrationEnablementCallback(nWorkflow, 'onEnabled').then().catch(e => {
            consoleLog("Error activating integration trigger", {workflowId: workflow.id}, e)
            handleError(e, {workflowId: workflow.id}, false)
        })

    }
    if (oldTriggerDeactivated) {
        triggerIntegrationEnablementCallback(workflow, 'onDisabled').then().catch(e => {
            consoleLog("Error activating integration trigger", {workflowId: workflow.id}, e)
            handleError(e, {workflowId: workflow.id}, false)
        })
    }

    broadcastWorkflowUpdated(workspaceId, nWorkflow)

    return nWorkflow
}

// const getIntegrationTriggerNextRunAt = async (triggerType: string, workspaceId: string) => {
//     const typeSplit = triggerType.split(':')
//     if (typeSplit.length < 2) return null;
//
//     const integration = typeSplit[0]
//     const trigger = typeSplit[1]
//     const integrationInfo = await getIntegration(integration)
//     if (!(integrationInfo && Object.keys(integrationInfo.triggers || {}).includes(trigger))) return null
//
//     const triggerInfo = (integrationInfo.triggers || {})[trigger]
//     if (triggerInfo.type !== "WEBHOOK") return null
//     const billingCycle = await CurrentBillingCycle(workspaceId)
//
//     const plan = WorkspacePlanPriceIdMap[billingCycle.planId || 'free']
//
//     // const polling
//     return datePlusMinutes(new Date(), 1)
// }

const triggerIntegrationEnablementCallback = async (workflow: Workflow, action: "onEnabled" | 'onDisabled') => {
    const definition = workflow.definition
    const triggerNode = definition.map[definition.triggerId]
    const type = workflow.triggerType
    const typeSplit = type.split(':')
    if (typeSplit.length < 2) return
    const integration = typeSplit[0]
    const trigger = typeSplit[1]

    const data = triggerNode.data as IWorkflowNodeData
    if (!data) return
    const connectionId = data.connectionId
    const rawProps = data.configs || {}

    const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workflow.workspaceId)
    const propsValue = substituteVarsInObjects(rawProps || {}, vars, 'curly')

    const webhookUrl = getWebhookURL(workflow.id, integration, trigger)

    const params: RunStepParams<void> = {
        connection: getConnectionAPI(workflow.workspaceId),
        files: getFilesAPI(workflow.workspaceId),
        integration: {name: integration},
        log: (...args: any[]) => consoleLog(`Logs from integration on activated (${integration}${trigger}) `, ...args),
        propsValue: propsValue,
        store: getStoreAPI(workflow.workspaceId, integration, workflow.id),
        type: 'trigger',
        connectionId,
        name: trigger,
        webhookUrl,
        mode: "run"
    }
    const coreApi = getCoreApi()
    if (action === 'onDisabled') {
        await coreApi.onTriggerDisabled(params)
        consoleLog("Integration trigger deactivated", {workflowId: workflow.id, integration, trigger})
    } else if (action === 'onEnabled') {
        await coreApi.onTriggerEnabled(params)
        consoleLog("Integration trigger activated", {workflowId: workflow.id, integration, trigger})
    }
}

export const DiscardDraftWorkflow = async (userId: string, workspaceId: string, id: string) => {
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (!workflow.draftDefinition) return workflow

    const update: Partial<Workflow> = {
        draftDefinition: null,
    }

    await s.update({id}, update)
    const nWorkflow: Workflow = {...workflow, ...update, updatedAt: new Date()}

    broadcastWorkflowUpdated(workspaceId, nWorkflow)

    return nWorkflow
}

export interface GetWorkflowInstancesParams extends PaginationParams {
    id?: string
}

export const GetWorkflowInstances = async (userId: string, workspaceId: string, id: string, params: GetWorkflowInstancesParams = {}) => {
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const s2 = new WorkflowInstanceService()
    const {limit, offset} = resolvePaginationParams(params)

    const qB = s2.getRepository()
        .createQueryBuilder("i")
        .where({workflowId: id})

    if (params.id) {
        qB.andWhere({id: params.id})
    }
    qB.limit(limit)
    qB.offset(offset)
    qB.orderBy({createdAt: "DESC"})
    return qB.getMany()
}

export interface CreateWorkflowInstanceData {
    data?: object
}

export const CreateWorkflowInstance = async (userId: string, workspaceId: string, id: string, requestData: CreateWorkflowInstanceData): Promise<WorkflowInstance> => {
    await verifyPermission(userId, workspaceId)
    return createWorkspaceInstanceNoAuth(workspaceId, id, requestData)
}

const callableTypes: string[] = [WorkflowTriggerType.OnDemand_Callable, WorkflowTriggerType.Schedule_OnSchedule]
export const createWorkspaceInstanceNoAuth = async (workspaceId: string, id: string, requestData: CreateWorkflowInstanceData): Promise<WorkflowInstance> => {
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id, status: WorkflowStatus.Published})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (!callableTypes.includes(workflow.triggerType)) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const definition = workflow.definition
    const triggerNode = definition.map[definition.triggerId]
    if (!triggerNode || !callableTypes.includes(triggerNode.data?.type)) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const runData: WorkflowRunData = {
        triggerNodeId: triggerNode.id, triggerOutput: requestData.data || {}, workflow: workflow
    }
    const {instances} = await StartWorkflowRuns([runData])
    return instances[0]
}

export interface DeleteWorkflowInstanceData {
    ids: string[]
}

export const DeleteWorkflowInstance = async (userId: string, workspaceId: string, id: string, requestData: DeleteWorkflowInstanceData) => {
    if (!requestData.ids || requestData.ids.length === 0) throw new RequiredParameterError("ids")
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const s2 = new WorkflowInstanceService()

    await s2.remove({id: In(requestData.ids), workflowId: id})
}

export interface UpdateWorkflowInstancesStatusData {
    ids: string[]
    status: WorkflowInstanceStatus.Active | WorkflowInstanceStatus.Cancelled | WorkflowInstanceStatus.Paused
}

export const UpdateWorkflowInstancesStatus = async (userId: string, workspaceId: string, id: string, requestData: UpdateWorkflowInstancesStatusData) => {
    if (!requestData.ids || requestData.ids.length === 0) throw new RequiredParameterError("ids")
    if (!requestData.status) throw new RequiredParameterError("status")
    if (![WorkflowInstanceStatus.Active, WorkflowInstanceStatus.Cancelled, WorkflowInstanceStatus.Paused].includes(requestData.status)) throw new InvalidParameterError("status")
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const ids = arrayDeDuplicate(requestData.ids)
    const instances = await new WorkflowInstanceService().find({workflowId: id, id: In(ids)})
    if (instances.length !== ids.length) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const s2 = new WorkflowInstanceService()

    await s2.update({id: In(ids), status: In([WorkflowInstanceStatus.Active, WorkflowInstanceStatus.Paused]), workflowId: id}, {status: requestData.status})

    if (requestData.status === WorkflowInstanceStatus.Active) {
        // mark the last failed task as scheduled, maybe??
        const s3 = new WorkflowTaskService()
        const tableName = s3.getRepository().metadata.tableName;
        const qB = s3.getRepository().createQueryBuilder('wT')
            .select()
            .where({instanceId: In(ids)})
            .andWhere(`wT.createdAt = (SELECT MAX(wT2.createdAt) FROM ${tableName} wT2 WHERE wT2.instanceId = wT.instanceId)`)

        const res = await qB.getMany()
        const taskIds = res.filter(t => t.status === WorkflowTaskStatus.Failed).map(r => r.id)
        if (taskIds.length > 0) {
            await s3.update({id: In(taskIds)}, {status: WorkflowTaskStatus.Scheduled, errorCount: 0})
            await s3.appendLogs(taskIds, `Task status reset to scheduled state by ${userId}`)
        }
        triggerWorkflowRunner(`Resumed ${taskIds.length} tasks in workflow: ${id}`)
    }
}

export interface ApproveWorkflowInstanceData {
    action: 'approve' | 'reject'
}

export const ApproveWorkflowInstance = async (userId: string, workspaceId: string, id: string, instanceId: string, data: ApproveWorkflowInstanceData) => {
    if (!data.action) throw new RequiredParameterError("action")
    if (!instanceId) throw new RequiredParameterError("instanceId")
    if (data.action !== 'approve' && data.action !== 'reject') throw new InvalidParameterError("action")

    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    await FinalizeWorkflowInstanceApproval(id, instanceId, data.action)
}

export const GetWorkflowInstanceTasks = async (userId: string, workspaceId: string, id: string, instanceId: string) => {
    const instances = await GetWorkflowInstances(userId, workspaceId, id, {id: instanceId})
    if (instances.length === 0) throw new NotfoundError(ErrorMessage.EntityNotFound)
    const s = new WorkflowTaskService()
    return await s.find({instanceId}, {createdAt: "ASC"})
}

export interface UpdateWorkflowDefinitionData {
    definition: IWorkflowDefinition
    id: string
}

export const UpdateWorkflowDefinition = async (userId: string, workspaceId: string, requestData: UpdateWorkflowDefinitionData) => {
    if (!requestData.id) throw new RequiredParameterError("id")
    if (!requestData.definition) throw new RequiredParameterError("definition")
    const id = requestData.id

    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const update: Partial<Workflow> = {draftDefinition: requestData.definition}
    await s.update({id}, update)
    const nWorkflow: Workflow = {...workflow, ...update, updatedAt: new Date()}

    broadcastWorkflowUpdated(workspaceId, nWorkflow)

    return nWorkflow
}

export interface TestWorkflowStepData {
    id: string
}

export const TestWorkflowStep = async (userId: string, workspaceId: string, id: string, requestData: TestWorkflowStepData) => {
    if (!requestData.id) throw new RequiredParameterError("id")
    await verifyPermission(userId, workspaceId)
    const s = new WorkflowService()
    const workflow = await s.findOne({workspaceId, id})
    if (!workflow) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const definition = workflow.draftDefinition || workflow.definition
    if (!definition) throw new NotfoundError(ErrorMessage.WorkflowHasNoStepsYet)
    const node = definition.map[requestData.id]
    if (!node) throw new NotfoundError(ErrorMessage.WorkflowStepNotFound)

    if (node.data.type === WorkflowActionType.HTTP_SendHTTPRequest) {
        const config = (node.data.configs || {}) as OpendashboardSendHTTPActionWorkflowNode_Configs
        if (!config.method) throw new RequiredParameterError("method")
        if (!config.url || !config.url.trim()) throw new RequiredParameterError("url")
        const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workflow.workspaceId)

        const headers = {}
        let url = substituteTags(config.url.trim(), vars, undefined, 'curly')
        if (config.headers) {
            for (let {key, value} of config.headers) {
                headers[key] = substituteTags((value || '').trim(), vars, undefined, 'curly')
            }
        }
        let rawBody = substituteTags((config.body || '').trim(), vars, (val): any => {
            if (typeof val !== "string") return val
            const jsonEncoded = JSON.stringify(val);

            // Check if first and last character are quotes before removing them
            if (jsonEncoded.startsWith('"') && jsonEncoded.endsWith('"')) {
                return jsonEncoded.slice(1, -1);
            }
            return jsonEncoded;
        }, 'curly').trim().replace(/[\n\r]/g, '')
        let httpData: object = {}
        if (rawBody) {
            try {
                httpData = JSON.parse(rawBody)
            } catch (e) {
                const error = `Send HTTP Request: Parsing request data failed with error ${e.message}`
                throw new BadRequestError(error)
            }
        }
        return await sendHTTPRequest(config.method, url, headers, httpData)
    } else if (node.data.type === WorkflowActionType.Opendashboard_GenerateAIContent) {
        const config = (node.data.configs || {}) as OpendashboardGenerateAIContentWorkflowNode_Configs
        const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workflow.workspaceId)

        let userPrompt = substituteTags(config.userPrompt.trim(), vars, undefined, 'curly')
        const {aiContentLog} = await generateAIContentAndLog(workspaceId, {userPrompt, systemPrompt: WORKFLOW_SYSTEM_PROMPT, provider: AIProvider.OpenAI, maxWords: config.maxWords, purpose: 'Test AI Content Generation in workflow'})

        return {content: aiContentLog.contentGenerated}
    } else {throw new NotfoundError(ErrorMessage.WorkflowStepTypeNotSupported)}

}

export const getTriggerObjectId = (type: 'database', id: string) => {
    if (type === 'database') {
        return `db:${id}`
    }
    return null
}

export const getTriggerType = (trigger: string, integration: string = '') => {
    const paths: string[] = []
    if (integration) paths.push(integration)
    if (trigger) paths.push(trigger)
    return paths.join(':')
}

const getWorkflowEmailTask = async (wTId: string, hash: string, salt: string) => {
    const s = new WorkflowTaskService()

    const qB = s.getRepository()
        .createQueryBuilder()
        .select('wT.id', 'taskId')
        .addSelect('wT.createdAt', 'createdAt')
        .addSelect("wT.instanceId", "instanceId")
        .addSelect("w.id", "workflowId")
        .leftJoin(WorkflowInstance, 'wI', 'wI.id = wT.instanceId')
        .leftJoin(Workflow, 'w', 'w.id = wI.workflowId')
        .where({id: wTId})
    const res = await qB.getRawOne()
    if (!res) return null
    const task = {
        id: res.taskId,
        instanceId: res.instanceId,
        workflowId: res.workflowId,
        createdAt: new Date(res.createdAt)
    }

    const tsNow = task.createdAt.getTime()

    // const tokenValue = `${salt}:${instanceId}:${id}:${tsNow}`
    const tokenValue = `${salt}:${task.instanceId}:${task.id}:${tsNow}`
    const urlHash = crypto.createHash('sha1').update(tokenValue).digest('hex')

    if (hash !== urlHash) {
        console.log({hash, urlHash})
        return null
    }

    return task
}

export const registerWorkflowEmailClick = async (wTId: string, hash: string, salt: string, url: string) => {
    const task = await getWorkflowEmailTask(wTId, hash, salt)
    if (!task) return false

    const lS = new LinkService()
    const cAS = new CampaignAnalyticService()
    const link = await lS.getLink(url)

    const data: CampaignAnalyticsData = {
        workflowId: task.workflowId, clickCount: 1, emailId: task.id, linkId: link.id, openCount: 0
    }
    await cAS.addAnalytics(data)

    return true

}

export const registerWorkflowEmailOpen = async (wTId: string, hash: string, salt: string) => {
    const task = await getWorkflowEmailTask(wTId, hash, salt)
    if (!task) return false

    const cAS = new CampaignAnalyticService()
    const data: CampaignAnalyticsData = {
        workflowId: task.workflowId, clickCount: 0, emailId: task.id, linkId: 0, openCount: 1
    }
    await cAS.addAnalytics(data)
    return true
}

export const registerWorkflowEmailUnsubscribe = async (wTId: string, hash: string, salt: string) => {
    const task = await getWorkflowEmailTask(wTId, hash, salt)
    if (!task) return false

    consoleLog("Workflow Unsubscribe link clicked:", {wTId, hash, salt, task})

    return true
}




