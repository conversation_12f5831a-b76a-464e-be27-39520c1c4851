import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";
import {WorkspaceUsage} from "../entity/WorkspaceUsage";


interface AddWorkspaceUsageData extends Pick<WorkspaceUsage,
    'workspaceId' | 'hour' | 'day' | 'month' |
    'year' | 'aiGeneration' | 'enrichment' | 'emailSent' |'workflowTask' | 'creditBilledInCents'> {
}

export class WorkspaceUsageService extends BaseService<WorkspaceUsage> {

    initRepository = (): Repository<WorkspaceUsage> => {
        return getRepository(WorkspaceUsage);
    }

    async addUsage(data: AddWorkspaceUsageData) {
        const repo = this.getRepository()
        const qB = repo.createQueryBuilder()
            .insert()
            .into(WorkspaceUsage)
            .values(data)

        let [query, parameters] = qB.getQueryAndParameters()
        const updateList = []

        updateList.push("aiGeneration=aiGeneration+VALUES(aiGeneration)")
        updateList.push("enrichment=enrichment+VALUES(enrichment)")
        updateList.push("emailSent=emailSent+VALUES(emailSent)")
        updateList.push("workflowTask=emailSent+VALUES(workflowTask)")
        updateList.push("creditBilledInCents=creditBilledInCents+VALUES(creditBilledInCents)")

        const orUpdateQuery = updateList.join(", ");

        if (orUpdateQuery) {
            query += ` ON DUPLICATE KEY UPDATE ${orUpdateQuery}`;
        }
        await repo.manager.query(query, parameters);
8
        return true
    }

}
