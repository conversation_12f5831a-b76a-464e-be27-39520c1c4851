import config from "../config"
import * as Mail from "nodemailer/lib/mailer";
import * as nodemailer from "nodemailer";
import {getTemplateContent} from "../utility/template";
import {handleError} from "../config/error";

export interface EmailUser {
    email: string
    name: string
}

export interface SMTPEmailUser {
    address: string
    name: string
}

export interface EmailButton {
    label: string
    url: string
}

const defaultFrom: SMTPEmailUser = {
    address: config.EMAIL.email,
    name: config.EMAIL.name,
}

const emailTransport = nodemailer.createTransport({
    host: config.EMAIL.host,
    port: Number(config.EMAIL.port),
    secure: config.EMAIL.port === "465", // true for 465, false for other ports
    auth: {
        user: config.EMAIL.username,
        pass: config.EMAIL.password,
    },
    tls: {
        minVersion: 'TLSv1',
    }
});

let doNotSendMail = false;

export const setDoNotSendEmail = (val: boolean) => {
    doNotSendMail = val;
}

export const getEmailWithContentBody = (
    body: string,
    button: EmailButton = null,
    footer: string = null,
) => {
    footer = footer || `
	<p>
If you have any questions, please let us know by replying this email or sending us a <NAME_EMAIL>.
</p>
<p>Cheers,</p>
<p><span style="font-weight: 600;">The Opendashboard Team.</span></p>
	`
    const buttonHTML = button ? `
<p style="padding: 15px 0">
<a href="${button.url}" 
style="display:inline-block;background:#18181b;color:#ffffff;font-weight:600;margin:0;text-decoration:none;text-transform:none;padding:10px 25px;mso-padding-alt:0px;border-radius:40px;" 
       target="_blank"> ${button.label} </a></p>
	` : ''
    body = `
	<div>${body}</div>
	<div>${buttonHTML}</div>
	<div>${footer}</div>
	`
    const data = {
        content: body
    }
    const template = 'email/base.html';

    return getTemplateContent(template, data)
}

export const SendEmailWithContent = async (
    to: EmailUser,
    subject: string,
    body: string,
    button: EmailButton = null,
    footer: string = null,
    IsTransactional = false,
    from: SMTPEmailUser = undefined, transport = undefined, replyTo: EmailUser = undefined,
    attachments: string[] = [], bubbleException = false, cc: string[] = [], bcc: string[] = []
) => {
    const content = getEmailWithContentBody(body, button, footer)

    return await SendEmail(
        to,
        subject,
        content,
        IsTransactional,
        from,
        transport,
        replyTo,
        attachments,
        bubbleException,
        cc,
        bcc
    )
}


export const SendEmail = async (
    emailTo: EmailUser, subject: string,
    body: string,
    IsTransactional = false,
    from: SMTPEmailUser = undefined, transport = undefined, replyTo: EmailUser = undefined,
    attachments: string[] = [], bubbleException = false, cc: string[] = [], bcc: string[] = []
): Promise<string> => {

    const to: SMTPEmailUser = {
        address: emailTo.email,
        name: emailTo.name
    }

    let info
    let data: Mail.Options = {
        from: from ? from : defaultFrom,
        to,
        subject: subject,
        html: body,
        headers: {
            IsTransactional: String(IsTransactional),
        },
        cc,
        bcc
    }
    if (replyTo) {
        data['replyTo'] = replyTo.email 
    }
    if (attachments && attachments.length > 0) {
        data['attachments'] = attachments.map(a => {
            return {path: a}
        })
    }
    if (doNotSendMail) {
        return '1234-message-id'
    }
    try {

        if (transport) {
            info = await transport.sendMail(data);
        } else {
            // send mail with defined transport object
            info = await emailTransport.sendMail(data);
        }
        return info.messageId
    } catch (e) {
        handleError('Failed to send email', {
            exception: e,
            response: e.response?.data,
            data,
            withCustomTransport: transport ? 'Yes' : 'No'
        })
        // Sentry.captureException('Failed to send email', {
        //     extra: {
        //         exception: e,
        //         response: e.response?.data,
        //         data,
        //         withCustomTransport: transport ? 'Yes' : 'No'
        //     }
        // })
        if (bubbleException) {
            throw e
        }
        return ''
    }
}