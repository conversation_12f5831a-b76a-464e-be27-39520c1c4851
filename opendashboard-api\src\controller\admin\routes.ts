import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {AdminController} from "./controller";

export const adminRoutes: Routes = {
    basePath: '/admin',
    middleware: [verifyJWT],
    routes: {
        "/": {
            get: {controller: Admin<PERSON>ontroller, action: "getMember"},
        },
        "/templates/submitted": {
            get: {controller: AdminController, action: "getSubmittedTemplates"},
        },
        "/templates/submitted/:id": {
            post: {controller: AdminController, action: "processSubmittedTemplates"},
        },
        "/templates/top-picks": {
            post: {controller: AdminController, action: "addTopPick"},
            delete: {controller: AdminController, action: "removeTopPick"},
        },
        "/affiliates": {
            get: {controller: AdminController, action: "getAffiliates"},
        },
        "/affiliates/approve": {
            post: {controller: Admin<PERSON>ontroller, action: "approveAffiliate"},
        },
        "/payouts": {
            get: {controller: <PERSON><PERSON><PERSON><PERSON>roller, action: "getPayouts"},
        },
    }
}
