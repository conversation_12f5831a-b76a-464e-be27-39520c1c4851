import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {DatabaseController} from "./controller";

export const databaseRoutes: Routes = {
    basePath: '/workspaces/:id/databases',
    middleware: [verifyJWT],
    routes: {
        '/': {
            get: {controller: DatabaseController, action: "getDatabases"},
            post: {controller: DatabaseController, action: "createDatabase"},
        },
        // '/:databaseId': {
        //     get: {controller: DatabaseController, action: "getDatabase"},
        // },
        // '/:databaseId/records': {
        //     get: {controller: DatabaseController, action: "getRecords"},
        //     post: {controller: DatabaseController, action: "addRecords"},
        //     patch: {controller: DatabaseController, action: "updateRecord"},
        //     delete: {controller: DatabaseController, action: "deleteRecords"},
        // },
        '/:databaseId/match-records': {
            post: {controller: DatabaseController, action: "matchRecords"},
        },
        '/:databaseId/activate-messaging': {
            post: {controller: DatabaseController, action: "activateMessaging"},
        },
        '/:databaseId/records/:recordId/cover-image': {
            patch: {controller: DatabaseController, action: "uploadRecordCoverImage"}
        },
        '/:databaseId/records/:recordId/image': {
            patch: {controller: DatabaseController, action: "uploadRecordProfileImage"}
        },
    }
}