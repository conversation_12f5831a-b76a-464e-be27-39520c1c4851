import {<PERSON>um<PERSON>, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

export interface NotificationChannelSetting {
    isActive: boolean
    notifyOn: {
        comment: boolean
        replies: boolean
        assignedRecord: boolean
        dueTask: boolean
    }
}

export enum SettingsType {
    Notification = "notification",
}

export interface NotificationSettings {
    email: NotificationChannelSetting

    [key: string]: NotificationChannelSetting
}

@Entity()
export class Settings {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: 'varchar', nullable: false, unique: true})
    userId: string

    @Column({type: 'json', nullable: true})
    notification: NotificationSettings

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

}