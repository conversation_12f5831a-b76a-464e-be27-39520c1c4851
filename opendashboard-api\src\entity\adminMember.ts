import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"

export enum AdminMemberRole {
    Owner = "owner",
    Admin = "admin",
    Member = "member",
    TemplateApproval = "templateApproval",
}


@Entity()
export class AdminMember {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index({unique: true})
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Column({type: 'varchar', nullable: false})
    role: AdminMemberRole

    @Column({type: 'varchar', nullable: false})
    invitedById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

