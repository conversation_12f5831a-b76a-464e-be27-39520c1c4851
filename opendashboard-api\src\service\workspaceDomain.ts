import {getRepository} from '../connection/db';
import {BaseService} from './service';
import {Repository} from 'typeorm/repository/Repository';
import {WorkspaceDomain} from "../entity/WorkspaceDomain";

export class WorkspaceDomainService extends BaseService<WorkspaceDomain> {

    initRepository = (): Repository<WorkspaceDomain> => {
        return getRepository(WorkspaceDomain);
    }

    // addDomain = async (data: Pick<WorkspaceDomain, 'domain' | 'addedByUserId' | 'workspaceId' | 'configs'>) => {
    //     const repo = this.getRepository()
    //     let [query, parameters] = repo.createQueryBuilder()
    //         .insert()
    //         .values(data)
    //         .getQueryAndParameters();
    //     query += ` ON DUPLICATE KEY UPDATE domain = VALUES(domain)`;
    //     await repo.manager.query(query, parameters);
    //
    //     return data as WorkspaceDomain;
    // }


}






