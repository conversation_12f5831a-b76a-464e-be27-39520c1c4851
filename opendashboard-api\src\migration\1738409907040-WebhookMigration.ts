import { MigrationInterface, QueryRunner } from "typeorm";

export class WebhookMigration1738409907040 implements MigrationInterface {
    name = 'WebhookMigration1738409907040'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`webhook\` (\`id\` varchar(36) NOT NULL, \`workflowId\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`method\` varchar(255) NOT NULL, \`headers\` json NULL, \`body\` json NULL, \`query\` json NULL, \`isTest\` tinyint NOT NULL DEFAULT 0, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`meta\` json NULL, INDEX \`IDX_d9c007868e81ef3d0cb6f2456e\` (\`workflowId\`), INDEX \`IDX_597ab5e7de76f1836b8fd80d6b\` (\`workspaceId\`), INDEX \`IDX_c7be8a53038b49e1374158a09e\` (\`method\`), INDEX \`IDX_20a959ea613badefb5c23e3219\` (\`isTest\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`DROP INDEX \`IDX_d951d3017f189b909627b54beb\` ON \`workspace_secret\``);
        await queryRunner.query(`ALTER TABLE \`workspace_secret\` CHANGE \`type\` \`type\` varchar(255) NOT NULL DEFAULT 'secret'`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_d951d3017f189b909627b54beb\` ON \`workspace_secret\` (\`workspaceId\`, \`type\`, \`name\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_d951d3017f189b909627b54beb\` ON \`workspace_secret\``);
        await queryRunner.query(`ALTER TABLE \`workspace_secret\` CHANGE \`type\` \`type\` varchar(255) NOT NULL DEFAULT 'active'`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_d951d3017f189b909627b54beb\` ON \`workspace_secret\` (\`workspaceId\`, \`type\`, \`name\`)`);
        await queryRunner.query(`DROP INDEX \`IDX_20a959ea613badefb5c23e3219\` ON \`webhook\``);
        await queryRunner.query(`DROP INDEX \`IDX_c7be8a53038b49e1374158a09e\` ON \`webhook\``);
        await queryRunner.query(`DROP INDEX \`IDX_597ab5e7de76f1836b8fd80d6b\` ON \`webhook\``);
        await queryRunner.query(`DROP INDEX \`IDX_d9c007868e81ef3d0cb6f2456e\` ON \`webhook\``);
        await queryRunner.query(`DROP TABLE \`webhook\``);
    }

}
