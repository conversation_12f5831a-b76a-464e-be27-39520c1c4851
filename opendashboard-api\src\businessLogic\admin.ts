import {TemplateSubmission, TemplateSubmissionResult} from "../entity/templateSubmission";
import {inclusivePick} from "../utility/object";
import {ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, UnauthorizedError} from "../errors/AppError";
import {AdminMemberService} from "../service/adminMember";
import {TemplateService} from "../service/template";
import {TemplateSubmissionService} from "../service/templateSubmission";
import {TemplateListingService} from "../service/templateListing";
import {TemplateReleaseService} from "../service/templateRelease";
import {IsNull} from "typeorm";
import {CreatorService} from "../service/creator";
import {GetCreatorMembersNoAuth} from "./creator";
import {CreatorMemberRole} from "../entity/creatorMember";
import {EmailUser, SendEmailWithContent} from "./email";
import {appUrl, isProduction, isStage} from "../config";
import {Template} from "../entity/template";
import {TemplateTopPickService} from "../service/templateTopPick";
import {TemplateListing} from "../entity/templateListing";
import {Creator} from "../entity/creator";
import {TemplateInstall} from "../entity/templateInstall";
import {TemplateReview} from "../entity/templateReview";
import {PaginationParams, resolvePaginationParams} from "./document";
import {AffiliateService} from "../service/affiliate";
import {Affiliate} from "../entity/affiliate";
import {User} from "../entity/User";
import {AffiliateStats, GetAffiliateStatsForIds, GetApprovedAffiliate} from "./affiliate";
import {PayoutService} from "../service/payout";
import {PayoutProcessor, PayoutStatus} from "../entity/payout";
import {UserService} from "../service/user";

const userId = isStage() ? 'd8255071-f98a-49eb-8bdd-6a78812b775f' : '40ef2c56-9b5c-48d2-abcc-25172814c941'
export const AutoApproveNonProdTemplateSubmission = async (submissionId: number) => {
    if (isProduction()) return
    // at random, maybe once in every three times, reject the template
    const random = Math.random()
    if (random < 0.33) {
        await ApproveTemplateSentForReview(userId, submissionId, {
            reviewResult: TemplateSubmissionResult.Rejected, reviewInternalNote: 'auto rejected',
            reviewNote: `auto-rejected, it's intentional, just try again`
        })
    } else {
        await ApproveTemplateSentForReview(userId, submissionId, {reviewResult: TemplateSubmissionResult.Approved, reviewInternalNote: 'auto approved', reviewNote: 'auto-approved'})
    }
}

export interface ApproveTemplateSentForReviewData {
    reviewNote: string,
    reviewInternalNote: string,
    reviewResult: TemplateSubmissionResult
}

export const ApproveTemplateSentForReview = async (userId: string, submissionId: number, rawData: ApproveTemplateSentForReviewData) => {
    const data = inclusivePick<ApproveTemplateSentForReviewData>(rawData)
    if (!data.reviewNote) throw new RequiredParameterError("reviewNote")
    if (!data.reviewInternalNote) throw new RequiredParameterError("internalReviewNote")
    if (!data.reviewResult) throw new RequiredParameterError("reviewResult")
    if (data.reviewResult !== TemplateSubmissionResult.Approved && data.reviewResult !== TemplateSubmissionResult.Rejected) throw new InvalidParameterError(`Status must either be ${TemplateSubmissionResult.Approved} or ${TemplateSubmissionResult.Rejected}`)

    const s = new AdminMemberService()
    const member = await s.findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const tS = new TemplateService()
    const tSS = new TemplateSubmissionService()
    const tLS = new TemplateListingService()
    const tRS = new TemplateReleaseService()
    let submission = await tSS.findOne({id: submissionId, reviewResult: IsNull()})
    if (!submission) throw new NotfoundError(ErrorMessage.EntityNotFound)

    let template = await tS.findOne({id: submission.templateId})
    const listing = await tLS.findOne({id: submission.listingId})

    const creator = await new CreatorService().findOne({id: submission.creatorId})
    const members = await GetCreatorMembersNoAuth(submission.creatorId)
    const owner = members.find(m => m.creatorMember.role === CreatorMemberRole.Owner)

    const to: EmailUser = {
        email: owner.user.email,
        name: `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim()
    }
    const cc = members
        .filter(m => m.creatorMember.role !== CreatorMemberRole.Owner)
        .map(m => m.user.email)

    const templateLink = appUrl(`/creators/${creator.domain}/templates/${listing.templateId}`)


    let messageId = ''
    if (data.reviewResult === TemplateSubmissionResult.Rejected) {
        const update: Partial<TemplateSubmission> = {...data, reviewedBy: userId, reviewedAt: new Date()}
        await tSS.update({id: submissionId}, update)
        submission = {...submission, ...update}

        // send email to person about

        const subject = `Template Submission for ${listing.name} rejected`
        const message = `
        Hello there, <br/> You template submission for <a href="${templateLink}"><strong>${listing.name}</strong></a> was rejected due to "${data.reviewNote}".<br/>
        Please fix the issue and resubmit at a later time.
        `
        const button = {
            label: 'View Template →',
            url: templateLink
        }
        messageId = await SendEmailWithContent(to, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, cc)
    } else {
        const update: Partial<TemplateSubmission> = {...data, reviewedBy: userId, reviewedAt: new Date()}
        await tSS.update({id: submissionId}, update)
        submission = {...submission, ...update}

        // update listing and release that are active to replaced at
        await tLS.update({isFeaturedInMarketplace: true}, {isFeaturedInMarketplace: false, replacedAt: new Date()})
        await tRS.update({isFeaturedInMarketplace: true}, {isFeaturedInMarketplace: false, replacedAt: new Date()})

        // update the listing adn release of th sub
        // update the template
        // update the template submission
        await tLS.update({id: submission.listingId}, {isFeaturedInMarketplace: true, featuredAt: new Date()})
        await tRS.update({id: submission.releaseId}, {isFeaturedInMarketplace: true, featuredAt: new Date()})

        let tUpdate: Partial<Template> = {
            isListedInMarketplace: true,
            marketplaceListingId: submission.listingId,
            marketplaceReleaseId: submission.releaseId,
            listingUpdatedAt: new Date()
        }
        if (!template.listedAt) {
            tUpdate.listedAt = new Date()
        }
        await tS.update({id: submission.templateId}, tUpdate)
        template = {...template, ...tUpdate}

        const subject = `Template Submission for ${listing.name} approved`
        const message = `
        Hello there, <br/> You template submission for <a href="${templateLink}"><strong>${listing.name}</strong></a> has been approved.<br/>
        `
        const button = {
            label: 'View Template →',
            url: templateLink
        }
        messageId = await SendEmailWithContent(to, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, cc)
    }
    return {
        submission, messageId, template, listing
    }
}

export interface AddAsTopPickData {
    templateId: string
}

export const AddAsTopPick = async (userId: string, {templateId}: AddAsTopPickData) => {
    const s = new AdminMemberService()
    const member = await s.findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const tS = new TemplateService()
    const template = await tS.findOne({id: templateId, isListedInMarketplace: true})
    if (!template) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const tPS = new TemplateTopPickService()
    const topPick = await tPS.insert({
        templateId, addedById: userId
    })
    return {topPick}
}

export const RemoveTopPick = async (userId: string, {templateId}: AddAsTopPickData) => {
    const s = new AdminMemberService()
    const member = await s.findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const tPS = new TemplateTopPickService()
    await tPS.remove({
        templateId
    })

}

export interface GetTemplatesForReviewParams {
    query?: string
    page?: number
    perPage?: number
    submissionId?: number
    creatorId?: string
}

export interface SubmittedMarketplaceTemplate {
    template: Template
    templateListing: TemplateListing
    creator: Creator
    submission: TemplateSubmission
    stats: {
        installs: number
        rating: number
    }
}

export const GetTemplatesForReview = async (userId: string, params?: GetTemplatesForReviewParams) => {
    if (!params) params = {}

    if (!params.perPage || Number.isNaN(Number(params.perPage))) params.perPage = 20
    if (!params.page || Number.isNaN(Number(params.page))) params.page = 1

    const member = await new AdminMemberService().findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    let offset = 0
    if (!params.perPage) params.perPage = 12
    if (params.page > 1) {
        offset = (params.page - 1) * params.perPage
    }

    const s = new TemplateSubmissionService()
    const qB = s.getRepository()
        .createQueryBuilder("tS")
        .select()
        .addSelect("t")
        .addSelect("c")
        .addSelect("tL")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplateInstall, "tI")
                .where("tI.templateId=tS.templateId")
        }, "s_installs")
        .addSelect(qb => {
            return qb.select("AVG(rating)")
                .from(TemplateReview, "tR")
                .where("tR.templateId=tS.templateId AND tR.rating IS NOT NULL")
        }, "s_rating")
        .leftJoin(Template, 't', 't.id=tS.templateId')
        .leftJoin(Creator, 'c', 'c.id=tS.creatorId')
        .leftJoin(TemplateListing, 'tL', 'tL.id=tS.listingId')
        .where({reviewResult: IsNull()})
        .orderBy("tS.id", 'ASC')

    if (params.creatorId) {
        qB.andWhere("tS.creatorId=:creatorId", {creatorId: params.creatorId})
    }
    if (params.submissionId) {
        qB.andWhere("tS.id=:submissionId", {submissionId: params.submissionId})
    }
    if (params.query && !!params.query.trim()) {
        // qB.andWhere("tS.id=:submissionId", {submissionId: params.submissionId})
        qB.andWhere("tL.name LIKE :query", {query: `%%${params.query.trim()}%`})
    }
    if (offset > 0) qB.offset(offset)

    const rawResults = await qB
        .getRawMany()

    if (rawResults && Array.isArray(rawResults) && rawResults.length > 0) {
        return rawResults.map(r => {
            const row: SubmittedMarketplaceTemplate = {
                creator: {} as Creator, stats: {installs: 0, rating: 0}, template: {} as Template, templateListing: {} as TemplateListing, submission: {} as TemplateSubmission
            }
            for (let key of Object.keys(r)) {
                const [pre, field] = key.split("_")
                if (pre === "t") {
                    row.template[field] = r[key]
                } else if (pre === "c") {
                    row.creator[field] = r[key]
                } else if (pre === "s") {
                    row.stats[field] = Number(r[key])
                } else if (pre === "tS") {
                    row.submission[field] = Number(r[key])
                } else if (pre === "tL") {
                    row.templateListing[field] = r[key]
                } else if (pre === "t") {
                    row.template[field] = r[key]
                }
            }
            return row
        })
    }

    return []

}

export interface GetAffiliatesParams extends PaginationParams {
    isApproved?: boolean | string
}

export interface AffiliateData {
    affiliate: Affiliate
    user: User
    stats: AffiliateStats
}

export const GetAffiliates = async (userId: string, params: GetAffiliatesParams = {}) => {
    const member = await new AdminMemberService().findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const {query, limit, offset} = resolvePaginationParams(params)

    const s = new AffiliateService()
    const qB = s.getRepository().createQueryBuilder("a")
        .addSelect('u', 'u.id=a.userId')
        .leftJoin(User, 'u', 'u.id=a.userId')
        .where({isApproved: params.isApproved === 'true'})

    if (query) {
        qB.andWhere("email LIKE :query OR referralCode LIKE :query OR u.firstName LIKE :query OR u.lastName LIKE :query", {query: `%${query}%`})
    }
    qB.limit(limit)
    qB.offset(offset)

    const rawResults = await qB.getRawMany()
    const affiliates: AffiliateData[] = []
    for (let r of rawResults) {
        const affiliate: AffiliateData = {
            affiliate: {} as Affiliate,
            user: {} as User,
            stats: {} as AffiliateStats
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "a") {
                affiliate.affiliate[field] = r[key]
            } else if (pre === "u") {
                affiliate.user[field] = r[key]
            }
        }
        affiliates.push(affiliate)
    }
    const stats = await GetAffiliateStatsForIds(affiliates.map(a => a.affiliate.id))
    affiliates.forEach(a => {
        a.stats = stats.find(s => s.affiliateId === a.affiliate.id) || {} as AffiliateStats
    })

    return {affiliates}

}

export interface ApproveAffiliateData {
    id: string
    isApproved: boolean
}

export const ApproveAffiliate = async (userId: string, data: ApproveAffiliateData) => {
    let {id, isApproved} = data
    isApproved = !!isApproved
    if (!id) throw new RequiredParameterError("id")
    const member = await new AdminMemberService().findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const s = new AffiliateService()
    const affiliate = await s.findOne({id})
    if (!affiliate) throw new NotfoundError(ErrorMessage.EntityNotFound)

    if (affiliate.isApproved === !!isApproved) {
        return {affiliate}
    }

    let update: Partial<Affiliate> = {}
    if (isApproved) {
        update = {isApproved: true, approvedAt: new Date(), approvedByUserId: userId}
    } else {
        update = {isApproved: false}
    }
    await s.update({id}, update)

    await s.appendLog(id, `isApproved changed to ${String(isApproved)} by user:${userId}`)

    if (isApproved) {
        await sendApprovalNotification({...affiliate, ...update})
    }

    return {affiliate: {...affiliate, ...update},}
}

const sendApprovalNotification = async (affiliate: Affiliate) => {
    if (!affiliate.isApproved) return

    const user = await new UserService().findById(affiliate.userId)

    const to: EmailUser = {
        email: user.email,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
    }
    const ctaLink = appUrl(`/welcome/referral`)

    const subject = `Affiliate Account Approved | Opendashboard`
    const message = `
        Hello there, <br/> Your affiliate account has been approved.<br/>
        You can now earn money promoting Opendashboard services.<br/>
        Thank you for joining us!
        `
    const button = {
        label: 'View Affiliate Dashboard →',
        url: ctaLink
    }
    const messageId = await SendEmailWithContent(to, subject, message, button, null, true)

}

export interface GetPayoutsParams extends PaginationParams {
    affiliateId?: string
    creatorId?: string
    payoutProviderReference?: string
    payoutStatus?: PayoutStatus
    payoutProvider?: PayoutProcessor
}

export const GetPayouts = async (userId: string, params: GetPayoutsParams = {}) => {
    const member = await new AdminMemberService().findOne({userId})
    if (!member) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const {affiliate} = await GetApprovedAffiliate(userId)
    const {offset, limit} = resolvePaginationParams(params)

    const s = new PayoutService()
    const qB = s.getRepository().createQueryBuilder("p")
        .limit(limit)
        .offset(offset)

    if (params.affiliateId) qB.andWhere({affiliateId: params.affiliateId})
    if (params.creatorId) qB.andWhere({creatorId: params.creatorId})
    if (params.payoutProviderReference) qB.andWhere({payoutProviderReference: params.payoutProviderReference})
    if (params.payoutStatus) qB.andWhere({payoutStatus: params.payoutStatus})
    if (params.payoutProvider) qB.andWhere({payoutProvider: params.payoutProvider})

    const payouts = await new PayoutService().find({affiliateId: affiliate.id}, {createdAt: 'DESC'}, limit, offset)
    return {payouts}
}







