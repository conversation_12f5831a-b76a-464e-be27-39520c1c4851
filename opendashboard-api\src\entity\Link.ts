import { Entity, PrimaryGeneratedColumn, Column, Index, CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';

@Entity()
export class Link {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', nullable: false, unique: true })
    linkUrl: string;

    @Index()
    @CreateDateColumn({ type: 'timestamp' })
    createdAt: Date;

    @Index()
    @UpdateDateColumn({ type: 'timestamp' })
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deletedAt: Date;
}
