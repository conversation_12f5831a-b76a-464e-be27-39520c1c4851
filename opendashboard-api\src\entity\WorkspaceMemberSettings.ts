import {Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";


export interface KeyValueStore {
    [key: string]: object;
}

@Entity()
@Index(["workspaceId", "userId"], {unique: true})
export class WorkspaceMemberSettings {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: 'varchar', nullable: false})
    userId: string

    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Column({type: 'json', nullable: true})
    settings: KeyValueStore

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

}