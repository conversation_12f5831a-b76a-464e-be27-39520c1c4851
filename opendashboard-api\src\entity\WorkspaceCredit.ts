import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm";
import {PaymentCurrency, PaymentProcessor, PurchaseStatus} from "./templatePurchase";
import {KeyValueStore} from "./WorkspaceMemberSettings";

@Entity()
export class WorkspaceCredit {

    @PrimaryGeneratedColumn()
    id: number

    @Index()
    @Column({type: "varchar", nullable: false})
    workspaceId: string

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @Index()
    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: "timestamp", nullable: true})
    purchasedAt: Date

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    costInCents: number

    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    amountPaidInCents: number

    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    amountInLocalCurrency: number

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    earningsInCents: number

    @Index()
    @Column({type: "varchar", default: PaymentCurrency.USD})
    currency: PaymentCurrency

    @Index()
    @Column({type: 'varchar', nullable: true})
    paymentProcessorReference: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    paymentProcessor: PaymentProcessor

    @Index()
    @Column({type: 'varchar', nullable: true})
    status: PurchaseStatus

    @Column({type: "decimal", default: 0})
    creditAmountInCents: number

    @Index()
    @Column({type: "decimal", default: 0})
    creditRemainingInCents: number

    @Index()
    @Column({type: "timestamp", nullable: true})
    validFrom: Date

    @Index()
    @Column({type: "timestamp", nullable: true})
    expiresAt: Date

    @Column({type: "varchar", nullable: true})
    stripeInvoiceId: string

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}