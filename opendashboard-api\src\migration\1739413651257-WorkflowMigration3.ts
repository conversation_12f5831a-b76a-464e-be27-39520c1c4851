import { MigrationInterface, QueryRunner } from "typeorm";

export class WorkflowMigration31739413651257 implements MigrationInterface {
    name = 'WorkflowMigration31739413651257'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workflow_task\` DROP COLUMN \`taskInput\``);
        await queryRunner.query(`ALTER TABLE \`workflow_instance\` ADD \`auditLog\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` ADD \`errorCount\` int NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` ADD \`shouldRetry\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` ADD \`auditLog\` json NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_f5181cf32304848d54109cc141\` ON \`workflow_task\` (\`errorCount\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_d3fab564be3e001d1d7eca1e9d\` ON \`workflow_task\` (\`shouldRetry\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_d3fab564be3e001d1d7eca1e9d\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_f5181cf32304848d54109cc141\` ON \`workflow_task\``);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` DROP COLUMN \`auditLog\``);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` DROP COLUMN \`shouldRetry\``);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` DROP COLUMN \`errorCount\``);
        await queryRunner.query(`ALTER TABLE \`workflow_instance\` DROP COLUMN \`auditLog\``);
        await queryRunner.query(`ALTER TABLE \`workflow_task\` ADD \`taskInput\` json NULL`);
    }

}
