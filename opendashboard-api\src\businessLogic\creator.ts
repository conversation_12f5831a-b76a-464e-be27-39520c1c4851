import {BadRequestError, <PERSON>rror<PERSON>essage, InvalidParameterError, NotfoundError, RequiredParameterError, UnauthorizedError, UniqueConstraintError} from "../errors/AppError";
import {WorkspaceMemberService} from "../service/workspaceMember";
import {WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {CreatorService} from "../service/creator";
import {WorkspaceService} from "../service/workspace";
import {CreatorMemberService} from "../service/creatorMember";
import {CreatorMember, CreatorMemberRole} from "../entity/creatorMember";
import {Creator} from "../entity/creator";
import {FileType, ProcessFormToSpaceUpload} from "./doUpload";
import {User} from "../entity/User";
import {arrayDeDuplicate, arrayDiff, generateUUID} from "opendb-app-db-utils/lib";
import {validateEmail} from "../utility/validator";
import {CreateUser, UserService} from "../service/user";
import {In} from "typeorm";
import {apiUrl, appUrl, isProduction} from "../config";
import {EmailUser, SendEmailWithContent} from "./email";
import {createAccountLink, createConnectAccount, createLoginLink} from "./stripe";
import {inclusivePick} from "../utility/object";
import {WorkspaceUpload} from "../entity/WorkspaceUpload";
import {GetProfile} from "./account";
import {WorkspaceUploadService} from "../service/workspaceUpload";
import {EarningStats, GetTemplateEarningStats, GetTemplateInstallStats, GetTemplateReviewStats, InstallStats, ReviewStats} from "./templates";
import {Request} from "express"
import {PaginationParams, resolvePaginationParams} from "./document";
import {PayoutService} from "../service/payout";
import {Discount} from "../entity/discount";
import {DiscountService} from "../service/discount";
import {AffiliateService} from "../service/affiliate";


export interface SetupCreatorData {
    workspaceId: string
    name: string
}

const MAX_MEMBERS = 5

export const SetupCreator = async (userId: string, data: SetupCreatorData) => {
    const {workspaceId, name} = data
    if (!name) {
        throw new RequiredParameterError('Name')
    }
    if (name.toLowerCase() === 'all') {
        throw new InvalidParameterError(ErrorMessage.AllIsReservedCannotBeName)
    }

    const wMS = new WorkspaceMemberService()
    const wS = new WorkspaceService()
    const cS = new CreatorService()
    const cMS = new CreatorMemberService()

    const workspace = await wS.findOne({id: workspaceId})
    if (!workspace) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (name.toLowerCase().includes('opendashboard') && !name.toLowerCase().includes('opendashboard')) {
        throw new InvalidParameterError(ErrorMessage.NameCannotContainOpendashboard)
    }
    const member = await wMS.findOne({workspaceId, userId, role: WorkspaceMemberRole.Owner})
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const existingCreator = await cS.findOne({workspaceId})
    if (existingCreator) {
        throw new BadRequestError(ErrorMessage.WorkspaceAssociatedWithAnotherCreator)
    }
    const creator = await cS.insert({
        workspaceId,
        createdById: userId,
        ownerId: userId,
        domain: workspace.domain,
        name: (name || '').trim() || workspace.name,
    })
    const creatorMember = await cMS.insert({
        role: CreatorMemberRole.Owner,
        creatorId: creator.id,
        userId: userId,
        invitedById: userId,
    })

    return {
        creator, creatorMember
    }
}

export const UpdateCreatorPhoto = async (userId: string, creatorId: string, request: Request, type: 'cover' | 'profile'): Promise<MyCreator> => {
    if (type !== 'cover' && type !== 'profile') {
        throw new InvalidParameterError('Type is invalid');
    }

    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const date = new Date(myCreator.creator.createdAt)

    const month = date.getMonth() + 1
    const year = date.getFullYear()
    const day = date.getDate()

    const result = await ProcessFormToSpaceUpload(request, `creators/${year}/${month}/${day}/${userId}`, `${type}.png`, FileType.Image)

    const update: Partial<Creator> = {
        // profilePhoto: `${result.location}?t=${new Date().getTime()}`
    }
    if (type === 'cover') {
        update.coverImage = `${result.location}?t=${new Date().getTime()}`
    } else {
        update.logo = `${result.location}?t=${new Date().getTime()}`
    }
    const cS = new CreatorService();
    await cS.update({id: creatorId}, update)

    myCreator.creator = {...myCreator.creator, ...update}

    return myCreator
}

export interface UpdateCreatorData extends Pick<Creator, 'name' | 'canBeEmailed' | 'contactEmail' | 'shortDescription' | 'links' | 'timezone'> {

}

export const UpdateCreator = async (userId: string, creatorId: string, rezData: UpdateCreatorData): Promise<MyCreator> => {
    const data = inclusivePick<UpdateCreatorData>(rezData)
    if (!data.name) {
        throw new RequiredParameterError("name")
    }
    if (!data.shortDescription) {
        throw new RequiredParameterError("name")
    }
    if (!data.links) {
        throw new RequiredParameterError("name")
    }
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const update: Partial<Creator> = {
        ...data
    }
    const cS = new CreatorService();
    await cS.update({id: creatorId}, update)

    myCreator.creator = {...myCreator.creator, ...update}
    return myCreator
}

export interface MyCreatorMember {
    user: User;
    creatorMember: CreatorMember
}

export const GetCreatorMembers = async (userId: string, creatorId: string): Promise<MyCreatorMember[]> => {
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    return GetCreatorMembersNoAuth(creatorId)
}

export const GetCreatorMembersNoAuth = async (creatorId: string): Promise<MyCreatorMember[]> => {
    const service = new CreatorMemberService();

    const repo = service.getRepository()
    const qBQuery = repo.createQueryBuilder("wM")
        .addSelect("u")
        .leftJoin(User, "u", "wM.userId=u.id")
        .where("wM.creatorId=:creatorId ANd wM.role != :supportUser")
        .setParameters({
            creatorId,
            supportUser: CreatorMemberRole.SupportUser
        })

    const rawResults = await qBQuery.getRawMany()

    return rawResults.map(r => {
        const result: MyCreatorMember = {
            user: {} as User,
            creatorMember: {} as CreatorMember
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "u") {
                result.user[field] = r[key]
            } else if (pre === "wM") {
                result.creatorMember[field] = r[key]
            }
        }

        return result
    })
}

export interface AddCreatorMembersData {
    emails: string[]
    role: CreatorMemberRole
}

export const AddCreatorMembers = async (userId: string, creatorId: string, data: AddCreatorMembersData) => {
    if (!data.emails) {
        throw new RequiredParameterError("emails");
    }
    if (!Array.isArray(data.emails)) {
        throw new InvalidParameterError("emails is invalid");
    }
    const emails = arrayDeDuplicate(data.emails.map(e => e.trim()))

    for (const email of emails) {
        if (!validateEmail(email)) throw new InvalidParameterError(`Email "${email}" is invalid`);
    }
    // if (data.role !== CreatorMemberRole.Tester) {
    //     throw new InvalidParameterError("Role is invalid");
    // }
    if (![CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.Tester].includes(data.role as CreatorMemberRole)) {
        throw new InvalidParameterError("Role is invalid");
    }
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    // const workspaceMembers = await new WorkspaceMemberService().find({workspaceId: myCreator.creator.workspaceId})

    if (myCreator.membersCount >= MAX_MEMBERS) {
        throw new BadRequestError(ErrorMessage.MaximumMemberLimitReached)
    }
    const userService = new UserService();
    const users = await userService.find({email: In(emails)})
    const registeredEmails = users.map(u => u.email)
    const unregistered = arrayDiff(emails, registeredEmails)

    // // create the users that don't exist
    const toCreate: CreateUser[] = []
    for (const email of unregistered) {
        const uData: CreateUser = {
            firstName: "",
            lastName: "",
            email: email,
        }
        toCreate.push(uData)
    }
    const createdUsers = await userService.batchCreate(toCreate)
    users.push(...createdUsers)

    const usersMap = {}
    for (const user of [...users, ...createdUsers]) {
        usersMap[user.id] = user
    }
    const cMS = new CreatorMemberService()
    const members = await cMS.find({creatorId})

    // for users that are not members of the workspace, add them as collaborators
    const userIds: string[] = Object.keys(usersMap)
    const nonMemberIds: string[] = arrayDiff(userIds, members.map(m => m.userId))

    const toAdd: Partial<CreatorMember>[] = []
    for (const id of nonMemberIds) {
        toAdd.push({
            role: data.role,
            creatorId,
            userId: id,
            invitedById: userId,
        })
    }
    const addedMembers = await cMS.batchAdd(toAdd)
    members.push(...addedMembers)

    const creatorMembers: MyCreatorMember[] = []
    for (let addedMember of addedMembers) {
        const user = usersMap[addedMember.userId]
        if (!user) continue

        const creatorMember: MyCreatorMember = {
            creatorMember: addedMember,
            user
        }
        await sendAddCreatorMemberEmail(creatorMember, myCreator.creator)
        creatorMembers.push(creatorMember)
    }

    return {members, creatorMembers}
}

const sendAddCreatorMemberEmail = async (creatorMember: MyCreatorMember, creator: Creator) => {
    const creatorLink = appUrl(`/creators/${creator.domain}`);

    const to: EmailUser = {
        email: creatorMember.user.email,
        name: `${creatorMember.user.firstName || ''} ${creatorMember.user.lastName || ''}`.trim()
    }
    const subject = `You were added as a ${creatorMember.creatorMember.role} in ${creator.name} on Opendashboard`
    const message = `
	Hello ${creatorMember.user.firstName || 'there'}, <br/> You were added as a ${creatorMember.creatorMember.role} in <a href="${creatorLink}"><strong>${creator.name}</strong></a> in Opendashboard.<br/>
	<p>Click the link below to gain access to ${creator.name}</p>
	`
    const button = {
        label: 'View Profile →',
        url: creatorLink
    }
    const messageId = await SendEmailWithContent(to, subject, message, button)

    return messageId
}

export interface UpdateCreatorMemberData {
    userId: string
    role: CreatorMemberRole
}

export const UpdateCreatorMember = async (userId: string, creatorId: string, data: UpdateCreatorMemberData) => {
    const role = data.role
    const memberId = data.userId || ''
    if (![CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.Tester].includes(data.role as CreatorMemberRole)) {
        throw new InvalidParameterError("Role is invalid");
    }
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    const cMS = new CreatorMemberService()
    const memberToUpdate = await cMS.findOne({
        creatorId, userId: memberId
    })
    if (!memberToUpdate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (memberToUpdate.role === CreatorMemberRole.Owner) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    await cMS.update({creatorId, userId: memberId}, {role})
    return true
}

export interface RemoveCreatorMemberData {
    userId: string
}

export const DeleteCreatorMember = async (userId: string, creatorId: string, data: UpdateCreatorMemberData) => {
    const role = data.role
    const memberId = data.userId || ''
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    const cMS = new CreatorMemberService()
    const memberToUpdate = await cMS.findOne({
        creatorId, userId: memberId
    })
    if (!memberToUpdate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (memberToUpdate.role === CreatorMemberRole.Owner) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    await cMS.hardRemove({creatorId, userId: memberId})
    return true
}

export const StripeConnectAccountURL = async (userId: string, creatorId: string) => {
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    myCreator.creator = await InitStripeConnectAccount(myCreator.creator)

    return accountLink(myCreator.creator)
}

export const StripeConnectRefreshURL = async (domain: string) => {
    const s = new CreatorService()
    const creator = await s.findOne({domain})
    if (!creator) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    return accountLink(creator)
}

const accountLink = async (creator: Creator) => {
    const {accountLink} = await createAccountLink({
        account: creator.stripeAccountId,
        return_url: appUrl(`/creators/${creator.domain}/settings?tab=payout`),
        refresh_url: apiUrl(`/webhook/v1/stripe-refresh-url?domain=${creator.domain}`),
        type: "account_onboarding"
    })
    return {accountLink}
}

export const StripeConnectDashboardURL = async (userId: string, creatorId: string) => {
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    myCreator.creator = await InitStripeConnectAccount(myCreator.creator)

    const {loginLink} = await createLoginLink(myCreator.creator.stripeAccountId)

    return {loginLink}
}

const InitStripeConnectAccount = async (creator: Creator) => {
    if (!creator.stripeAccountId) {
        const s = new CreatorService()

        const businessUrl = isProduction() ? appUrl(`/@${creator.domain}`) : ''
        const r = await createConnectAccount(creator, businessUrl)
        const {account} = r
        const stripeAccountId = account.id

        const update: Partial<Creator> = {
            stripeAccountId
        }
        await s.update({id: creator.id}, update)
        creator.stripeAccountId = stripeAccountId
    }
    return creator
}

export interface MyCreator {
    creator: Creator;
    creatorMember: CreatorMember
    membersCount: number
    referralCode?: string
}

export const GetMyCreators = async (userId: string, creatorId?: string): Promise<MyCreator[]> => {
    const service = new CreatorService();
    const repo = service.getRepository()

    const qBQuery = repo.createQueryBuilder("w")
        .addSelect("wM")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(CreatorMember, "wM2")
                .where("wM2.creatorId = w.id AND wM2.deletedAt IS NULL AND wM2.role != :supportUser")
        }, "membersCount")
        .leftJoin(CreatorMember, "wM", "wM.creatorId=w.id")
        .where("wM.userId=:userId")
        .setParameters({
            userId,
            supportUser: CreatorMemberRole.SupportUser
            // subActive: StatusTypes.Active
        })
    if (creatorId) {
        qBQuery.andWhere("wM.creatorId=:creatorId")
            .setParameters({creatorId})
    }
    let referralCode: string | undefined = undefined

    const s = new AffiliateService()
    let affiliate = await s.findOne({userId})
    if (affiliate) {
        referralCode = affiliate.referralCode
    } else if (creatorId) {
        const creator = await service.findOne({id: creatorId})
        if (creator) {
            affiliate = await s.findOne({userId: creator.ownerId})
            if (affiliate) {
                referralCode = affiliate.referralCode
            }
        }
    }

    const rawResults = await qBQuery.getRawMany()

    return rawResults.map(r => {
        const result: MyCreator = {
            creator: {} as Creator,
            creatorMember: {} as CreatorMember,
            membersCount: Number(r['membersCount']),
            referralCode
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "w") {
                result.creator[field] = r[key]
            } else if (pre === "wM") {
                result.creatorMember[field] = r[key]
            }
        }
        return result
    });
}

export const GetMyCreator = async (userId: string, creatorId: string): Promise<MyCreator> => {
    const myCreator = (await GetMyCreators(userId, creatorId))[0]
    if (!myCreator) throw new NotfoundError(ErrorMessage.EntityNotFound)
    return myCreator
}

export const UploadCreatorFile = async (userId: string, creatorId: string, request: Request, anonymousUser = false): Promise<{ upload: WorkspaceUpload }> => {
    let date = new Date()
    if (!anonymousUser) {
        const user = await GetProfile(userId);
        date = user.createdAt
    }

    const month = date.getMonth() + 1
    const year = date.getFullYear()
    const day = date.getDate()

    const id = generateUUID()

    const data = await ProcessFormToSpaceUpload(request, `c/${creatorId}/${year}/${month}/${day}/u/${userId}`, `${id}`, FileType.File)
    const s = new WorkspaceUploadService()

    const d: Partial<WorkspaceUpload> = {
        finalUrl: data.location,
        height: data.height,
        mimeType: data.mime,
        name: data.name,
        size: data.size,
        thumbnailUrl: undefined,
        type: data.type,
        doSpaceKey: data.key,
        userId: userId,
        workspaceId: '',
        width: data.width
    }
    const upload = await s.insert(d)
    return {upload}
}

export interface MyCreatorStats {
    installed: InstallStats[]
    reviews: ReviewStats[]
    earnings: EarningStats[]
}

export const GetMyCreatorStats = async (userId: string, creatorId: string, templateId?: string) => {
    const creator = await GetMyCreator(userId, creatorId)
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const stats: MyCreatorStats = {
        earnings: await GetTemplateEarningStats(creatorId),
        installed: await GetTemplateInstallStats(creatorId),
        reviews: await GetTemplateReviewStats(creatorId)
    }

    return {stats}
}

export const GetCreatorPayouts = async (userId: string, creatorId: string, params: PaginationParams) => {
    const creator = await GetMyCreator(userId, creatorId)
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {offset, limit} = resolvePaginationParams(params)

    const payouts = await new PayoutService().find({creatorId}, {createdAt: 'DESC'}, limit, offset)
    return {payouts}
}

export interface CreateDiscountData extends Pick<Discount, 'code' | 'name' | 'isActive' | 'templateIds' | 'isAllTemplates' | 'isPercent' | 'amount' | 'isValidForPeriod' | 'validTo' | 'validFrom' | 'isUsageLimited' | 'usageLimit'> {

}

export const CreateDiscount = async (userId: string, creatorId: string, rawData: CreateDiscountData) => {
    validateDiscount(rawData)

    const creator = await GetMyCreator(userId, creatorId)
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const s = new DiscountService()
    const oldD = await s.findOne({
        creatorId,
        code: rawData.code,
    });
    if (oldD) throw new UniqueConstraintError("code")

    const data: Partial<Discount> = {
        creatorId,
        name: rawData.name,
        code: rawData.code.trim().toUpperCase(),
        isActive: rawData.isActive ?? true,
        isValidForPeriod: rawData.isValidForPeriod ?? false,
        validFrom: rawData.isValidForPeriod ? rawData.validFrom : null,
        validTo: rawData.isValidForPeriod ? rawData.validTo : null,
        isUsageLimited: rawData.isUsageLimited ?? false,
        usageLimit: rawData.usageLimit || 0,
        isAllTemplates: rawData.isAllTemplates ?? false,
        templateIds: rawData.isAllTemplates ? [] : rawData.templateIds ?? [],
        createdById: userId,
        updatedById: userId,
        amount: rawData.amount,
        isPercent: !!rawData.isPercent
    };
    return await s.insert(data)
}

const validateDiscount = (rawData: CreateDiscountData) => {
    if (!rawData.name) throw new RequiredParameterError("name");
    if (!rawData.code) throw new RequiredParameterError("code");
    if (!rawData.amount) throw new RequiredParameterError("amount");

    if (Number.isNaN(rawData.amount) || rawData.amount < 1) throw new InvalidParameterError("Discount must be atleast 1")

    if (rawData.isValidForPeriod) {
        if (!rawData.validFrom || !rawData.validTo) {
            throw new BadRequestError("validFrom and validTo are required when isValidForPeriod is true.");
        }
        if (new Date(rawData.validFrom).getTime() >= new Date(rawData.validTo).getTime()) {
            throw new BadRequestError("validFrom must be earlier than validTo.");
        }
    }
    if (rawData.isUsageLimited && (rawData.usageLimit == null || rawData.usageLimit < 1)) {
        throw new BadRequestError("usageLimit is required and must be greater than 0 when isUsageLimited is true.");
    }
}

export interface UpdateDiscountData extends CreateDiscountData {
    id: string
}

export const UpdateDiscount = async (userId: string, creatorId: string, rawData: UpdateDiscountData) => {
    validateDiscount(rawData)

    const creator = await GetMyCreator(userId, creatorId);
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const s = new DiscountService();
    const discount = await s.findOne({creatorId, id: rawData.id});
    if (!discount) throw new NotfoundError(ErrorMessage.EntityNotFound);

    const data: Partial<Discount> = {
        name: rawData.name,
        code: rawData.code.trim().toUpperCase(),
        isActive: rawData.isActive ?? true,
        isValidForPeriod: rawData.isValidForPeriod ?? false,
        validFrom: rawData.isValidForPeriod ? rawData.validFrom : null,
        validTo: rawData.isValidForPeriod ? rawData.validTo : null,
        isUsageLimited: rawData.isUsageLimited ?? false,
        usageLimit: rawData.usageLimit || 0,
        isAllTemplates: rawData.isAllTemplates ?? false,
        templateIds: rawData.isAllTemplates ? [] : rawData.templateIds ?? [],
        updatedById: userId,
        amount: rawData.amount,
        isPercent: !!rawData.isPercent
    };
    await s.update({id: discount.id}, data);

    const updated: Discount = {...discount, ...data}
    return updated

}

export const GetDiscounts = async (userId: string, creatorId: string) => {
    const creator = await GetMyCreator(userId, creatorId);
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const s = new DiscountService();
    return await s.find({creatorId});
}

export interface DeleteDiscountData {
    id: string
}

export const DeleteDiscount = async (userId: string, creatorId: string, data: DeleteDiscountData) => {
    const creator = await GetMyCreator(userId, creatorId);
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const s = new DiscountService();
    const discount = await s.findOne({id: data.id, creatorId});
    if (!discount) throw new NotfoundError(ErrorMessage.EntityNotFound);

    const updatedData: Partial<Discount> = {
        isActive: false,
        code: `${discount.code}|deleted|${discount.id}`,
        updatedById: userId,
        deletedAt: new Date(),
    };

    return await s.update({id: data.id}, updatedData);
}

