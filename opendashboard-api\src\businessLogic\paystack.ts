import config from "../config";
import {sendHTTPRequest} from "../utility/http";
import {BadRequestError} from "../errors/AppError";

const secretKey = config.PAYSTACK.secret_key

export interface PaystackTransactionResponse<T> {
    status: boolean
    message: string
    data: T
}

export interface PaystackTransaction {
    id: number,
    domain: string,
    status: "success" | "failed" | string,
    reference: string,
    amount: number,
    message: string,
    gateway_response: "Successful" | "Declined" | string,
    fees: number
    metadata?: PaystackMetaData
}

export const getVerifiedTransaction = async (reference: string) => {
    const endpoint = `https://api.paystack.co/transaction/verify/${reference}`

    const response = await sendHTTPRequest("get", endpoint, {
        Authorization: `Bearer ${secretKey}`
    })
    if (!response.isSuccess) {
        return null
    }
    const data = response.data as PaystackTransactionResponse<PaystackTransaction>
    if (data.data.status !== 'success') {
        return null
    }
    return data.data
}

// const params = JSON.stringify({
// 	"email": "<EMAIL>",
// 	"amount": "20000"
// })
//
// const options = {
// 	hostname: 'api.paystack.co',
// 	port: 443,
// 	path: '/transaction/initialize',
// 	method: 'POST',
// 	headers: {
// 		Authorization: 'Bearer SECRET_KEY',
// 		'Content-Type': 'application/json'
// 	}
// }

export interface PaystackInitializedTransaction {
    authorization_url: string,
    access_code: string,
    reference: string
    metadata?: PaystackMetaData
}

export interface PaystackMetaData {
    custom_fields?: {
        display_name: string,
        variable_name: string,
        value: string | number
    }[]
    cancel_action?: string
    [key: string]: any;
}

export const initializeTransaction = async (email: string, amount_kobo: number, callback_url: string, metadata?: PaystackMetaData) => {
    const endpoint = `https://api.paystack.co/transaction/initialize`

    const body = {email, amount: amount_kobo, callback_url, metadata}
    const response = await sendHTTPRequest("post", endpoint, {Authorization: `Bearer ${secretKey}`, 'Content-Type': 'application/json'}, body)

    if (!response.isSuccess) {
        return null
    }
    const data = response.data as PaystackTransactionResponse<PaystackInitializedTransaction>
    if (!data.status) {
        throw new BadRequestError(data.message)
    }
    return data.data
}










