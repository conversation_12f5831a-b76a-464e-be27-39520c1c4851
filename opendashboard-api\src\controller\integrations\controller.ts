import {NextFunction, Request, Response} from "express"
import {getIntegrations, getOAuth2Configs, saveConnection} from "../../businessLogic/integrationHelpers";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {BadRequestError, ErrorMessage, InvalidParameterError} from "../../errors/AppError";
import {decodeState} from "@opendashboard-inc/integration-core-api";
import {getCoreApi} from "../../businessLogic/integration";
import {WorkspaceIntegrationConnection} from "../../entity/workspaceIntegrationConnection";

export class IntegrationsController {

    async getAll(request: Request, response: Response, next: NextFunction) {
        const integrations = await getIntegrations()
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                integrations
            },
        }
        return response.json(
            responseData
        )
    }

    async integrationOAuth2Callback(request: Request, response: Response, next: NextFunction) {
        const {state} = request.query;

        if (typeof state !== 'string') {
            throw new InvalidParameterError("State parameter is missing or invalid")
        }
        const stateData = decodeState(state);
        if (!stateData) {
            throw new InvalidParameterError("Failed to decode OAuth2 connection state")
        }
        // const encodedState = encodeState({
        //     connectionName: (name || integrationInfo.displayName).toString(),
        //     clientId: typeof clientId === 'string' ? clientId : undefined,
        //     clientSecret: typeof clientSecret === 'string' ? clientSecret : undefined,
        //     integrationParams: {name: integration},
        //     connectionId,
        //     metadata: {workspaceId}
        // });
        const connectionId = stateData.connectionId
        const {credentials, redirectUri} = await getOAuth2Configs(stateData.integrationParams.name || '', {clientSecret: stateData.clientSecret, clientId: stateData.clientId})

        const workspaceId = stateData.metadata?.workspaceId
        if (!workspaceId) {
            throw new InvalidParameterError("Workspace ID is missing in the state metadata")
        }

        const integration = stateData.integrationParams.name

        const coreApi = getCoreApi()
        const integrationInfo = await coreApi.getIntegration(stateData.integrationParams);

        let connection: WorkspaceIntegrationConnection
        await coreApi.handleOAuth2Callback({
            integration: integrationInfo,
            queryParams: request.query as Record<string, string>,
            state: stateData,
            credentials,
            redirectUri,
            connection: {
                save: async (params) => {
                    const {credentials, id, name} = params
                    connection = await saveConnection({
                        integration,
                        credentials,
                        id: id || connectionId,
                        name,
                        workspaceId,
                    })
                },
                delete: async () => {
                    throw new BadRequestError(ErrorMessage.ErrorOccurredWhileProcessingRequest)
                },
                get: async () => {
                    throw new BadRequestError(ErrorMessage.ErrorOccurredWhileProcessingRequest)
                },
            },
        });
        if (!connection) {
            throw new BadRequestError(ErrorMessage.ErrorOccurredWhileProcessingRequest)
        }

        response.send(`
          <script>
            if (window.opener) {
              window.opener.postMessage({ type: 'OAUTH_SUCCESS', connection: ${JSON.stringify(connection)} }, '*');
              window.close();
            } else {
              document.body.innerText = '✅ OAuth2 connection successful. You can close this tab.';
            }
          </script>
        `);
    }

}