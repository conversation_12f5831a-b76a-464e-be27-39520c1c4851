import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm";

@Entity()
export class WorkspaceBillableTransaction {

    @PrimaryGeneratedColumn("increment")
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @Index()
    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    debitAmountInCents: number

    @Column({type: "text", nullable: true})
    summary: string

}