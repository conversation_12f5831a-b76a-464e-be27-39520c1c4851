import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm";


export enum RiskType {
    QuotaExceeded = "quotaExceeded",
    UnpaidInvoice = "unpaidInvoice",
    UnpaidSubscription = "unpaidSubscription"
}

@Entity()
export class WorkspaceRiskLog {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    riskType: RiskType

    @Index()
    @Column({type: 'boolean', default: false})
    isResolved: boolean

    @Index()
    @Column({type: 'boolean', default: false})
    isClosed: boolean

    @Index()
    @Column({type: 'timestamp', nullable: true})
    startAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    resolvedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    lastNotifiedAt: Date

    @Index()
    @Column({type: 'tinyint', nullable: false, unsigned: true, default: 0})
    notificationCount: number

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}
