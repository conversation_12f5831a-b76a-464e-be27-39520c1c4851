import {AffiliateService} from "../service/affiliate";
import {BadRequestError, ErrorMessage, NotfoundError, RequiredParameterError} from "../errors/AppError";
import {UserService} from "../service/user";
import {datePlusMonths, strReplaceAll} from "opendb-app-db-utils/lib";
import {PaginationParams, resolvePaginationParams} from "./document";
import {PayoutService} from "../service/payout";
import {AffiliateAnalyticService} from "../service/affiliateAnalytic";
import {WorkspaceService} from "../service/workspace";
import {AffiliateEarningService} from "../service/affiliateEarning";
import {Affiliate} from "../entity/affiliate";
import {WorkspaceCredit} from "../entity/WorkspaceCredit";
import {logInfo} from "./logtail";
import {AddAffiliateEarning, AffiliateEarning} from "../entity/affiliateEarning";
import {BillingCycle} from "../entity/BillingCycle";
import {Workspace} from "../entity/Workspace";
import {Payout} from "../entity/payout";

const AffiliateDefaults = Object.freeze({
    referralDiscountPercent: 20,
    referralExpiryMonths: 3,
    earningPercent: 10,
    earningExpiryMonths: 6
})

export const CreateAffiliate = async (userId: string) => {
    let affiliate: Affiliate = undefined

    const affiliates = (await GetAffiliate(userId)).affiliates
    if (affiliates.length > 0) {
        return {affiliate: affiliates[0]}
    }

    const s = new AffiliateService()

    const s1 = new UserService()
    const user = await s1.findOne({id: userId})
    const codes = await s.generateUniqueReferralCode((user.firstName || '' + ' ' + user.lastName || '').trim() || user.email.split('@')[0] || strReplaceAll(userId, '-', ''), 10)
    affiliate = await s.insert({
        userId,
        referralCode: codes[0],
        ...AffiliateDefaults
    })
    return {affiliate}
}

export const GetAffiliate = async (userId: string) => {
    const s = new AffiliateService()
    const affiliates: Affiliate[] = []
    const affiliate = await s.findOne({userId})
    if (affiliate) {
        affiliates.push(affiliate)
    }
    return {affiliates}
}

export const GetApprovedAffiliate = async (userId: string) => {
    const s = new AffiliateService()
    const affiliate = await s.findOne({userId, isApproved: true})
    if (!affiliate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    return {affiliate}
}

export const GetAffiliateByReferralCode = async (referralCode: string) => {
    const s = new AffiliateService()
    const affiliate = await s.findOne({referralCode})
    if (!affiliate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    return {affiliate}
}

export interface UpdateAffiliateData {
    code: string
}

export const UpdateAffiliate = async (userId: string, data: UpdateAffiliateData) => {
    const {affiliate} = await GetApprovedAffiliate(userId)

    const s = new AffiliateService()

    if (!data.code) throw new RequiredParameterError('code')
    if (data.code !== affiliate.referralCode) {
        const curr = await s.findOne({referralCode: data.code})
        if (curr) {
            throw new BadRequestError(ErrorMessage.EntityAlreadyExists)
        }
    }
    await s.update({id: affiliate.id}, {referralCode: data.code})

    affiliate.referralCode = data.code

    return {affiliate}
}

export const GetAffiliatePayouts = async (userId: string, params: PaginationParams = {}) => {
    const {affiliate} = await GetApprovedAffiliate(userId)
    const {offset, limit} = resolvePaginationParams(params)

    const payouts = await new PayoutService().find({affiliateId: affiliate.id}, {createdAt: 'DESC'}, limit, offset)
    return {payouts}
}

export interface AffiliateStats {
    affiliateId: string
    clicks: number
    referrals: number
    expiredReferrals: number
    earningsInCents: number
    paidInCents: number
}

export const GetAffiliateStats = async (userId: string) => {
    const {affiliate} = await GetApprovedAffiliate(userId)

    {
        const stats: AffiliateStats = {
            affiliateId: affiliate.id,
            clicks: 0,
            referrals: 0,
            expiredReferrals: 0,
            earningsInCents: 0,
            paidInCents: 0,
        }
        const aAS = new AffiliateAnalyticService()
        {
            const data = await aAS.getRepository().createQueryBuilder('aa')
                .select('SUM(aa.clickCount)', 'clicks')
                .where('aa.affiliateId = :affiliateId', {affiliateId: affiliate.id})
                .getRawOne()
            stats.clicks = parseInt(data.clicks, 10)
        }
        {
            const expiryMonths = affiliate.earningExpiryMonths;
            const wS = new WorkspaceService();
            const data = await wS.getRepository().createQueryBuilder('w')
                .select('COUNT(1)', 'referrals')
                .addSelect(`SUM(CASE WHEN w.createdAt < DATE_SUB(NOW(), INTERVAL :expiryMonths MONTH) THEN 1 ELSE 0 END)`, 'expiredReferrals')
                .where('w.affiliateId = :affiliateId', {affiliateId: affiliate.id, expiryMonths})
                .getRawOne();
            stats.referrals = parseInt(data.referrals, 10);
            stats.expiredReferrals = parseInt(data.expiredReferrals, 10);
        }
        {
            const s = new AffiliateEarningService()
            const data = await s.getRepository().createQueryBuilder('ae')
                .select('SUM(ae.earningsInCents)', 'earningsInCents')
                .where('ae.affiliateId = :affiliateId', {affiliateId: affiliate.id})
                .getRawOne()
            stats.earningsInCents = parseFloat(data.earningsInCents) || 0
        }
        {
            const s = new PayoutService()
            const data = await s.getRepository().createQueryBuilder('p')
                .select('SUM(p.amountInCents)', 'amountInCents')
                .where('p.affiliateId = :affiliateId', {affiliateId: affiliate.id})
                .getRawOne()
            stats.paidInCents = parseFloat(data.amountInCents) || 0
        }
    }
    const stats = (await GetAffiliateStatsForIds([affiliate.id]))[0]
    return {stats}
}

export const GetAffiliateStatsForIds = async (affiliateIds: string[]): Promise<AffiliateStats[]> => {
    if (affiliateIds.length === 0) return []
    const aAS = new AffiliateAnalyticService()

    const statsData = await aAS.getRepository().createQueryBuilder('aa')
        .select('aa.affiliateId', 'affiliateId')
        .addSelect('SUM(aa.clickCount)', 'clicks')
        .addSelect(subQuery => {
            return subQuery
                .select('COUNT(1)')
                .from(Workspace, 'w')
                .where('w.affiliateId = aa.affiliateId')
        }, 'referrals')
        .addSelect(subQuery => {
            return subQuery
                .select('SUM(CASE WHEN w.createdAt < DATE_SUB(NOW(), INTERVAL a.earningExpiryMonths MONTH) THEN 1 ELSE 0 END)')
                .from(Workspace, 'w')
                .where('w.affiliateId = aa.affiliateId')
        }, 'expiredReferrals')
        .addSelect(subQuery => {
            return subQuery
                .select('SUM(ae.earningsInCents)')
                .from(AffiliateEarning, 'ae')
                .where('ae.affiliateId = aa.affiliateId')
        }, 'earningsInCents')
        .addSelect(subQuery => {
            return subQuery
                .select('SUM(p.amountInCents)')
                .from(Payout, 'p')
                .where('p.affiliateId = aa.affiliateId')
        }, 'paidInCents')
        .leftJoin(Affiliate, 'a', 'a.id = aa.affiliateId')
        .where('aa.affiliateId IN (:...affiliateIds)', {affiliateIds})
        .groupBy('aa.affiliateId')
        .getRawMany()

    const statsMap: { [key: string]: AffiliateStats } = {}

    affiliateIds.forEach(id => {
        statsMap[id] = {
            affiliateId: id,
            clicks: 0,
            referrals: 0,
            expiredReferrals: 0,
            earningsInCents: 0,
            paidInCents: 0,
        }
    })

    statsData.forEach(data => {
        statsMap[data.affiliateId] = {
            affiliateId: data.affiliateId,
            clicks: parseInt(data.clicks, 10) || 0,
            referrals: parseInt(data.referrals, 10) || 0,
            expiredReferrals: parseInt(data.expiredReferrals, 10) || 0,
            earningsInCents: parseFloat(data.earningsInCents) || 0,
            paidInCents: parseFloat(data.paidInCents) || 0,
        }
    })

    return affiliateIds.map(id => statsMap[id])
}

export const UpdatePayoutSettings = async (userId: string, data: any) => {
    throw new BadRequestError(ErrorMessage.UnableToProcessRequest)
}

export const getWorkspaceAffiliateCalculateDiscountAndEarning = async (workspaceId: string, amountInCents: number) => {
    const wS = new WorkspaceService()
    const workspace = await wS.findOne({id: workspaceId})

    return getAffiliateCalculateDiscountAndEarning(workspace, amountInCents)
}

export interface AffiliateCalculateDiscountAndEarning {
    affiliate?: Affiliate
    amountToPayInCents: number
    discountInCents: number
    earningsInCents: number
    discountMonthsRemaining: number
    earningsMonthsRemaining: number
    note: string
    workspace: Workspace
}

export const getAffiliateCalculateDiscountAndEarning = async (workspace: Workspace, amountInCents: number): Promise<AffiliateCalculateDiscountAndEarning> => {
    let amountToPayInCents = amountInCents
    let discountInCents = 0
    let earningsInCents = 0
    let notes: string[] = []
    let discountMonthsRemaining = 0
    let earningsMonthsRemaining = 0

    let affiliate: Affiliate | undefined = undefined

    const dateNow = new Date()

    if (workspace.affiliateId) {
        const aS = new AffiliateService()
        affiliate = await aS.findOne({id: workspace.affiliateId})
    }
    if (affiliate) {
        const createdAt = workspace.createdAt
        const referralExpiryMonths = affiliate.referralExpiryMonths
        const referralDiscountPercent = affiliate.referralDiscountPercent

        const referralExpiryDate = datePlusMonths(createdAt, referralExpiryMonths)
        if (referralExpiryDate > dateNow) {
            discountInCents = (amountInCents * referralDiscountPercent) / 100
            discountMonthsRemaining = Math.min(Math.ceil((referralExpiryDate.getTime() - dateNow.getTime()) / (1000 * 60 * 60 * 24 * 30)), affiliate.referralExpiryMonths)
        } else notes.push('Referral discount expired')
        amountToPayInCents = amountInCents - discountInCents

        const earningExpiryMonths = affiliate.earningExpiryMonths
        const earningPercent = affiliate.earningPercent
        const earningExpiryDate = datePlusMonths(createdAt, earningExpiryMonths)

        if (earningExpiryDate > dateNow) {
            earningsInCents = earningPercent / 100 * amountInCents
            earningsMonthsRemaining = Math.min(Math.ceil((earningExpiryDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24 * 30)), affiliate.earningExpiryMonths)
        } else notes.push('Referral earnings expired')
    } else notes.push('No affiliate found')
    return {
        affiliate,
        amountToPayInCents,
        workspace,
        discountInCents,
        earningsInCents,
        discountMonthsRemaining,
        earningsMonthsRemaining,
        note: notes.join(', ')
    }
}

export const AddAffiliateEarningOnWorkspaceCreditPurchase = async (credit: WorkspaceCredit) => {
    const {affiliate, earningsInCents, note} = await getWorkspaceAffiliateCalculateDiscountAndEarning(credit.workspaceId, credit.costInCents)

    if (affiliate && earningsInCents > 0) {
        const data: AddAffiliateEarning = {
            earningsInCents,
            summary: "",
            affiliateId: affiliate.id,
            workspaceCreditId: String(credit.id)
        }
        const earning = await new AffiliateEarningService().insert(data)
        logInfo(`Affiliate earning added for credit purchase`, {credit, affiliate, earning, note,})

        return {earning}
    }
    logInfo('No affiliate earning added for credit purchase', {credit, affiliate, earningsInCents, note})
}

export const AddAffiliateEarningOnWorkspaceBillingCycle = async (cycle: BillingCycle) => {
    if (!cycle.stripeSubscriptionId || cycle.costInCents === 0) return
    const {affiliate, earningsInCents, note} = await getWorkspaceAffiliateCalculateDiscountAndEarning(cycle.workspaceId, cycle.costInCents)

    if (affiliate && earningsInCents > 0) {
        const data: AddAffiliateEarning = {
            earningsInCents,
            summary: "",
            affiliateId: affiliate.id,
            billingCycleId: String(cycle.id),
            subscriptionId: cycle.stripeSubscriptionId
        }
        const earning = await new AffiliateEarningService().insert(data)
        logInfo(`Affiliate earning added for new billing cycle`, {cycle, affiliate, earning, note,})

        return {earning}
    }
    logInfo('No affiliate earning added for new billing cycle', {cycle, affiliate, earningsInCents, note})
}

export interface AddAffiliateClickData {
    referralCode: string
}

export interface AffiliateOffer extends Pick<Affiliate, 'referralDiscountPercent' | 'referralExpiryMonths'> {

}

export const AddAffiliateClick = async (data: AddAffiliateClickData) => {
    const {affiliate} = await GetAffiliateByReferralCode(data.referralCode)

    const s = new AffiliateAnalyticService()
    const analytic = await s.addAnalytics({
        affiliateId: affiliate.id,
        clickCount: 1,
    })
    const offer: AffiliateOffer = {
        referralDiscountPercent: affiliate.referralDiscountPercent,
        referralExpiryMonths: affiliate.referralExpiryMonths
    }

    return {offer}
}


