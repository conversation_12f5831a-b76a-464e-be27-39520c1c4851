import {datePlus<PERSON><PERSON><PERSON>, datePlus<PERSON>ears, getDaysIn<PERSON><PERSON>h, isDateValid} from "opendb-app-db-utils/lib";
import {BillingCycle} from "../entity/BillingCycle";
import {AddOnsQuota, CreatorSettings, free, PayPerUsePricing, PayPerUsePricingInCents, UsageLimits, WorkspaceAddOns, WorkspacePlan, WorkspacePlanPriceIdMap, WorkspacePlans} from "./subscription";
import {BadRequestError, ErrorMessage, ServerProcessingError, UnauthorizedError} from "../errors/AppError";
import {BillingCycleService, CreateBillingCycleData} from "../service/billingCycle";
import {datePlusDays, getCurrentDate} from "opendb-app-db-utils/lib/methods/date";
import {In, IsNull, LessThan, LessThanOrEqual, <PERSON><PERSON>han, Not} from "typeorm";
import {Workspace} from "../entity/Workspace";
import {cancelSubscription, createCustomer, stopSubscriptionAutoRenew, stripe, StripeCheckoutSessionMetadata} from "./stripe";
import {WorkspaceService} from "../service/workspace";
import {WorkspaceCreditService} from "../service/workspaceCredit";
import {Stripe} from "stripe";
import {SubscriptionService} from "../service/subscription";
import {Subscription, SubscriptionStatus} from "../entity/Subscription";
import {WorkspaceStatsService} from "../service/workspaceStats";
import {WorkspaceStats} from "../entity/WorkspaceStats";
import {WorkspaceRiskLogService} from "../service/workspaceRiskLog";
import {RiskType, WorkspaceRiskLog} from "../entity/WorkspaceRiskLog";
import {WorkspaceUsage} from "../entity/WorkspaceUsage";
import {WorkspaceUsageService} from "../service/workspaceUsage";
import {WorkspaceBillableTransactionService} from "../service/workspaceBillableTransaction";
import {redisAcquireLock, redisReleaseLock} from "../connection/redis";
import {HandleTemplatePurchaseAbandonedWebhook, HandleTemplatePurchaseCompletedWebhook} from "./templates";
import {PaymentProcessor, PurchaseStatus, TemplatePurchase} from "../entity/templatePurchase";
import {TemplatePurchaseService} from "../service/templatePurchase";
import {Template} from "../entity/template";
import {PayoutService} from "../service/payout";
import {Payout, PayoutStatus} from "../entity/payout";
import {CreatorService} from "../service/creator";
import {consoleLog} from "./logtail";
import {GetCreatorMembersNoAuth} from "./creator";
import {CreatorMemberRole} from "../entity/creatorMember";
import {EmailButton, EmailUser, SendEmailWithContent} from "./email";
import {appUrl} from "../config";
import {dbDataSource} from "../connection/db";
import {WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {AddAffiliateEarningOnWorkspaceBillingCycle} from "./affiliate";
import {AffiliateEarningService} from "../service/affiliateEarning";
import {AffiliateEarning} from "../entity/affiliateEarning";
import {AffiliateService} from "../service/affiliate";
import {User} from "../entity/User";
import {Affiliate} from "../entity/affiliate";
import {broadcastWorkspaceUpdated} from "../socketio/workspace";
import {GetWorkspaceById, getWorkspaceMemberNoCacheNoAuth} from "./workspace";
import {RecalculateWorkspaceStats} from "./stats";

export interface AppDate {
    day: number
    month: number
    year: number
}

export const AppDateToMySQL = (date: AppDate) => {
    return `${date.year}-${date.month}-${date.day}`
}


export const SendOverQuotaEmail = async (to: EmailUser, upgradeLink: string, workspace: Workspace, cc: string[]) => {
    const workspaceLink = appUrl(`/${workspace.domain}`);
    const body = `
    <p style="font-size: 24px; font-weight: 600;">You've exceeded your allocated quota</p>
    <p style="color:#313539;">Hello there,</p>
    <p style="color:#313539;">
        It looks like you've exceeded your allocated quota for
       <a href="${workspaceLink}" style="color: #313539; text-decoration: underline; font-weight: 600;">
            ${workspace.name}
        </a>
        on Opendashboard. 
        To continue using our services without interruptions, consider upgrading your plan.
    </p>
    <p style="color:#313539;">
        Click on the button below to review your usage and explore upgrade options.
    </p>
    `;

    const button: EmailButton = {
        label: "Upgrade Plan",
        url: upgradeLink
    };

    const subject = `You've exceeded your quota for ${workspace.name} on Opendashboard`;
    const bcc = ['<EMAIL>']

    const messageId = await SendEmailWithContent(to, subject, body, button, null, true, undefined, undefined, undefined, undefined, false, cc, bcc);

    console.log({messageId});
    return {messageId};
};

export const sendLimitExceededQuotaEmail = async (to: EmailUser, upgradeLink: string, workspace: Workspace, cc: string[]) => {
    const workspaceLink = appUrl(`/${workspace.domain}`);
    const body = `
   <p style="color:#313539;">Hello there,</p>
<p style="color:#313539;">
    We noticed that the issue regarding your allocated quota for 
    <a href="${workspaceLink}" style="color: #313539; text-decoration: underline; font-weight: 600;">
        ${workspace.name}
    </a> 
    on Opendashboard has not been addressed within the last 7 days. As a result, some functionalities have been limited to ensure fair usage. 
    To fully restore access and continue using our services without restrictions, we recommend upgrading your account at your earliest convenience.
</p>
<p style="color:#313539;">If you need assistance or have any questions, feel free to reach out to our support team.</p>
    `;

    const button: EmailButton = {
        label: "Upgrade Plan",
        url: upgradeLink
    };

    const subject = `You've exceeded your quota for ${workspace.name} on Opendashboard`;

    const bcc = ['<EMAIL>']


    const messageId = await SendEmailWithContent(to, subject, body, button, null, true, undefined, undefined, undefined, undefined, false, cc, bcc);

    console.log({messageId});
    return {messageId};
};


export const startBillingCycle = async (workspaceId: string,
                                        planId = '',
                                        priceId = '',
                                        stripeSubscriptionId = '',
                                        anchorDay?: number,
                                        addOnsQuota?: AddOnsQuota,
                                        persistExistingBillingCycle = false,
                                        ignoreExisting = false,
): Promise<{
    billingCycle: BillingCycle
}> => {
    let plan = WorkspacePlans['free']
    let pricePlan = WorkspacePlanPriceIdMap[priceId]
    if (planId) {
        plan = WorkspacePlans[planId]
    }
    if (!plan) {
        throw new ServerProcessingError(`Invalid plan ${planId} when creating billing cycle`)
    }
    if (!pricePlan) {
        throw new ServerProcessingError(`Invalid price id ${priceId}`)
    }

    // get current plan
    let currentBillingCycle: BillingCycle;
    if (!ignoreExisting) {
        currentBillingCycle = await CurrentBillingCycle(workspaceId);
        if (currentBillingCycle) {
            if (persistExistingBillingCycle) {
                // keep existing billing Cycle
                return {
                    billingCycle: currentBillingCycle
                }
            }
            await endCurrentBillingCycle(workspaceId)
        }
    }
    const today = getCurrentDate()
    const billingCycleStarts: AppDate = {day: today.getDate(), month: today.getMonth() + 1, year: today.getFullYear()}
    const billingCycleEnds: AppDate = cycleEndDate(billingCycleStarts)
    anchorDay = anchorDay ? anchorDay : currentBillingCycle ? currentBillingCycle.anchorDay : today.getDate()
    anchorDay = anchorDay == 0 ? today.getDate() : anchorDay

    const endsAt = new Date(AppDateToMySQL(billingCycleEnds))
    const startsAt = new Date(AppDateToMySQL(billingCycleStarts))

    endsAt.setHours(23, 59, 59, 999)
    startsAt.setHours(0, 0, 0, 0)

    if (!addOnsQuota) {
        addOnsQuota = {
            aiGeneration: 0,
            enrichment: 0,
            collaborators: 0,
            records: 0,
            sendingDomains: 0,
            sendingEmails: 0,
            users: 0,
            workflowTask: 0
        }
    }
    const usages: UsageLimits = {
        aiGeneration: 0,
        collaborators: 0,
        dataHistory: 0,
        emails: 0,
        enrichment: 0,
        records: 0,
        senderDomains: 0,
        senderEmails: 0,
        users: 0,
        workflowTask: 0,
        pollingIntervalMins: 30
    }
    const data: CreateBillingCycleData = {
        addOnsQuota,
        anchorDay,
        auditLog: [],
        cyclePlanQuota: plan.limits,
        cycleUsage: usages,
        endsAt,
        isActive: true,
        isRenewing: true,
        planId: plan.id,
        priceId,
        startsAt,
        workspaceId,
        stripeSubscriptionId
    }

    const s = new BillingCycleService()
    const billingCycle = await s.insert(data)
    const workspace = await GetWorkspaceById(workspaceId)
    await RestoreWorkspaceLimitedFunctionality(workspace)
    await AddAffiliateEarningOnWorkspaceBillingCycle(billingCycle)

    return {
        billingCycle
    }
}

export const nextAnchorDate = (date: AppDate, anchorDay: number = 0): AppDate => {
    const dateStr = AppDateToMySQL(date)
    if (!isDateValid(dateStr)) {
        throw `Invalid Date ${dateStr}`
    }
    let {day, month, year} = date

    if (anchorDay > 0) {
        day = anchorDay
    }

    let nextMonth = month == 12 ? 1 : month + 1
    let nextYear = month == 12 ? year + 1 : year

    let nextDay = Math.min(getDaysInMonth(nextYear, nextMonth - 1), day)

    return {
        day: nextDay, month: nextMonth, year: nextYear
    }
}

export const cycleEndDate = (startDate: AppDate): AppDate => {
    const dateStr = AppDateToMySQL(startDate)
    if (!isDateValid(dateStr)) {
        throw `Invalid Date ${dateStr}`
    }
    let {day, month, year} = startDate
    let daysInMonth = getDaysInMonth(year, month - 1)

    if (day == 1) {
        // it's the first day of the month, end should be the last day of that month
        return {
            day: daysInMonth, month, year
        }
    }
    let nextYear = month == 12 ? year + 1 : year
    let nextMonth = month == 12 ? 1 : month + 1

    daysInMonth = getDaysInMonth(nextYear, nextMonth - 1)

    let endDay = Math.min(day - 1, daysInMonth - 1)

    return {
        day: endDay, month: nextMonth, year: nextYear
    }
}

export const CurrentBillingCycle = async (workspaceId: string): Promise<BillingCycle> => {
    const s = new BillingCycleService()
    return s.findOne({workspaceId, isActive: true}, {id: 'DESC'})
}

export const ActiveCredits = async (workspaceId: string) => {
    const s = new WorkspaceCreditService()
    const credits = await s.getActiveCredits(workspaceId)

    const availableCreditInCents = credits.reduce((v, c) => v + Number(c.creditRemainingInCents), 0)

    return {
        availableCreditInCents,
        credits
    }
}

const endCurrentBillingCycle = async (workspaceId: string, data: Partial<BillingCycle> = {}): Promise<boolean> => {
    if (!data) {
        data = {}
    }
    data.isActive = false
    data.endedAt = new Date()
    data.isRenewing = false

    const s = new BillingCycleService()
    await s.update({workspaceId, isActive: true}, data)
    return true
}

export const getRenewalDueBillingCycles = async () => {
    const s = new BillingCycleService()
    return s.find({
        isActive: true,
        endsAt: LessThan(new Date()),
        deletedAt: IsNull()
    })
}

export const renewBillingCycle = async (billingCycle: BillingCycle) => {
    let planId = ''
    let priceId = ''
    let anchorDay: number = billingCycle.anchorDay
    let addOns: AddOnsQuota
    let subscriptionId = ''
    const workspaceId = billingCycle.workspaceId

    if (billingCycle.isRenewing) {
        planId = billingCycle.planId
        priceId = billingCycle.priceId
        addOns = billingCycle.addOnsQuota
        subscriptionId = billingCycle.stripeSubscriptionId
    } else if (billingCycle.futurePriceId && billingCycle.futureStripeSubscriptionId) {
        const plan = WorkspacePlanPriceIdMap[billingCycle.futurePriceId] || free
        planId = plan.id
        priceId = billingCycle.futurePriceId
        subscriptionId = billingCycle.futureStripeSubscriptionId
    }
    return startBillingCycle(workspaceId, planId, priceId, subscriptionId, anchorDay, addOns)
}

export interface BillingCycleRenewal {
    old: BillingCycle
    new: BillingCycle
}

export enum CRON_LOCK_KEYS {
    RENEW_DUE_BILLING_CYCLES = 'renewDueBillingCycles',
    CHECK_AND_HANDLE_OVER_QUOTA_WORKSPACES = 'CheckAndHandleOverQuotaWorkspaces',
    CHECK_TEMPLATE_PURCHASES_PREPARE_PAYOUT = 'CheckTemplatePurchasesAndPreparePayout',
    CHECK_AFFILIATE_EARNINGS_PREPARE_PAYOUT = 'CheckAffiliateEarningsAndPreparePayout',
    PROCESS_DUE_REMINDERS = 'ProcessDueReminders',
    PUSH_ANALYTICS = 'PushAnalytics',
    EXPIRE_TEMPLATE_PURCHASE_AFTER_TIMEOUT = 'ExpireTemplatePurchaseAfterTimeout',
    FIND_AND_TRIGGER_SCHEDULED_WORKFLOWS = 'FindAndTriggerScheduledWorkflows',
}

export const renewDueBillingCycles = async () => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.RENEW_DUE_BILLING_CYCLES)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }
    const billingCycles = await getRenewalDueBillingCycles()

    const started = new Date()

    const renewals: BillingCycleRenewal[] = []
    for (const cycle of billingCycles) {
        const res = await renewBillingCycle(cycle)
        renewals.push({
            old: cycle,
            new: res.billingCycle
        })
    }
    const completed = new Date()
    const timeTakenMs = completed.getTime() - started.getTime()

    // await redisReleaseLock(CRON_LOCK_KEYS.RENEW_DUE_BILLING_CYCLES)

    return {
        renewals,
        timeTakenMs,
        count: renewals.length
    }
}


interface OverQuotaWorkspace {
    workspace: Workspace
    workspaceStats: WorkspaceStats,
    billingCycle: BillingCycle
}


export const RestoreWorkspaceLimitedFunctionality = async (workspace: Workspace) => {
    await RecalculateWorkspaceStats(workspace.id)
    const overQuotaWorkspaces = await GetOverQuotaWorkspaces([workspace.id], [], false)
    if (overQuotaWorkspaces.length > 0) return false

    const s2 = new WorkspaceRiskLogService()
    await s2.update({isClosed: false, workspaceId: workspace.id}, {isClosed: true, isResolved: true, resolvedAt: new Date()});

    if (workspace.isFunctionalityLimited) {
        const s = new WorkspaceService();
        await s.update({id: workspace.id}, {isFunctionalityLimited: false});
        broadcastWorkspaceUpdated(workspace.id, {isFunctionalityLimited: false});
    }
    return true
};


export const GetOverQuotaWorkspaces = async (includeIds: string[] = [], excludeIds: string[] = [], excludeRestricted = true) => {
    const s = new WorkspaceStatsService()
    const qB = s.getRepository().createQueryBuilder("wS")
        .select("wS")
        .addSelect("w")
        .addSelect("bC")
        .leftJoin(Workspace, 'w', 'w.id = wS.workspaceId')
        .leftJoin(BillingCycle, 'bC', 'w.id = bC.workspaceId AND isActive = 1')
        .where(`
        (wS.users > bC.usersQuota 
        OR wS.records > bC.recordsQuota 
        OR wS.collaborators > bC.collaboratorsQuota 
        OR wS.sendingDomains > CAST(JSON_UNQUOTE(bC.cyclePlanQuota->'$.senderDomains') AS SIGNED) 
        OR wS.sendingEmails > CAST(JSON_UNQUOTE(bC.cyclePlanQuota->'$.senderEmails') AS SIGNED))
    `);
    if (excludeRestricted) {
        qB.andWhere("w.isFunctionalityLimited = 0");
    }
    qB.andWhere("w.createdAt <= :date", {date: datePlusDays(new Date(), -30)});

    if (includeIds.length > 0) {
        qB.andWhere({workspaceId: In(includeIds)})
    } else if (excludeIds.length > 0) {
        qB.andWhere({workspaceId: Not(In(excludeIds))})
    }

    const rawResults = await qB.getRawMany()

    return rawResults.map(r => {
        const row: OverQuotaWorkspace = {
            workspace: {} as Workspace,
            workspaceStats: {} as WorkspaceStats,
            billingCycle: {} as BillingCycle
        }

        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "w") {
                row.workspace[field] = r[key]
            } else if (pre === "wS") {
                row.workspaceStats[field] = r[key]
            } else if (pre === "bC") {
                row.billingCycle[field] = r[key]
            }
        }

        return row
    })
}

const notifyOwnersAndAdminAboutOverQuota = async (workspace: Workspace, risk: WorkspaceRiskLog) => {
    const members = await getWorkspaceMemberNoCacheNoAuth(workspace.id)


    const owner = members.find(k => k.user.id === workspace.ownerId)
    const admin = members.filter(k => k.workspaceMember.role === WorkspaceMemberRole.Admin)


    const url = appUrl(`/${workspace.domain}/settings/billing`);

    const to: EmailUser = {
        email: owner.user.email,
        name: `${owner.user.firstName} ${owner.user.lastName}`.trim()
    };

    const cc = admin.map(m => m.user.email)

    const sendEmail = await SendOverQuotaEmail(to, url, workspace, cc)
    return sendEmail
};


const limitWorkspaceFunctionality = async (workspace: Workspace) => {

    const s = new WorkspaceService()
    const members = await getWorkspaceMemberNoCacheNoAuth(workspace.id)

    await s.update({id: workspace.id}, {isFunctionalityLimited: true})

    broadcastWorkspaceUpdated(workspace.id, {isFunctionalityLimited: true})

    const owner = members.find(k => k.user.id === workspace.ownerId)
    const admin = members.filter(k => k.workspaceMember.role === WorkspaceMemberRole.Admin)

    const url = appUrl(`/${workspace.domain}/settings/billing`);

    const to: EmailUser = {
        email: owner.user.email,
        name: `${owner.user.firstName} ${owner.user.lastName}`.trim()
    };

    const cc = admin.map(m => m.user.email)

    const sendEmail = await sendLimitExceededQuotaEmail(to, url, workspace, cc)
}

export const ProcessWorkspaceOverQuota = async (workspace: Workspace, riskLog: WorkspaceRiskLog = null) => {
    const s = new WorkspaceRiskLogService()
    const notificationDays = [1, 3, 7, 14, 20]; // Days on which to send notifications
    const blockOnDay = 21; // Day to limit functionality

    if (!riskLog) {
        // No risk log means this is a new over-quota workspace.
        console.log("No risk log means this is a new over-quota workspace.", workspace);
        const newRiskLog = await s.insert({
            workspaceId: workspace.id,
            riskType: RiskType.QuotaExceeded,
            startAt: new Date(),
            isResolved: false,
            lastNotifiedAt: new Date(), // Initial notification sent immediately.
            notificationCount: 1 // Initial notification count.
        });
        await notifyOwnersAndAdminAboutOverQuota(workspace, newRiskLog);
    } else {
        // Process existing risk log.
        const daysSinceStart = Math.floor((Date.now() - riskLog.startAt.getTime()) / (24 * 60 * 60 * 1000));
        console.log(`Workspace ${workspace.id} is ${daysSinceStart} days over quota`);

        if (daysSinceStart >= blockOnDay) {
            // Time to limit functionality after grace period
            await limitWorkspaceFunctionality(workspace);
            await s.update({id: riskLog.id}, {isClosed: true});
        } else if (notificationDays.includes(daysSinceStart) &&
            (!riskLog.lastNotifiedAt ||
                Math.floor((Date.now() - riskLog.lastNotifiedAt.getTime()) / (24 * 60 * 60 * 1000)) >= 1)) {
            // Send notification on specific days if we haven't sent one today
            await notifyOwnersAndAdminAboutOverQuota(workspace, riskLog);
            await s.update({id: riskLog.id}, {
                lastNotifiedAt: new Date(),
                notificationCount: () => `notificationCount + 1`
            });
        }
    }
}


export const CheckAndHandleOverQuotaWorkspaces = async () => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.CHECK_AND_HANDLE_OVER_QUOTA_WORKSPACES)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }
    const s = new WorkspaceRiskLogService()
    const unresolvedRisks = await s.find({riskType: RiskType.QuotaExceeded, isClosed: false})
    console.log("Unresolved risks", unresolvedRisks)

    // Get IDs of workspaces from unresolved risks.
    const riskWorkspaceIds = unresolvedRisks.map(risk => risk.workspaceId);

    let workspaceIds: string[] = []


    // Process all of the over quota workspaces that are still over quota.
    const stillOverQuotaWorkspaces = await GetOverQuotaWorkspaces();
    console.log("Still over quota workspaces", stillOverQuotaWorkspaces)

    for (const workspace of stillOverQuotaWorkspaces) {
        const riskLog = unresolvedRisks.find(risk => risk.workspaceId === workspace.workspace.id);
        await ProcessWorkspaceOverQuota(workspace.workspace, riskLog);
        workspaceIds.push(workspace.workspace.id)
    }
    // Handle all of the workspaces in riskWorkspaceIds that are no longer over quota.
    const resolvedWorkspaceIds = riskWorkspaceIds.filter(id => !workspaceIds.includes(id));
    for (const id of resolvedWorkspaceIds) {
        const riskLog = unresolvedRisks.find(risk => risk.workspaceId === id);
        if (riskLog) {
            await s.update({id: riskLog.id}, {
                isResolved: true,
                resolvedAt: new Date(),
                isClosed: true
            })

        }
    }
    await redisReleaseLock(CRON_LOCK_KEYS.CHECK_AND_HANDLE_OVER_QUOTA_WORKSPACES)

    return {
        workspaceIds, resolvedWorkspaceIds
    }
}

export const initStripeCustomer = async (workspace: Workspace) => {
    if (!workspace.stripeCustomerId) {
        const s = new WorkspaceService()

        const r = await createCustomer(workspace)
        const {customer} = r
        const stripeCustomerId = customer.id

        const update: Partial<Workspace> = {
            stripeCustomerId: stripeCustomerId
        }
        await s.update({id: workspace.id}, update)
        workspace.stripeCustomerId = stripeCustomerId
    }
    return workspace
}

export const handleSubscriptionCreated = async (subscriptionData: Stripe.Subscription) => {

    const {id, customer, status, items, billing_cycle_anchor, ...rest} = subscriptionData

    const now = new Date()
    const nowTsSeconds = now.getTime() / 1000

    const startDiffTs = Math.abs(billing_cycle_anchor - nowTsSeconds)
    const startingImmediately = startDiffTs < 2 * 60 // less than 2 min

    console.log({
        subscriptionData,
        startDiffTs,
        startingImmediately,
        nowTsSeconds
    })

    // @ts-ignore
    const plan = subscriptionData.plan as Stripe.Plan
    const wsPlan = WorkspacePlanPriceIdMap[plan.id]
    if (!wsPlan) {
        throw new BadRequestError(ErrorMessage.PlanNotFound)
    }
    console.log({
        wsPlan,
        id,
        customer,
        status,
        items,
        plan
    })

    // check if the current billing cycle is on a subscription
    //      if yes, also check if this new subscription starts immediately
    //      end the current subscription on stripe either immediately or just cancel renewal, so it ends on its own


    const s = new WorkspaceService()
    const workspace = await s.findOne({stripeCustomerId: customer as string})
    if (!workspace) {
        throw new BadRequestError(ErrorMessage.WorkspaceNotFound)
    }

    const s2 = new SubscriptionService()

    const endsAt = plan.interval === 'month' ? datePlusMonths(now, 1) :
                   datePlusYears(now, 1)

    const todayDate = now.getDate()

    const cycle = await CurrentBillingCycle(workspace.id)
    if (cycle.stripeSubscriptionId) {
        if (startingImmediately) {
            await cancelSubscription(cycle.stripeSubscriptionId)
        } else {
            await stopSubscriptionAutoRenew(cycle.stripeSubscriptionId)
            // update the active billing cycle's future's subscription id to this one and do not start a new one
        }
    }


    const subscription = await s2.insert({
        workspaceId: workspace.id,
        stripeSubscriptionId: id,
        status: status as SubscriptionStatus,
        planId: wsPlan.id,
        priceId: plan.id,
        anchorDay: todayDate,
        startsAt: now,
        endsAt,
        auditLog: []
    })


    let billingCycle: BillingCycle
    if (startingImmediately) {
        const res = await startBillingCycle(
            workspace.id,
            wsPlan.id,
            plan.id,
            id,
            todayDate
        )
        billingCycle = res.billingCycle
    } else {
        const s = new BillingCycleService()
        await s.update({
                workspaceId: workspace.id,
                isActive: true,
                isRenewing: false
            }, {futureStripeSubscriptionId: id, futurePriceId: plan.id}
        )
    }

    return {
        subscription,
        billingCycle
    }

}

export const handleSubscriptionUpdated = async (subscriptionData: Stripe.Subscription) => {
    const {id, customer, status, items, cancel_at_period_end, ...rest} = subscriptionData

    // @ts-ignore
    const plan = subscriptionData.plan as Stripe.Plan
    const isRenewing = !cancel_at_period_end

    const s = new WorkspaceService()
    const workspace = await s.findOne({stripeCustomerId: customer as string})
    if (!workspace) {
        throw new BadRequestError(ErrorMessage.WorkspaceNotFound)
    }
    if (!plan) {
        throw new BadRequestError(ErrorMessage.PlanNotFound)
    }
    const wsPlan = WorkspacePlanPriceIdMap[plan.id]
    if (!wsPlan) {
        throw new BadRequestError(ErrorMessage.PlanNotFound)
    }
    const s2 = new SubscriptionService()
    let subscription = await s2.findOne({
        workspaceId: workspace.id,
        stripeSubscriptionId: id
    })
    if (!subscription) {
        throw new BadRequestError(ErrorMessage.SubscriptionNotFound)
    }
    const up1: Partial<Subscription> = {
        status: status as SubscriptionStatus
    }
    await s2.update({
        workspaceId: workspace.id,
        stripeSubscriptionId: id
    }, up1)
    subscription = {...subscription, ...up1}

    const s3 = new BillingCycleService()
    let billingCycle = await s3.findOne({
        stripeSubscriptionId: id,
        isActive: true
    })
    if (billingCycle) {
        const up2: Partial<BillingCycle> = {
            isRenewing
        }
        await s3.update({
            stripeSubscriptionId: id,
            isActive: true
        }, up2)
        billingCycle = {...billingCycle, ...up2}

        await UpdateWorkspaceSubscription(billingCycle, subscriptionData)
    }
    return {
        subscription,
        billingCycle
    }
}

export const handleSubscriptionCancelled = async (subscriptionData: Stripe.Subscription) => {
    // mark the subscription as cancelled, end the billing cycle.
    // for transitional ie upgrade or downgrade, we'll create a free billing cycle which will get replaced by new plan.
    const {id, customer, status, items, cancel_at_period_end, ...rest} = subscriptionData

    const s = new WorkspaceService()
    const workspace = await s.findOne({stripeCustomerId: customer as string})
    if (!workspace) {
        throw new BadRequestError(ErrorMessage.WorkspaceNotFound)
    }
    const s2 = new SubscriptionService()
    let subscription = s2.findOne({
        workspaceId: workspace.id,
        stripeSubscriptionId: id
    })
    if (!subscription) {
        throw new BadRequestError(ErrorMessage.SubscriptionNotFound)
    }
    const up1: Partial<Subscription> = {
        status: status as SubscriptionStatus,
        endedAt: new Date()
    }
    await s2.update({
        workspaceId: workspace.id,
        stripeSubscriptionId: id
    }, up1)
    subscription = {...subscription, ...up1}

    const s3 = new BillingCycleService()
    let billingCycle = await s3.findOne({
        stripeSubscriptionId: id,
        isActive: true,
        workspaceId: workspace.id
    })
    if (billingCycle) {
        const res = await startBillingCycle(workspace.id)
        billingCycle = res.billingCycle
    } else {
        await s3.update({
                workspaceId: workspace.id,
                futureStripeSubscriptionId: id
            }, {
                futureStripeSubscriptionId: '',
                futurePriceId: ''
            }
        )
    }
    return {
        subscription,
        billingCycle
    }
}

export interface Billable extends Pick<WorkspaceUsage, 'workspaceId' | 'emailSent' | 'aiGeneration' | 'enrichment' | 'workflowTask'> {

}

interface BillCreditLog {
    id: number,
    amountInCents: number
}

export interface BillingSummary {
    workspaceId: string,
    billingCycleId: string,
    creditBilled: BillCreditLog[],
    billable: Billable,
    cycleBillable: Billable,
    perUseBillable: Billable,
    perUseCostInCents: number,
    availableCreditInCents: number,
    PayPerUsePricingInCents: typeof PayPerUsePricing
    transactionId: number
}


export const billWorkspaceCredit = async (billable: Billable) => {
    const {workspaceId} = billable

    const billingCycle = await CurrentBillingCycle(workspaceId)
    if (!billingCycle) {
        throw new BadRequestError(ErrorMessage.EntityNotFound)
    }
    const creditSummary = await ActiveCredits(workspaceId)

    const cycleBillable: Billable = {
        workspaceId, aiGeneration: 0, enrichment: 0, emailSent: 0, workflowTask: 0
    }
    const perUseBillable: Billable = {
        ...billable
    }
    if (!billingCycle.cyclePlanQuota.workflowTask) {
        billingCycle.cyclePlanQuota.workflowTask = WorkspacePlanPriceIdMap[billingCycle.planId].limits.workflowTask || 100
    }
    if (!billingCycle.cycleUsage.workflowTask) {
        billingCycle.cycleUsage.workflowTask = 0
    }

    const billingCycleRemainingQuota: Billable = {
        workspaceId,
        aiGeneration: Math.max(0, billingCycle.cyclePlanQuota.aiGeneration - billingCycle.cycleUsage.aiGeneration),
        enrichment: Math.max(0, billingCycle.cyclePlanQuota.enrichment - billingCycle.cycleUsage.enrichment),
        emailSent: Math.max(0, billingCycle.cyclePlanQuota.emails - billingCycle.cycleUsage.emails),
        workflowTask: Math.max(0, billingCycle.cyclePlanQuota.workflowTask - billingCycle.cycleUsage.workflowTask),
    };
    if (billingCycleRemainingQuota.aiGeneration > 0 && perUseBillable.aiGeneration > 0) {
        const aiToBill = Math.min(perUseBillable.aiGeneration, billingCycleRemainingQuota.aiGeneration);
        cycleBillable.aiGeneration += aiToBill;
        perUseBillable.aiGeneration -= aiToBill;
    }
    if (billingCycleRemainingQuota.enrichment > 0 && perUseBillable.enrichment > 0) {
        const enrichmentToBill = Math.min(perUseBillable.enrichment, billingCycleRemainingQuota.enrichment);
        cycleBillable.enrichment += enrichmentToBill;
        perUseBillable.enrichment -= enrichmentToBill;
    }
    if (billingCycleRemainingQuota.emailSent > 0 && perUseBillable.emailSent > 0) {
        const emailsToBill = Math.min(perUseBillable.emailSent, billingCycleRemainingQuota.emailSent);
        cycleBillable.emailSent += emailsToBill;
        perUseBillable.emailSent -= emailsToBill;
    }
    if (billingCycleRemainingQuota.workflowTask > 0 && perUseBillable.workflowTask > 0) {
        const tasksToBill = Math.min(perUseBillable.workflowTask, billingCycleRemainingQuota.workflowTask);
        cycleBillable.workflowTask += tasksToBill;
        perUseBillable.workflowTask -= tasksToBill;
    }
    const perUseCostInCents = PayPerUsePricingInCents.aiGeneration * perUseBillable.aiGeneration
        + PayPerUsePricingInCents.email * perUseBillable.emailSent
        + PayPerUsePricingInCents.enrichment * perUseBillable.enrichment
        + PayPerUsePricingInCents.workspaceTask * perUseBillable.workflowTask

    if (creditSummary.availableCreditInCents < perUseCostInCents) {
        throw new BadRequestError(ErrorMessage.NotEnoughCredit)
    }
    // add usage to the billing cycle, add log for it
    // calculate the per usage cost
    // bill that to the credit, as user can have multiple purchased credit, split the total cost across the credits,
    //  record the split,
    //  bill the credit, also log for it
    // add to workspace transaction, the whole summary of the billing
    // add workspace usage

    const s = new BillingCycleService()
    await s.addUsage(String(billingCycle.id), billable, `Add usage: ${JSON.stringify(cycleBillable)}`)

    const creditBilled: BillCreditLog[] = []
    let creditRemainingToBilInCents = perUseCostInCents

    const s2 = new WorkspaceCreditService()
    for (const credit of creditSummary.credits) {
        if (creditRemainingToBilInCents <= 0) { // it will be 0
            break;
        }
        const toBill = credit.creditRemainingInCents > creditRemainingToBilInCents ? creditRemainingToBilInCents : credit.creditRemainingInCents;
        creditRemainingToBilInCents -= toBill

        await s2.billCredit(credit.id, toBill, `Full/Partial in cents billing: ${toBill} of ${perUseCostInCents}`)
        creditBilled.push({
            id: credit.id,
            amountInCents: toBill
        })
    }

    const now = new Date()
    const s3 = new WorkspaceUsageService()
    await s3.addUsage({
        ...billable,
        hour: now.getHours(),
        day: now.getDate(),
        month: now.getMonth() + 1,
        year: now.getFullYear(),
        creditBilledInCents: perUseCostInCents
    })

    const summary: BillingSummary = {
        creditBilled,
        billable,
        cycleBillable,
        perUseBillable,
        perUseCostInCents,
        availableCreditInCents: creditSummary.availableCreditInCents,
        PayPerUsePricingInCents,
        workspaceId,
        billingCycleId: String(billingCycle.id),
        transactionId: 0
    }
    const s4 = new WorkspaceBillableTransactionService()
    const t = await s4.insert({
        workspaceId,
        debitAmountInCents: perUseCostInCents,
        summary: JSON.stringify(summary)
    })
    summary.transactionId = t.id

    return {summary}
}

export const refundWorkspaceCredit = async (summary: BillingSummary, reason: string) => {
    const {creditBilled, perUseCostInCents, billable, workspaceId, billingCycleId, transactionId} = summary;

    // Reverse the billing cycle usage by applying negative values
    const cycleBillable = {
        ...summary.cycleBillable,
        aiGeneration: -summary.cycleBillable.aiGeneration - summary.perUseBillable.aiGeneration,
        enrichment: -summary.cycleBillable.enrichment - summary.perUseBillable.enrichment,
        emailSent: -summary.cycleBillable.emailSent - summary.perUseBillable.emailSent,
        workflowTask: -summary.cycleBillable.workflowTask - summary.perUseBillable.workflowTask,
    };
    const s = new BillingCycleService()
    await s.addUsage(billingCycleId, cycleBillable, `Refund Workspace Credit usage: ${JSON.stringify(cycleBillable)}`)


    // Reverse the credit billing by applying negative values
    const s2 = new WorkspaceCreditService();
    for (const billedCredit of creditBilled) {
        const {amountInCents, id} = billedCredit
        await s2.billCredit(id, -amountInCents, `Full/Partial refund in cent of billing: ${amountInCents} of ${perUseCostInCents}`);
    }

    const now = new Date()

    // Reverse the workspace usage entry by removing it
    const s3 = new WorkspaceUsageService();
    await s3.addUsage({
        workspaceId,
        emailSent: -billable.emailSent,
        aiGeneration: -billable.aiGeneration,
        enrichment: -billable.enrichment,
        workflowTask: -billable.workflowTask,
        hour: now.getHours(),
        day: now.getDate(),
        month: now.getMonth() + 1,
        year: now.getFullYear(),
        creditBilledInCents: -perUseCostInCents
    });

    // Reverse the workspace billable transaction entry by creating a new transaction with the opposite
    const s4 = new WorkspaceBillableTransactionService();
    await s4.insert({
        workspaceId,
        debitAmountInCents: -perUseCostInCents,
        summary: JSON.stringify({...summary, reversal: true, reason})
    })

    // Return the refunded summary
    return {summary: summary};
}

export const handleStripeCheckoutSessionCompleted = async (data: Stripe.Checkout.Session) => {
    const {id, client_reference_id, ...rest} = data
    const metadata = rest.metadata as StripeCheckoutSessionMetadata

    const expanded = await stripe.checkout.sessions.retrieve(id, {
        expand: ['payment_intent', 'payment_intent.latest_charge', 'payment_intent.latest_charge.balance_transaction'],
    });
    const intent = expanded.payment_intent as Stripe.PaymentIntent
    const charge = intent?.latest_charge as Stripe.Charge
    const transaction = charge?.balance_transaction as Stripe.BalanceTransaction

    // console.log('Session Data', JSON.stringify(data, null, '\t'))
    // console.log('Expanded', JSON.stringify({intent, charge, transaction}, null, '\t'))

    const fee = transaction?.fee
    console.log("Fee:", fee)


    if (metadata.type === 'template_purchase') {
        await HandleTemplatePurchaseCompletedWebhook(client_reference_id, PaymentProcessor.Stripe, id, fee, fee)
    }
}


export const handleStripeCheckoutSessionExpired = async (data: Stripe.Checkout.Session) => {
    const {id, client_reference_id, ...rest} = data
    const metadata = rest.metadata as StripeCheckoutSessionMetadata

    if (metadata.type === 'template_purchase') {
        await HandleTemplatePurchaseAbandonedWebhook(client_reference_id, PaymentProcessor.Stripe, id)
    }
}

export const handleStripeChargeUpdated = async (data: Stripe.Charge) => {
    const {id, ...rest} = data
    // const metadata = rest.metadata as StripeCheckoutSessionMetadata
    //
    const expanded = await stripe.charges.retrieve(id, {
        expand: ['payment_intent', 'balance_transaction', 'invoice.subscription'],
    });


    // charge.updated
    // console.log('Charge Data', JSON.stringify(data, null, '\t'))

    // if (metadata.type === 'template_purchase') {
    //     const s = new TemplatePurchaseService()
    //     const purchase = await s.findOne({id: Number(client_reference_id)})
    //     if (!purchase) throw new NotfoundError(ErrorMessage.EntityNotFound)
    //
    //     // const purchase
    // }
}

interface EligiblePayout {
    creatorId: string
    totalEarningsInCents: number
    purchasedIds: string
}

export const GetEligibleCreatorPayouts = async (eligibleDays: number = CreatorSettings.PurchaseEligibilityForPayoutDays, minPayoutBalance: number = CreatorSettings.PayoutMinBalance): Promise<EligiblePayout[]> => {
    const s = new TemplatePurchaseService()

    // modify such that, all the associated tP.id are joined into a comma separated list as purchasedIds, also modify where now is 14 days after tP.purchaseAt
    const qB = s.getRepository().createQueryBuilder("tP")
        .select('t.creatorId', 'creatorId')
        .addSelect('SUM(tP.creatorEarningsInCents)', 'totalEarningsInCents')
        .addSelect("GROUP_CONCAT(tP.id)", 'purchasedIds') // Add comma-separated list of tP.id
        .leftJoin(Template, 't', 't.id=tP.templateId')
        .where({
            creatorEarningsInCents: MoreThan(0),
            payoutId: IsNull(),
            purchaseStatus: PurchaseStatus.Settled,
            purchasedAt: LessThanOrEqual(datePlusDays(new Date(), -eligibleDays))
        })
        .groupBy("t.creatorId")
        .having("totalEarningsInCents > :minPayout", {minPayout: minPayoutBalance})

    return await qB.getRawMany()
}

export const CheckTemplatePurchasesAndPrepareCreatorPayout = async (eligibleDays: number = CreatorSettings.PurchaseEligibilityForPayoutDays, minPayoutBalance: number = CreatorSettings.PayoutMinBalance) => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.CHECK_TEMPLATE_PURCHASES_PREPARE_PAYOUT)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }

    const eligiblePayouts: EligiblePayout[] = await GetEligibleCreatorPayouts(eligibleDays, minPayoutBalance)

    const s = new PayoutService()

    // let entityManager = s.getRepository().manager
    const queryRunner = s.getRepository().queryRunner || dbDataSource.createQueryRunner();
    const entityManager = queryRunner.manager
    const hasExistingTransaction = entityManager.queryRunner.isTransactionActive

    if (!hasExistingTransaction) {
        await entityManager.queryRunner.startTransaction()
    }
    // const entityManager = s.getRepository().queryRunner.manager
    // s2.getRepository().queryRunner = s.getRepository().queryRunner

    const payouts: Payout[] = []
    for (const eligible of eligiblePayouts) {
        const payout = await s.insert({
            creatorId: eligible.creatorId,
            amountInCents: eligible.totalEarningsInCents,
            payoutStatus: PayoutStatus.Pending,
        })
        await entityManager.update(TemplatePurchase, {id: In(eligible.purchasedIds.split(','))}, {payoutId: payout.id})
        payouts.push(payout)
    }
    if (!hasExistingTransaction && entityManager.queryRunner.isTransactionActive) {
        await entityManager.queryRunner.commitTransaction()
    }

    const creatorIds = payouts.map(p => p.creatorId)
    const creators = await new CreatorService().find({id: In(creatorIds)})

    for (let payout of payouts) {
        const creator = creators.find(c => c.id === payout.creatorId)
        if (!creator) {
            consoleLog("Creator for payout not found", {payout})
            continue
        }
        const members = await GetCreatorMembersNoAuth(creator.id)
        const owner = members.find(m => m.creatorMember.role === CreatorMemberRole.Owner)

        const to: EmailUser = {
            email: owner.user.email,
            name: `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim()
        }
        const cc = members
            .filter(m => m.creatorMember.role !== CreatorMemberRole.Owner)
            .map(m => m.user.email)
        const bcc: string[] = ['<EMAIL>']

        const subject = `Your $${Math.round(payout.amountInCents / 100)} payout for ${creator.name} is on the way`
        const message = `
        Hello ${creator.name}, <br/> Your payout of $${Math.round(payout.amountInCents / 100)} is on the way.<br/>
        If you haven't received your payout within 5 business days from the date of this email, please contact <NAME_EMAIL>.
        `
        const button = {
            label: 'View Payouts →',
            url: appUrl(`/creators/${creator.domain}/payouts`)
        }
        const messageId = await SendEmailWithContent(to, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, cc, bcc)
    }
    await redisReleaseLock(CRON_LOCK_KEYS.CHECK_TEMPLATE_PURCHASES_PREPARE_PAYOUT)

    return {payouts, eligiblePayouts}

}


interface EligibleAffiliatePayout {
    affiliateId: string
    totalEarningsInCents: number
    earningIds: string
}

export const GetEligibleAffiliatePayouts = async (eligibleDays: number = CreatorSettings.PurchaseEligibilityForPayoutDays, minPayoutBalance: number = CreatorSettings.PayoutMinBalance): Promise<EligibleAffiliatePayout[]> => {
    const s = new AffiliateEarningService()

    // modify such that, all the associated tP.id are joined into a comma separated list as purchasedIds, also modify where now is 14 days after tP.purchaseAt
    const qB = s.getRepository().createQueryBuilder("aE")
        .select('aE.affiliateId', 'affiliateId')
        .addSelect('SUM(aE.earningsInCents)', 'totalEarningsInCents')
        .addSelect("GROUP_CONCAT(aE.id)", 'earningIds')
        .where({
            earningsInCents: MoreThan(0),
            payoutId: IsNull(),
            createdAt: LessThanOrEqual(datePlusDays(new Date(), -eligibleDays))
        })
        .groupBy("aE.affiliateId")
        .having("totalEarningsInCents > :minPayout", {minPayout: minPayoutBalance})

    return await qB.getRawMany()
}

export const CheckAffiliateEarningsAndPreparePayout = async (eligibleDays: number = CreatorSettings.PurchaseEligibilityForPayoutDays, minPayoutBalance: number = CreatorSettings.PayoutMinBalance) => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.CHECK_AFFILIATE_EARNINGS_PREPARE_PAYOUT)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }

    const eligiblePayouts: EligibleAffiliatePayout[] = await GetEligibleAffiliatePayouts(eligibleDays, minPayoutBalance)

    const s = new PayoutService()

    const queryRunner = s.getRepository().queryRunner || dbDataSource.createQueryRunner();
    const entityManager = queryRunner.manager
    const hasExistingTransaction = entityManager.queryRunner.isTransactionActive

    if (!hasExistingTransaction) {
        await entityManager.queryRunner.startTransaction()
    }

    const payouts: Payout[] = []
    for (const eligible of eligiblePayouts) {
        const payout = await s.insert({
            affiliateId: eligible.affiliateId,
            amountInCents: eligible.totalEarningsInCents,
            payoutStatus: PayoutStatus.Pending,
        })
        await entityManager.update(AffiliateEarning, {id: In(eligible.earningIds.split(','))}, {payoutId: payout.id})
        payouts.push(payout)
    }
    if (!hasExistingTransaction && entityManager.queryRunner.isTransactionActive) {
        await entityManager.queryRunner.commitTransaction()
    }

    const affiliateIds = payouts.map(p => p.affiliateId)
    const qB = new AffiliateService().getRepository().createQueryBuilder("a")
        .addSelect('u')
        .leftJoin(User, 'u', 'u.id=a.userId')
        .where({
            id: In(affiliateIds)
        })
    const result = await qB.getRawMany()
    const affiliates = result.map(r => {
        const res: { affiliate: Affiliate, user: User } = {
            affiliate: {} as Affiliate,
            user: {} as User
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "u") {
                res.user[field] = r[key]
            } else if (pre === "a") {
                res.affiliate[field] = r[key]
            }
        }
        return res
    })

    for (let payout of payouts) {
        const affiliate = affiliates.find(c => c.affiliate.id === payout.affiliateId)
        if (!affiliate) {
            consoleLog("Affiliate for payout not found", {payout})
            continue
        }
        const {user} = affiliate
        const to: EmailUser = {
            email: user.email,
            name: `${user.firstName || ''} ${user.lastName || ''}`.trim()
        }
        const bcc: string[] = ['<EMAIL>']

        const subject = `Your $${Math.round(payout.amountInCents / 100)} payout for your affiliate earnings is on the way`
        const message = `
        Hello ${to.name}, <br/> Your payout of $${Math.round(payout.amountInCents / 100)} is on the way.<br/>
        If you haven't received your payout within 5 business days from the date of this email, please contact <NAME_EMAIL>.
        `
        const button = {
            label: 'View Payouts →',
            url: appUrl(`/welcome/referral`)
        }
        const messageId = await SendEmailWithContent(to, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, undefined, bcc)
    }
    await redisReleaseLock(CRON_LOCK_KEYS.CHECK_AFFILIATE_EARNINGS_PREPARE_PAYOUT)

    return {payouts, eligiblePayouts}
}

export const ModifyAddOnUsersInSubscription = async (workspaceId: string, stripeSubscriptionId: string, addOnUsers: number) => {
    const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);
    if (!subscription) throw new UnauthorizedError(ErrorMessage.SubscriptionNotFound)
    // console.log({subscription})
    console.log("Items:", JSON.stringify(subscription.items, null, '\t'))

    let plan: WorkspacePlan | undefined = undefined
    let subPriceId: string = undefined
    let hasUserAddOn = false
    let userAddOnPriceId: string = undefined
    let userAddOnItemId: string = undefined

    for (let datum of subscription.items.data) {
        const itemId = datum.id
        const priceId = datum.price.id

        if (WorkspacePlanPriceIdMap[priceId]) {
            plan = WorkspacePlanPriceIdMap[priceId]
            subPriceId = priceId
        }
        if (WorkspaceAddOns.users.commitments.annual.priceId === priceId) {
            hasUserAddOn = true
            userAddOnPriceId = priceId
            userAddOnItemId = itemId
        } else if (WorkspaceAddOns.users.commitments.monthToMonth.priceId === priceId) {
            hasUserAddOn = true
            userAddOnPriceId = priceId
            userAddOnItemId = itemId
        }
    }
    if (!plan) throw new UnauthorizedError(ErrorMessage.SubscriptionNotFound)

    let newSubscription: Stripe.Subscription = undefined

    console.log({hasUserAddOn, addOnUsers, userAddOnPriceId})

    if (hasUserAddOn && userAddOnPriceId && userAddOnItemId) {
        const item: Stripe.SubscriptionUpdateParams.Item = {
            price: userAddOnPriceId,
            quantity: addOnUsers,
            id: userAddOnItemId
        }
        newSubscription = await stripe.subscriptions.update(stripeSubscriptionId, {
            items: [item],
            proration_behavior: "always_invoice",
            payment_behavior: "error_if_incomplete"
        });
    } else {
        let priceId = ''
        if (plan.commitments.monthToMonth.priceId === subPriceId) {
            priceId = WorkspaceAddOns.users.commitments.monthToMonth.priceId
        } else priceId = WorkspaceAddOns.users.commitments.annual.priceId

        const item: Stripe.SubscriptionUpdateParams.Item = {
            price: priceId,
            quantity: addOnUsers
        }
        newSubscription = await stripe.subscriptions.update(stripeSubscriptionId, {
            items: [item],
            proration_behavior: "always_invoice",
            payment_behavior: "error_if_incomplete"
        });
    }
    const billingCycle = await CurrentBillingCycle(workspaceId)

    console.log('New subscription:', JSON.stringify(newSubscription, null, '\t'))

    const response = await UpdateWorkspaceSubscription(billingCycle, newSubscription)
    const workspace = await GetWorkspaceById(workspaceId)
    await RestoreWorkspaceLimitedFunctionality(workspace)
    return response
}

export const RefreshWorkspaceSubscription = async (workspaceId: string) => {
    const billingCycle = await CurrentBillingCycle(workspaceId)
    if (!billingCycle.stripeSubscriptionId) {
        throw new UnauthorizedError(ErrorMessage.SubscriptionNotActive)
    }
    const subscription = await stripe.subscriptions.retrieve(billingCycle.stripeSubscriptionId);
    if (!subscription) throw new UnauthorizedError(ErrorMessage.SubscriptionNotFound)

    return await UpdateWorkspaceSubscription(billingCycle, subscription)
}

export const UpdateWorkspaceSubscription = async (billingCycle: BillingCycle, subscription: Stripe.Subscription): Promise<{ billingCycle: BillingCycle, subscription: Subscription }> => {
    const s = new SubscriptionService()
    const bC = new BillingCycleService()

    const wspSub = await s.findOne({workspaceId: billingCycle.workspaceId, stripeSubscriptionId: billingCycle.stripeSubscriptionId})
    if (!wspSub) throw new UnauthorizedError(ErrorMessage.SubscriptionNotFound)

    let addOnUsers = 0;

    for (let datum of subscription.items.data) {
        const priceId = datum.price.id
        const quantity = datum.quantity

        if (WorkspaceAddOns.users.commitments.annual.priceId === priceId) {
            addOnUsers = quantity
        } else if (WorkspaceAddOns.users.commitments.monthToMonth.priceId === priceId) {
            addOnUsers = quantity
        }
    }
    await s.update({id: wspSub.id}, {addOnUsers})

    const addOnsQuota: AddOnsQuota = {
        collaborators: addOnUsers * WorkspaceAddOns.users.extra.collaborators,
        records: addOnUsers * WorkspaceAddOns.users.extra.records,
        sendingDomains: 0,
        sendingEmails: addOnUsers * WorkspaceAddOns.users.extra.emails,
        users: addOnUsers,
        aiGeneration: addOnUsers * WorkspaceAddOns.users.extra.aiGeneration,
        enrichment: addOnUsers * WorkspaceAddOns.users.extra.enrichment,
        workflowTask: addOnUsers * WorkspaceAddOns.users.extra.workflowTasks
    }
    await bC.update({id: billingCycle.id}, {addOnsQuota})

    return {
        billingCycle: {...billingCycle, addOnsQuota},
        subscription: {...wspSub, addOnUsers}
    }
}

export const FindExistingCoupon = async (
    percentOff: number,
    durationMonths: number
): Promise<string | null> => {
    const coupons = await stripe.coupons.list({limit: 100});

    for (const coupon of coupons.data) {
        if (
            coupon.percent_off === percentOff &&
            coupon.duration === "repeating" &&
            coupon.duration_in_months === durationMonths
        ) {
            return coupon.id; // Reuse this coupon
        }
    }
    return null; // No matching coupon found
};

/**
 * Retrieves or creates a coupon dynamically based on discount percentage and duration.
 */
export const GetOrCreateCoupon = async (
    percentOff: number,
    durationMonths: number
): Promise<string> => {
    let couponId = await FindExistingCoupon(percentOff, durationMonths);

    if (couponId) {
        console.log(`Reusing existing coupon: ${couponId}`);
        return couponId;
    }

    // Create a new coupon if none exist
    const coupon = await stripe.coupons.create({
        percent_off: percentOff,
        duration: "repeating",
        duration_in_months: durationMonths,
    });

    console.log(`Created new coupon: ${coupon.id}`);
    return coupon.id;
};




