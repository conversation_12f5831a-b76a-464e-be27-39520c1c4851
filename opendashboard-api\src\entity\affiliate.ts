import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

@Entity()
export class Affiliate {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: false, unique: true})
    referralCode: string

    @Column({type: 'varchar', nullable: false, unique: true})
    userId: string

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

    @Index()
    @Column({default: 10, type: 'int'})
    referralDiscountPercent: number

    @Index()
    @Column({default: 3, type: 'int'})
    referralExpiryMonths: number

    @Index()
    @Column({default: 10, type: 'int'})
    earningPercent: number

    @Index()
    @Column({default: 3, type: 'int'})
    earningExpiryMonths: number

    @Index()
    @Column({default: false, type: 'boolean'})
    isApproved: boolean

    @Index()
    @Column({type: 'timestamp', nullable: true})
    approvedAt: Date

    @Index()
    @Column({type: 'varchar', nullable: true})
    approvedByUserId: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}