import {BaseService} from "./service";
import {Repository} from "typeorm";
import {getRepository} from "../connection/db";
import {WorkflowAnalytic} from "../entity/workflowAnalytic";


export interface WorkflowTaskAnalytic {
    taskId: number
    views: number
    clicks: number
    unsubscribe: number
}

export interface WorkflowNodeAnalytic {
    nodeId: string
    views: number
    clicks: number
    unsubscribe: number
}

export class WorkflowAnalyticService extends BaseService<WorkflowAnalytic> {

    initRepository = (): Repository<WorkflowAnalytic> => {
        return getRepository(WorkflowAnalytic)
    }

    async getWorkflowInstanceAnalytics(workflowId: string, instanceId: number): Promise<WorkflowTaskAnalytic[]> {
        const qB = this.getRepository().createQueryBuilder("wA")
            .select("wA.taskId", "taskId")
            .addSelect("SUM(wA.pageViews)", "views")
            .addSelect("SUM(wA.clickCount)", "clicks")
            .addSelect("SUM(wA.unsubscribeCount)", "unsubscribe")
            .where({
                workflowId: workflowId,
                instanceId: instanceId,
            }).groupBy("taskId")

        return await qB.getRawMany()
    }

    async getWorkflowAnalytics(workflowId: string, uniqueAnalytics = true): Promise<WorkflowNodeAnalytic[]> {
        if (uniqueAnalytics) {
            const qB = this.getRepository().createQueryBuilder()
                .select('nodeId')
                .addSelect('SUM(pageViews)', 'views')
                .addSelect('SUM(clickCount)', 'clicks')
                .addSelect('SUM(unsubscribeCount)', 'unsubscribe')
                .from(qb => {
                    return qb.select('nodeId, outboundId, instanceId')
                        .addSelect(`CASE WHEN SUM(pageViews)>0 THEN 1 ELSE 0 END`, 'pageViews')
                        .addSelect(`CASE WHEN SUM(clickCount)>0 THEN 1 ELSE 0 END`, 'clickCount')
                        .addSelect(`CASE WHEN SUM(unsubscribeCount)>0 THEN 1 ELSE 0 END`, 'unsubscribeCount')
                        .from(WorkflowAnalytic, 'wA')
                        .where({workflowId})
                        .groupBy('nodeId, outboundId, instanceId')
                }, 'X')
                .groupBy('nodeId')
            qB.setParameters({
                workflowId
            })
            return await qB.getRawMany();
        }
        const qB = this.getRepository().createQueryBuilder("wA")
            .select("wA.nodeId", "nodeId")
            .addSelect("SUM(wA.pageViews)", "views")
            .addSelect("SUM(wA.clickCount)", "clicks")
            .addSelect("SUM(wA.unsubscribeCount)", "unsubscribe")
            .where({
                workflowId: workflowId,
            }).groupBy("nodeId")

        return await qB.getRawMany()
    }

}
