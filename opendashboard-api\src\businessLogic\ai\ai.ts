import {AIContentGenerationLog} from "../../entity/AIContentGenerationLog";
import {AIContentGenerationLogService} from "../../service/aiContentGenerationLog";
import {ErrorMessage, RequiredParameterError, ServerProcessingError} from "../../errors/AppError";
import {generateMessage, Haiku} from "./anthropic";
import {generateMessage as openAIGenerate, GPT4_O_Mini} from "./openai";
import OpenAI from "openai";
import {Billable, BillingSummary, billWorkspaceCredit, refundWorkspaceCredit} from "../billing";
import * as Sentry from "@sentry/node";
import {Log} from "../logtail";
import {encode} from "gpt-tokenizer";
import ChatModel = OpenAI.ChatModel;


export interface AIModelPricing {
    modelName: string;
    costInCentsPerInputToken: number
    costInCentsPerOutputToken: number
}

export enum AIProvider {
    OpenAI = "openai",
    Anthropic = "anthropic",
}

const DEFAULT_SYSTEM_PROMPT = `
    Generate high-quality written content tailored to the provided prompt and specifications. Carefully analyze the prompt parameters, desired output type, tone, length, formatting requirements, and any other provided instructions. Produce a clear, engaging, and well-structured response that strictly adheres to the given prompt. Adapt your language, style, and content as needed to fit the context and purpose outlined in the prompt.

    Maintain professionalism and do not provide outputs that raise ethical concerns.
    Follow the prompt strictly, avoid any statements, explanations, or inclusions beyond specifically addressing the prompt details with your generated output content.
    Do not provide any instruction/explanation to the user, such as "Here is the content you requested" or any similar form.
    Provide the final version of the content in the form that will be used either in the report/sent to the final recipient.
    Do not introduce any substitution tag such as {{name}} or {{your name}} or <name> or in any other form that will need to be replaced/removed eg Best regards, [Your Name].
    `

export const WORKFLOW_SYSTEM_PROMPT = `
    You are an AI integrated into a workflow automation system. Respond in either text or JSON, based on the request. If the user provides an example or structure, follow it exactly. If no format is specified, choose the most appropriate format based on the request.

    Guidelines:
    Text Responses: Provide clear, concise, and relevant answers in plain text.
    JSON Responses: Return valid, well-structured JSON. Follow any example or structure provided exactly.
    Do not include invalid characters (e.g., \\n, extra spaces, or malformed syntax) that could break JSON.
    Default Behavior:
    For descriptive or explanatory requests, use text.
    For requests needing structured data, use JSON.
    Example:
    User Request:
    "Here’s the format I need:
    
    json
    Copy
    {  
      'status': 'active',  
      'user_count': 1200  
    }  
    Please provide the current system status in this format."
    
    Your Response:
    
    json
    Copy
    {  
      'status': 'active',  
      'user_count': 1200  
    }  

    `

const TOKEN_PER_1K_WORDS = 1024;
const CONTENT_UNIT_WORDS = 5000;

export const generateAIContentAndLog = async (workspaceId: string, options: {
    systemPrompt?: string,
    userPrompt: string,
    provider?: AIProvider,
    metadata?: object,
    purpose: string,
    maxWords?: number
}, skipBilling = false) => {
    let provider = options.provider || Math.round(Math.random()) === 0 ? AIProvider.OpenAI : AIProvider.Anthropic
    let systemPrompt = options.systemPrompt || DEFAULT_SYSTEM_PROMPT
    let metadata = options.metadata || {}
    let purpose = options.purpose || ''
    let providerReference = ''
    let modelName = ''
    let userPrompt = options.userPrompt || ''
    let contentGenerated = ''
    let providerResponse = ''
    let inputTokens = 0
    let outputTokens = 0
    let estimatedCostInCents = 0

    let maxWords = Math.max(options.maxWords || 1000, 1000)
    const maxTokens = getMaxTokens(maxWords)

    const estInputTokenCount = countTokens(userPrompt)
    const estInputWordCount = getWordsCount(estInputTokenCount)

    const contentUnits = Math.max(
        Math.ceil(estInputWordCount / CONTENT_UNIT_WORDS),
        Math.ceil(maxWords / CONTENT_UNIT_WORDS)
    )

    metadata = {
        ...metadata,
        maxTokens,
        maxWords,
        estInputWordCount,
        estInputTokenCount,
        contentUnits,
        CONTENT_UNIT_WORDS
    }

    if (!userPrompt) throw new RequiredParameterError("Prompt")

    let summary: BillingSummary;
    if (!skipBilling) {
        const billable: Billable = {
            workspaceId,
            aiGeneration: contentUnits,
            emailSent: 0,
            enrichment: 0,
            workflowTask: 0
        }
        const r = await billWorkspaceCredit(billable);
        summary = r.summary
    }


    try {
        if (provider === AIProvider.Anthropic) {
            const msg = await generateMessage(userPrompt, {system: systemPrompt, model: Haiku.modelName, max_tokens: maxTokens})
            providerResponse = JSON.stringify(msg)
            providerReference = msg.id
            modelName = Haiku.modelName
            contentGenerated = msg.content[0].text
            inputTokens = msg.usage.input_tokens
            outputTokens = msg.usage.output_tokens
            estimatedCostInCents = inputTokens * Haiku.costInCentsPerInputToken + outputTokens * Haiku.costInCentsPerOutputToken

        } else if (provider === AIProvider.OpenAI) {
            const msg = await openAIGenerate(userPrompt, {system: systemPrompt, model: GPT4_O_Mini.modelName as ChatModel, max_tokens: maxTokens})
            providerResponse = JSON.stringify(msg)
            providerReference = msg.id
            modelName = GPT4_O_Mini.modelName
            contentGenerated = msg.choices[0].message.content
            inputTokens = msg.usage.prompt_tokens
            outputTokens = msg.usage.completion_tokens
            estimatedCostInCents = inputTokens * GPT4_O_Mini.costInCentsPerInputToken + outputTokens * GPT4_O_Mini.costInCentsPerOutputToken

        } else throw new ServerProcessingError(ErrorMessage.EntityNotFound)

    } catch (e) {
        Sentry.captureException(e)
        await Log.error(e)

        if (!skipBilling) await refundWorkspaceCredit(summary, `Exception when generating content: ${e.message}`)

        throw new ServerProcessingError(ErrorMessage.UnableToProcessRequestTryAgainLater)
    }

    const d: Partial<AIContentGenerationLog> = {
        workspaceId,
        provider,
        providerReference,
        modelName,
        systemPrompt,
        userPrompt,
        contentGenerated,
        providerResponse,
        inputTokens,
        outputTokens,
        estimatedCostInCents,
        meta: {purpose, ...metadata}
    }

    const s = new AIContentGenerationLogService();
    const aiContentLog = await s.insert(d)

    return {
        aiContentLog
    }
}

const countTokens = (prompt: string) => {
    const tokens = encode(prompt)

    return tokens.length
}

const getMaxTokens = (maxWords: number) => {
    // 1000 words = 1024 tokens
    // Y = (1024 * maxWords) / 1000
    return Math.ceil((1024 * maxWords) / 1000); // Rounds up to the nearest whole token
};

const getWordsCount = (tokenCount: number) => {
    // 1024 tokens = 1000 words
    // tokenCount tokens = Y words
    // Y = (1000 * tokenCount) / 1024
    return Math.ceil((1000 * tokenCount) / 1024); // Rounds up to the nearest whole word
};
