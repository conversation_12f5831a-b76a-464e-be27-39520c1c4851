"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-rehype";
exports.ids = ["vendor-chunks/remark-rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-rehype/index.js":
/*!*********************************************!*\
  !*** ./node_modules/remark-rehype/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* reexport safe */ mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_1__.all),\n/* harmony export */   \"default\": () => (/* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   defaultHandlers: () => (/* reexport safe */ mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.handlers),\n/* harmony export */   one: () => (/* reexport safe */ mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_1__.one)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\");\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/**\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').Processor} Processor\n */\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVtYXJrLXJlaHlwZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLGtDQUFrQztBQUMvQyxhQUFhLG9DQUFvQztBQUNqRDs7QUFFNEQ7O0FBRXRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZW1hcmstcmVoeXBlL2luZGV4LmpzP2I5NTYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuL2xpYi9pbmRleC5qcycpLk9wdGlvbnN9IE9wdGlvbnNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vbGliL2luZGV4LmpzJykuUHJvY2Vzc29yfSBQcm9jZXNzb3JcbiAqL1xuXG5leHBvcnQge2RlZmF1bHRIYW5kbGVycywgYWxsLCBvbmV9IGZyb20gJ21kYXN0LXV0aWwtdG8taGFzdCdcblxuZXhwb3J0IHtkZWZhdWx0fSBmcm9tICcuL2xpYi9pbmRleC5qcydcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-rehype/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/remark-rehype/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/remark-rehype/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\");\n/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('mdast-util-to-hast').Options} Options\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n *\n * @typedef {import('mdast-util-to-hast')} DoNotTouchAsThisImportIncludesRawInTree\n */\n\n\n\n// Note: the `<MdastRoot, HastRoot>` overload doesn’t seem to work :'(\n\n/**\n * Plugin that turns markdown into HTML to support rehype.\n *\n * *   If a destination processor is given, that processor runs with a new HTML\n *     (hast) tree (bridge-mode).\n *     As the given processor runs with a hast tree, and rehype plugins support\n *     hast, that means rehype plugins can be used with the given processor.\n *     The hast tree is discarded in the end.\n *     It’s highly unlikely that you want to do this.\n * *   The common case is to not pass a destination processor, in which case the\n *     current processor continues running with a new HTML (hast) tree\n *     (mutate-mode).\n *     As the current processor continues with a hast tree, and rehype plugins\n *     support hast, that means rehype plugins can be used after\n *     `remark-rehype`.\n *     It’s likely that this is what you want to do.\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `mdast-util-to-hast`.\n */\nconst remarkRehype =\n  /** @type {(import('unified').Plugin<[Processor, Options?]|[null|undefined, Options?]|[Options]|[], MdastRoot>)} */\n  (\n    function (destination, options) {\n      return destination && 'run' in destination\n        ? bridge(destination, options)\n        : mutate(destination || options)\n    }\n  )\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (remarkRehype);\n\n/**\n * Bridge-mode.\n * Runs the destination with the new hast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], MdastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run((0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(node, options), file, (error) => {\n      next(error)\n    })\n  }\n}\n\n/**\n * Mutate-mode.\n * Further plugins run on the hast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], MdastRoot, HastRoot>}\n */\nfunction mutate(options) {\n  // @ts-expect-error: assume a corresponding node is returned by `toHast`.\n  return (node) => (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(node, options)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVtYXJrLXJlaHlwZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLGFBQWEscUJBQXFCO0FBQ2xDLGFBQWEsc0JBQXNCO0FBQ25DLGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsaURBQWlEO0FBQzlEO0FBQ0EsYUFBYSw4QkFBOEI7QUFDM0M7O0FBRXlDOztBQUV6Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxzR0FBc0c7QUFDbkg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsWUFBWTs7QUFFM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDBEQUFNO0FBQzFCO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDBEQUFNO0FBQ3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZW1hcmstcmVoeXBlL2xpYi9pbmRleC5qcz85Mjg2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLlJvb3R9IEhhc3RSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlJvb3R9IE1kYXN0Um9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QtdXRpbC10by1oYXN0JykuT3B0aW9uc30gT3B0aW9uc1xuICogQHR5cGVkZWYge2ltcG9ydCgndW5pZmllZCcpLlByb2Nlc3NvcjxhbnksIGFueSwgYW55LCBhbnk+fSBQcm9jZXNzb3JcbiAqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdC11dGlsLXRvLWhhc3QnKX0gRG9Ob3RUb3VjaEFzVGhpc0ltcG9ydEluY2x1ZGVzUmF3SW5UcmVlXG4gKi9cblxuaW1wb3J0IHt0b0hhc3R9IGZyb20gJ21kYXN0LXV0aWwtdG8taGFzdCdcblxuLy8gTm90ZTogdGhlIGA8TWRhc3RSb290LCBIYXN0Um9vdD5gIG92ZXJsb2FkIGRvZXNu4oCZdCBzZWVtIHRvIHdvcmsgOicoXG5cbi8qKlxuICogUGx1Z2luIHRoYXQgdHVybnMgbWFya2Rvd24gaW50byBIVE1MIHRvIHN1cHBvcnQgcmVoeXBlLlxuICpcbiAqICogICBJZiBhIGRlc3RpbmF0aW9uIHByb2Nlc3NvciBpcyBnaXZlbiwgdGhhdCBwcm9jZXNzb3IgcnVucyB3aXRoIGEgbmV3IEhUTUxcbiAqICAgICAoaGFzdCkgdHJlZSAoYnJpZGdlLW1vZGUpLlxuICogICAgIEFzIHRoZSBnaXZlbiBwcm9jZXNzb3IgcnVucyB3aXRoIGEgaGFzdCB0cmVlLCBhbmQgcmVoeXBlIHBsdWdpbnMgc3VwcG9ydFxuICogICAgIGhhc3QsIHRoYXQgbWVhbnMgcmVoeXBlIHBsdWdpbnMgY2FuIGJlIHVzZWQgd2l0aCB0aGUgZ2l2ZW4gcHJvY2Vzc29yLlxuICogICAgIFRoZSBoYXN0IHRyZWUgaXMgZGlzY2FyZGVkIGluIHRoZSBlbmQuXG4gKiAgICAgSXTigJlzIGhpZ2hseSB1bmxpa2VseSB0aGF0IHlvdSB3YW50IHRvIGRvIHRoaXMuXG4gKiAqICAgVGhlIGNvbW1vbiBjYXNlIGlzIHRvIG5vdCBwYXNzIGEgZGVzdGluYXRpb24gcHJvY2Vzc29yLCBpbiB3aGljaCBjYXNlIHRoZVxuICogICAgIGN1cnJlbnQgcHJvY2Vzc29yIGNvbnRpbnVlcyBydW5uaW5nIHdpdGggYSBuZXcgSFRNTCAoaGFzdCkgdHJlZVxuICogICAgIChtdXRhdGUtbW9kZSkuXG4gKiAgICAgQXMgdGhlIGN1cnJlbnQgcHJvY2Vzc29yIGNvbnRpbnVlcyB3aXRoIGEgaGFzdCB0cmVlLCBhbmQgcmVoeXBlIHBsdWdpbnNcbiAqICAgICBzdXBwb3J0IGhhc3QsIHRoYXQgbWVhbnMgcmVoeXBlIHBsdWdpbnMgY2FuIGJlIHVzZWQgYWZ0ZXJcbiAqICAgICBgcmVtYXJrLXJlaHlwZWAuXG4gKiAgICAgSXTigJlzIGxpa2VseSB0aGF0IHRoaXMgaXMgd2hhdCB5b3Ugd2FudCB0byBkby5cbiAqXG4gKiBAcGFyYW0gZGVzdGluYXRpb25cbiAqICAgT3B0aW9uYWwgdW5pZmllZCBwcm9jZXNzb3IuXG4gKiBAcGFyYW0gb3B0aW9uc1xuICogICBPcHRpb25zIHBhc3NlZCB0byBgbWRhc3QtdXRpbC10by1oYXN0YC5cbiAqL1xuY29uc3QgcmVtYXJrUmVoeXBlID1cbiAgLyoqIEB0eXBlIHsoaW1wb3J0KCd1bmlmaWVkJykuUGx1Z2luPFtQcm9jZXNzb3IsIE9wdGlvbnM/XXxbbnVsbHx1bmRlZmluZWQsIE9wdGlvbnM/XXxbT3B0aW9uc118W10sIE1kYXN0Um9vdD4pfSAqL1xuICAoXG4gICAgZnVuY3Rpb24gKGRlc3RpbmF0aW9uLCBvcHRpb25zKSB7XG4gICAgICByZXR1cm4gZGVzdGluYXRpb24gJiYgJ3J1bicgaW4gZGVzdGluYXRpb25cbiAgICAgICAgPyBicmlkZ2UoZGVzdGluYXRpb24sIG9wdGlvbnMpXG4gICAgICAgIDogbXV0YXRlKGRlc3RpbmF0aW9uIHx8IG9wdGlvbnMpXG4gICAgfVxuICApXG5cbmV4cG9ydCBkZWZhdWx0IHJlbWFya1JlaHlwZVxuXG4vKipcbiAqIEJyaWRnZS1tb2RlLlxuICogUnVucyB0aGUgZGVzdGluYXRpb24gd2l0aCB0aGUgbmV3IGhhc3QgdHJlZS5cbiAqXG4gKiBAdHlwZSB7aW1wb3J0KCd1bmlmaWVkJykuUGx1Z2luPFtQcm9jZXNzb3IsIE9wdGlvbnM/XSwgTWRhc3RSb290Pn1cbiAqL1xuZnVuY3Rpb24gYnJpZGdlKGRlc3RpbmF0aW9uLCBvcHRpb25zKSB7XG4gIHJldHVybiAobm9kZSwgZmlsZSwgbmV4dCkgPT4ge1xuICAgIGRlc3RpbmF0aW9uLnJ1bih0b0hhc3Qobm9kZSwgb3B0aW9ucyksIGZpbGUsIChlcnJvcikgPT4ge1xuICAgICAgbmV4dChlcnJvcilcbiAgICB9KVxuICB9XG59XG5cbi8qKlxuICogTXV0YXRlLW1vZGUuXG4gKiBGdXJ0aGVyIHBsdWdpbnMgcnVuIG9uIHRoZSBoYXN0IHRyZWUuXG4gKlxuICogQHR5cGUge2ltcG9ydCgndW5pZmllZCcpLlBsdWdpbjxbT3B0aW9ucz9dfHZvaWRbXSwgTWRhc3RSb290LCBIYXN0Um9vdD59XG4gKi9cbmZ1bmN0aW9uIG11dGF0ZShvcHRpb25zKSB7XG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IGFzc3VtZSBhIGNvcnJlc3BvbmRpbmcgbm9kZSBpcyByZXR1cm5lZCBieSBgdG9IYXN0YC5cbiAgcmV0dXJuIChub2RlKSA9PiB0b0hhc3Qobm9kZSwgb3B0aW9ucylcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-rehype/lib/index.js\n");

/***/ })

};
;