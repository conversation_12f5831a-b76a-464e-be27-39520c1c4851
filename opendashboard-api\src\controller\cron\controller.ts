import {ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {CheckAffiliateEarningsAndPreparePayout, CheckAndHandleOverQuotaWorkspaces, CheckTemplatePurchasesAndPrepareCreatorPayout, renewDueBillingCycles} from "../../businessLogic/billing";
import {consoleLog} from "../../businessLogic/logtail";
import {GenerateAndPushAnalytics, PushExistingUsersToDatabase} from "../../businessLogic/providers/opendashboard";
import {SendExistingDataToInbrandedWorkflows} from "../../businessLogic/providers/inbranded";
import {ProcessDueReminders} from "../../businessLogic/reminder";
import {ExpireTemplatePurchasesAfterTimeout} from "../../businessLogic/templates";
import {FindAndTriggerScheduledWorkflows} from "../../businessLogic/runStarter/runStarter";
import {ProvisionOpendashboardSiteSender, TurnOffAllSupportAccess} from "../../businessLogic/workspace";

export class CronController {

    async billingRenewals(request: Request, response: Response, next: NextFunction) {
        renewDueBillingCycles().then(r => {
            consoleLog("Billing renewal completed:", r)
        }).catch(e => {
            consoleLog("Billing renewal cron error", e)
        })
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async checkAndHandleOverQuota(request: Request, response: Response, next: NextFunction) {
        CheckAndHandleOverQuotaWorkspaces().then(r => {
            consoleLog("Check and handle over quota workspaces renewal completed:", r)
        }).catch(e => {
            consoleLog("Check and handle over quota workspaces ", e)
        })
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async pushMetrics(request: Request, response: Response, next: NextFunction) {
        const allTime = !!request.query.allTime

        await GenerateAndPushAnalytics(allTime).then()

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async pushInbrandedWorkflowData(request: Request, response: Response, next: NextFunction) {
        SendExistingDataToInbrandedWorkflows().then()
        PushExistingUsersToDatabase().then()

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async prepareCreatorPayouts(request: Request, response: Response, next: NextFunction) {
        CheckTemplatePurchasesAndPrepareCreatorPayout(14).then(r => {
            consoleLog("Check template purchases and prepare creator payout completed:", {eligiblePayouts: r.eligiblePayouts})
        }).catch(e => {
            consoleLog("Check template purchases and prepare creator payout cron error", e)
        })

        CheckAffiliateEarningsAndPreparePayout(14).then(r => {
            consoleLog("Check affiliate earnings and prepare payout completed:", {eligiblePayouts: r.eligiblePayouts})
        }).catch(e => {
            consoleLog("Check affiliate earnings and prepare payout cron error", e)
        })

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async processReminders(request: Request, response: Response, next: NextFunction) {
        ProcessDueReminders().then(r => {
            consoleLog("Process due reminder cron completed:", r)
        }).catch(e => {
            consoleLog("Process due reminder cron error", e)
        })

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async expireTemplatePurchases(request: Request, response: Response, next: NextFunction) {
        ExpireTemplatePurchasesAfterTimeout().then(r => {
            consoleLog("Mark abandoned purchases cron completed:", r)
        }).catch(e => {
            consoleLog("Mark abandoned purchases cron error", e)
        })

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async triggerScheduledWorkflows(request: Request, response: Response, next: NextFunction) {
        FindAndTriggerScheduledWorkflows().then(r => {
            consoleLog("Find and trigger scheduled workflows cron completed:", r)
        }).catch(e => {
            consoleLog("Find and trigger scheduled workflows cron error", e)
        })

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async provisionOpendashboardSiteEmail(request: Request, response: Response, next: NextFunction) {
        const domain: string = String(request.query['domain'] || '')
        await ProvisionOpendashboardSiteSender(domain)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

    async turnOffSupportAccess(request: Request, response: Response, next: NextFunction) {
        const all: string = String(request.query['all'] || '')
        await TurnOffAllSupportAccess(!!all)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {}
        }
        return response.json(responseData)
    }

}



