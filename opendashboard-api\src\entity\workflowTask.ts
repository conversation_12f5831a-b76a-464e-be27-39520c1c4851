import {Column, CreateDate<PERSON>olumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {WorkflowTaskStatus} from "opendb-app-db-utils/lib/typings/workflow";

export interface CreateWorkflowTaskData {
    id: string
    workflowId: string
    instanceId: string
    nodeId: string
    taskOutput: object
    executeAt: Date
    meta?: object
}

export interface WorkflowTaskMeta {
    webhookId?: string
    errors?: string[]
}

@Entity()
export class WorkflowTask {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: "varchar"})
    workflowId: string

    @Index()
    @Column({type: "varchar"})
    instanceId: string

    @Index()
    @Column({type: "varchar"})
    nodeId: string

    @Index()
    @Column({type: 'timestamp', nullable: true})
    executeAt: Date

    @Column({type: 'json', nullable: true})
    taskOutput: object

    @Column({type: 'json', nullable: true})
    taskInput: object

    @Column({type: 'varchar', default: WorkflowTaskStatus.Scheduled})
    status: WorkflowTaskStatus

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    startedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    completedAt: Date

    @Index()
    @Column({type: 'int', default: 0})
    errorCount: number

    @Column({type: 'varchar', nullable: true})
    error: string

    @Index()
    @Column({default: false, type: 'boolean'})
    shouldRetry: boolean

    @Column({type: 'json', nullable: true})
    meta: WorkflowTaskMeta

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}


