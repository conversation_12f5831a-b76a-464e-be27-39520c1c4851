import {Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"

@Entity()
export class User {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: true})
    firstName: string

    @Column({type: 'varchar', nullable: true})
    lastName: string

    @Column({type: 'varchar', nullable: false, unique: true})
    email: string

    @Column({type: 'varchar', nullable: true})
    profilePhoto: string

    @Column({default: false, type: 'boolean'})
    isEmailVerified: boolean

    @Column({default: false, type: 'boolean'})
    isSetupCompleted: boolean

    @Column({default: false, type: 'boolean', select: false})
    isSupportAccount: boolean

    @Column({type: 'varchar', nullable: true})
    activeWorkspaceId: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}


