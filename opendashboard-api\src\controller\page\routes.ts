import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {PageController} from "./controller";

export const pageRoutes: Routes = {
    basePath: '/workspaces/:id/pages',
    middleware: [verifyJWT],
    routes: {
        '/': {
            get: {controller: <PERSON><PERSON>ontroller, action: "getPages"},
            post: {controller: PageController, action: "createPage"},
            patch: {controller: PageController, action: "updatePage"},
            delete: {controller: PageController, action: "deletePage"},
        },
        '/:pageId': {
            get: {controller: PageController, action: "getPage"},
        },
        // '/:pageId/duplicate': {
        //     post: {controller: PageController, action: "duplicatePage"},
        // },
        '/:pageId/permissions': {
            post: {controller: PageController, action: "addPermission"},
            patch: {controller: PageController, action: "updatePermission"},
            delete: {controller: <PERSON><PERSON><PERSON>roll<PERSON>, action: "deletePermission"},
        },
        '/:pageId/views': {
            post: {controller: <PERSON><PERSON><PERSON>roller, action: "addView"},
            patch: {controller: Page<PERSON>ontroller, action: "updateView"},
            delete: {controller: PageController, action: "deleteView"},
        },
        // '/:pageId/views/:viewId/duplicate': {
        //     post: {controller: PageController, action: "duplicateView"},
        // },
    }
}