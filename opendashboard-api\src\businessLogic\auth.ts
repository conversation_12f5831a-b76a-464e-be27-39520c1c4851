import {BadRequestError, InvalidParameterError, RequiredParameterError} from "../errors/AppError";
import {validateEmail} from "../utility/validator";
import {AnonymousUser, CreateUser, UserService} from "../service/user";
import {TokenExpiryUnit, TokenService, ValidApiToken} from "../service/token";
import {Token, TokenType} from "../entity/Token";
import {User} from "../entity/User";
import config, {apiUrl, appUrl} from "../config";
import {EmailButton, EmailUser, SendEmailWithContent} from "./email";
import {SettingsService} from "../service/setting";
import {Application} from "express";
import * as cookieSession from "cookie-session";
import * as passport from "passport"
import {TriggerNewUserWorkflow} from "./providers/inbranded";

export interface CreateAccountData {
    email: string
    firstName?: string
    lastName?: string
    isEmailVerified?: boolean
}

export const InitializeSignIn = async (email: string) => {
    if (!email) {
        throw new RequiredParameterError('Email')
    }
    if (!validateEmail(email)) {
        throw new InvalidParameterError(`'${email}' is not a valid email address`)
    }
    const service = new UserService()
    let user = await service.findByEmail(email)
    if (!user) {
        let res = await CreateAccount({email})
        user = res.user
    }
    const {magicLink, hash, token} = await GenerateLoginToken(user)

    const {messageId} = await SendLoginTokenEmail(user, magicLink)

    const loginInfo = {
        token, hash, magicLink
    }
    return {
        user, loginInfo, messageId, tokenId: token.id
    }
}

export const CreateAccount = async (data: CreateAccountData) => {
    let {email, firstName, lastName, isEmailVerified} = data

    email = (email || '').trim()
    firstName = (firstName || '').trim()
    lastName = (lastName || '').trim()

    if (!email) {
        throw new RequiredParameterError('Email')
    }
    if (!validateEmail(email)) {
        throw new InvalidParameterError(`'${email}' is not a valid email address`)
    }
    const service = new UserService();
    const userData: CreateUser = {
        email, firstName, lastName, isEmailVerified
    }
    const user = await service.createUser(userData);

    const settingService = new SettingsService()
    await settingService.createSettings({
        userId: user.id,
        notification: {
            email: {
                isActive: true,
                notifyOn: {
                    comment: true,
                    replies: true,
                    assignedRecord: true,
                    dueTask: true
                }
            }
        }
    })

    TriggerNewUserWorkflow(user)

    return {
        user
    }
}

export const GenerateLoginToken = async (user: User, skipTIdCheck = false) => {
    const service = new TokenService();

    const userId = user.id

    const token: Token = await service.createToken(userId, TokenType.EmailLogin, {
        value: 7,
        unit: TokenExpiryUnit.Minutes
    })
    const hash = encodeURIComponent(`${user.id}:${token.token}`);
    const magicLink = appUrl(`/auth/magic?hash=${hash}&tId=${token.id}${skipTIdCheck ? '&skipTIdCheck=true' : ''}`);

    return {
        magicLink, hash, token
    }
}

export const ExchangeMagicTokenForJWT = async (encodedHash: string) => {
    const hash = decodeURIComponent(encodedHash)
    const [userId, tkn] = hash.split(':')

    const service = new TokenService();
    const loginToken: Token = await service.verifyToken(userId, tkn, TokenType.EmailLogin)
    if (!loginToken) {
        throw new BadRequestError(`The token provided is invalid or expired`)
    }
    await service.hardRemove({id: loginToken.id})

    const uService = new UserService()
    const user = await uService.findById(userId)

    if (!user.isEmailVerified) {
        await uService.update({id: user.id}, {isEmailVerified: true})
        user.isEmailVerified = true
    }

    const token = await service.generateJWTToken(userId, false)
    return {
        token, user
    }
}

export const VerifyJWTToken = async (token: string, allowAnonymous = false): Promise<ValidApiToken | null> => {
    const service = new TokenService()

    if (!token && allowAnonymous) {
        return {
            userId: AnonymousUser.id,
            apiToken: null
        }
    }
    let validApiToken = await service.validateJWTToken(token);
    if (!validApiToken && allowAnonymous) {
        validApiToken = {
            userId: AnonymousUser.id,
            apiToken: null
        }
    }
    return validApiToken
}

export const SendLoginTokenEmail = async (user: User, magicLink: string) => {
    const body = `
    <p style="font-size: 24px; font-weight: 600;">Your sign in request to Opendashboard</p>
    <p style="color:#313539;">Click on the button below to sign in to Opendashboard. The link expires in 5 minutes.</p>
    `
    const button: EmailButton = {
        label: "Sign In",
        url: magicLink
    }
    const subject = 'Your sign in request to Opendashboard'
    const to: EmailUser = {
        email: user.email,
        name: `${user.firstName} ${user.lastName}`.trim()
    }
    const messageId = await SendEmailWithContent(to, subject, body, button, " ", true)

    console.log({messageId})
    return {
        messageId
    }
}

export enum AuthProvider {
    Google = "google",
}

export const validProviders = [AuthProvider.Google]

export const provideAuthCallbackUrl = (provider: AuthProvider) => {
    if (!validProviders.includes(provider)) {
        throw new BadRequestError(`Invalid provider '${provider}'`)
    }
    return apiUrl(`/api/v1/auth/${provider}/callback`);
}

export interface ProviderAuthState {
    referralCode?: string
}

const GoogleStrategy = require('passport-google-oauth').OAuth2Strategy;

const AuthCallback = async (email: string, firstName: string, lastName: string) => {
    const userService = new UserService();

    let user: User = await userService.findByEmail(email);
    let isNew = false
    if (!user) {
        try {
            const signupResponse = await CreateAccount({
                email, firstName, lastName, isEmailVerified: true
            })
            user = signupResponse.user
            isNew = true
        } catch (err) {
            // do nothing
        }

    }
    if (!user.isEmailVerified) {
        const s = new UserService()
        await s.update({id: user.id}, {isEmailVerified: true})
        user.isEmailVerified = true
    }
    return {user, isNew}
}

const GoogleAuthStrategy = new GoogleStrategy({
    clientID: config.GOOGLE.client_id,
    clientSecret: config.GOOGLE.client_secret,
    callbackURL: provideAuthCallbackUrl(AuthProvider.Google),
}, async (accessToken, refreshToken, profile, done) => {
    if (profile && profile.emails) {
        const email = profile.emails[0]?.value;
        const displayName = profile?.displayName;
        const nameSplit = displayName.split(/(\s+)/);
        const firstName = nameSplit[0];
        const lastName = nameSplit[2] ? nameSplit[2] : '';

        if (email) await AuthCallback(email, firstName, lastName);

        return done(null, profile);
    }
})


export const RegisterAuthStrategies = (app: Application) => {

    app.use(cookieSession({
        name: 'opendashboard-sess',
        keys: ['key1', 'key2']
    }))

    app.use(passport.initialize());
    app.use(passport.session());

    // Passport session setup
    passport.serializeUser(function (user, cb) {
        cb(null, user);
    });

    passport.deserializeUser(function (obj, cb) {
        cb(null, obj);
    });

    passport.use(GoogleAuthStrategy);
}

export const FinalizeProviderLogin = async (provider: AuthProvider, email: string) => {
    const userService = new UserService();
    let user: User;
    if (email) {
        user = await userService.findByEmail(email);
    }
    if (!user) {
        const message = `Login failed as we could not find the email associated to your ${provider} account. Please check and try again later`
        return appUrl(`/auth/error?message=${encodeURI(message)}`);
    }
    const authViaEmail = !user.isEmailVerified
    const {magicLink, hash, token} = await GenerateLoginToken(user, true)

    if (authViaEmail) {
        const {messageId} = await SendLoginTokenEmail(user, magicLink)
        return appUrl(`/auth/mail?email=${encodeURI(user.email)}`);
    }

    return magicLink
};
