import config from "../config";
import axios from "axios";
import {Request} from "express";
import {CreateIpRecord, IpRecord} from "../entity/ipRecord";
import {IpRecordService} from "../service/ipRecord";
import {getMySQLCurrentDate} from "opendb-app-db-utils/lib";

const localhostIpAddr = ['::1', '127.0.0.1']

const iPInvalidateMonths = 3


export const getClientIpAddress = (req: Request): string => {
    let ipAddress = (typeof req.headers['x-forwarded-for'] === 'string'
            && req.headers['x-forwarded-for'].split(',').shift())
        || req.connection?.remoteAddress
        || req.socket?.remoteAddress
    // || req.connection?.socket?.remoteAddress

    if (ipAddress.substr(0, 7) == "::ffff:") {
        ipAddress = ipAddress.substr(7)
    }
    return ipAddress
}

export const queryClientIpRecord = async (req: Request): Promise<IpRecord> => {
    return queryIpRecord(getClientIpAddress(req))
}

export const queryIpRecord = async (ipAddress: string): Promise<IpRecord> => {
    if (localhostIpAddr.includes(ipAddress)) {
        return localQuery()
    }

    let ipRecord: IpRecord = await cacheQueryIpRecord(ipAddress)
    if (!ipRecord) {
        ipRecord = await remoteFetchIpRecord(ipAddress);
    }
    return ipRecord
}

const localQuery = (): IpRecord => {
    const ipRecord: IpRecord = new IpRecord();
    ipRecord.ipAddress = '::1'
    ipRecord.city = 'Local'
    ipRecord.country = 'Cloud'
    ipRecord.state = 'Local'
    ipRecord.timezone = 'GMT'
    ipRecord.coordinates = {
        latitude: "0", longitude: "0"
    }
    ipRecord.createdAt = getMySQLCurrentDate()
    ipRecord.updatedAt = getMySQLCurrentDate()

    return ipRecord
}

const cacheQueryIpRecord = async (ipAddress: string): Promise<IpRecord> => {
    const s = new IpRecordService()
    let ipRecord = await s.findOne({ipAddress}, {createdAt: 'DESC'})

    if (ipRecord) {
        // check if ipRecord becomes invalidated
        // Todo: Test function
        const created = new Date(ipRecord.createdAt)
        if (new Date().getTime() > created.getTime() + 1000 * 60 * 60 * 24 * 30 * iPInvalidateMonths) {
            return null
        }
    }
    return ipRecord
}

const remoteFetchIpRecord = async (ipAddress: string): Promise<IpRecord> => {
    const accessToken = config.IPINFO.token;
    const endPoint = `https://ipinfo.io/${ipAddress}?token=${accessToken}`

    let raw;
    try {
        raw = await axios.get(endPoint);
    } catch (e) {
        return null
    }
    const response = raw.data
    if (!response) {
        return null
    }
    if (!response.country || !response.city) {
        return null
    }
    const [lat, long] = response.loc.split(',')
    const ipRecordData: CreateIpRecord = {
        city: response.city,
        coordinates: {
            latitude: lat,
            longitude: long,
        },
        country: response.country,
        ipAddress: ipAddress,
        meta: {
            serviceResponse: response
        },
        state: response.region,
        timezone: response.timezone
    }

    return await new IpRecordService().insert(ipRecordData)
}