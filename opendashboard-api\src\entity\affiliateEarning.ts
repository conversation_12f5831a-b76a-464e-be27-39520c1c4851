import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {KeyValueStore} from "./WorkspaceMemberSettings";


export interface AddAffiliateEarning extends Pick<AffiliateEarning, 'affiliateId' | 'summary' | 'earningsInCents'> {
    workspaceCreditId?: string
    billingCycleId?: string
    subscriptionId?: string
    meta?: KeyValueStore
}

@Entity()
export class AffiliateEarning {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    affiliateId: string

    @Column({type: 'varchar', nullable: true})
    summary: string

    @Column({type: 'varchar', nullable: true, unique: true})
    workspaceCreditId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    subscriptionId: string

    @Column({type: 'varchar', nullable: true, unique: true})
    billingCycleId: string

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    earningsInCents: number

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @Index()
    @Column({type: 'int', nullable: true})
    payoutId: number

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}