import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {AddCreatorMembers, CreateDiscount, DeleteCreatorMember, DeleteDiscount, GetCreatorMembers, GetCreatorPayouts, GetDiscounts, GetMyCreator, GetMyCreators, GetMyCreatorStats, SetupCreator, StripeConnectAccountURL, StripeConnectDashboardURL, UpdateCreator, UpdateCreatorMember, UpdateCreatorPhoto, UpdateDiscount, UploadCreatorFile} from "../../businessLogic/creator";
import {AddManualPurchase, BuildTemplateReleaseDependencies, BuildTemplateReleaseDependenciesResponseData, CancelTemplateSubmission, CreateTemplate, DiscardTemplateListing, DiscardTemplateRelease, GetCreatorTemplateStats, GetMyTemplate, GetMyTemplatePurchases, GetMyTemplates, GetTemplateListings, GetTemplateReleases, PrepareTemplateRelease, SubmitTemplateForReview, UpdateTemplateListing, UpdateTemplateRelease} from "../../businessLogic/templates";
import {GetMyDatabases} from "../../businessLogic/database";
import {getMyPages} from "../../businessLogic/page";
import {CreatorService} from "../../service/creator";
import {ErrorMessage, NotfoundError} from "../../errors/AppError";
import {GetWorkflows} from "../../businessLogic/workflow";
import {WorkflowStatus} from "opendb-app-db-utils/lib/typings/workflow";


export class CreatorsController {

    async create(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreator = await SetupCreator(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {myCreator}
        }
        return response.json(
            responseData
        )
    }

    async getAll(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreators = await GetMyCreators(authInfo.userId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myCreators
            },
        }
        return response.json(
            responseData
        )
    }

    async getCreator(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreator = await GetMyCreator(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myCreator
            },
        }
        return response.json(
            responseData
        )
    }

    async uploadNewFile(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {upload} = await UploadCreatorFile(authInfo.userId, request.params.id, request, true)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                upload
            },
        }
        return response.json(
            responseData
        )
    }

    async update(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreator = await UpdateCreator(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myCreator
            },
        }
        return response.json(
            responseData
        )
    }

    async updatePhoto(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreator = await UpdateCreatorPhoto(authInfo.userId, request.params.id, request, 'profile')

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myCreator
            },
        }
        return response.json(
            responseData
        )
    }

    async updateCover(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreator = await UpdateCreatorPhoto(authInfo.userId, request.params.id, request, 'cover')

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myCreator
            },
        }
        return response.json(
            responseData
        )
    }

    async getMembers(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const creatorMembers = await GetCreatorMembers(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                creatorMembers
            },
        }
        return response.json(
            responseData
        )
    }

    async addMembers(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {creatorMembers} = await AddCreatorMembers(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                creatorMembers
            },
        }
        return response.json(
            responseData
        )
    }

    async updateMember(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await UpdateCreatorMember(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async removeMember(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DeleteCreatorMember(authInfo.userId, request.params.id, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getOnboardingLink(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {accountLink} = await StripeConnectAccountURL(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                link: accountLink.url
            },
        }
        return response.json(
            responseData
        )
    }

    async getDashboardLink(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {loginLink} = await StripeConnectDashboardURL(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                link: loginLink.url
            },
        }
        return response.json(
            responseData
        )
    }

    async getPurchases(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const purchases = await GetMyTemplatePurchases(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                purchases
            },
        }
        return response.json(responseData)
    }

    async getDiscounts(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const discounts = await GetDiscounts(authInfo.userId, request.params.id)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                discounts
            },
        }
        return response.json(responseData)
    }

    async updateDiscount(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const discount = await UpdateDiscount(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {discount},
        }
        return response.json(
            responseData
        )
    }

    async deleteDiscount(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeleteDiscount(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async addDiscount(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const discount = await CreateDiscount(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                discount
            },
        }
        return response.json(
            responseData
        )
    }

    async addPurchase(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const purchase = await AddManualPurchase(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                purchase
            },
        }
        return response.json(
            responseData
        )
    }

    async getPayouts(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {payouts} = await GetCreatorPayouts(authInfo.userId, request.params.id, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                payouts
            },
        }
        return response.json(
            responseData
        )
    }

    async getTemplates(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myTemplates = await GetMyTemplates(authInfo.userId, request.params.id, [], request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myTemplates
            },
        }
        return response.json(
            responseData
        )
    }

    async createTemplate(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)


        const {myTemplate} = await CreateTemplate(authInfo.userId, request.params.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myTemplate
            },
        }
        return response.json(
            responseData
        )
    }

    async getTemplate(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myTemplate = await GetMyTemplate(authInfo.userId, request.params.id, request.params.templateId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                myTemplate
            },
        }
        return response.json(
            responseData
        )
    }

    async getSharedResources(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const s = new CreatorService()
        const c = await s.findOne({id: request.params.id})
        if (!c) throw new NotfoundError(ErrorMessage.EntityNotFound)
        const {databases} = await GetMyDatabases(authInfo.userId, c.workspaceId, "shared")
        const {pages} = await getMyPages(authInfo.userId, c.workspaceId, 'page', 'shared')
        const workflows = await GetWorkflows(authInfo.userId, c.workspaceId, {status: WorkflowStatus.Published, perPage: 100})
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                pages, databases, workflows
            },
        }
        return response.json(
            responseData
        )
    }

    async getReleases(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const releases = await GetTemplateReleases(authInfo.userId, request.params.id, request.params.templateId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                releases
            },
        }
        return response.json(
            responseData
        )
    }

    async buildNewReleaseDependencies(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {dbMap, pageMap, workflowMap} = await BuildTemplateReleaseDependencies(authInfo.userId, request.params.id, request.params.templateId, request.body)


        const data: BuildTemplateReleaseDependenciesResponseData = {
            dbMap, pageMap, workflowMap
        }
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: data
        }
        return response.json(
            responseData
        )
    }

    async prepareNewRelease(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {release} = await PrepareTemplateRelease(authInfo.userId, request.params.id, request.params.templateId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                release
            },
        }
        return response.json(
            responseData
        )
    }

    async discardRelease(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const releaseId: string = request.body.releaseId
        const {} = await DiscardTemplateRelease(authInfo.userId, request.params.id, request.params.templateId, releaseId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async updateRelease(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const {} = await UpdateTemplateRelease(authInfo.userId, request.params.id, request.params.templateId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getListings(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const listings = await GetTemplateListings(authInfo.userId, request.params.id, request.params.templateId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                listings
            },
        }
        return response.json(
            responseData
        )
    }

    async updateListing(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {listing} = await UpdateTemplateListing(authInfo.userId, request.params.id, request.params.templateId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                listing
            },
        }
        return response.json(
            responseData
        )
    }

    async deleteListing(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await DiscardTemplateListing(authInfo.userId, request.params.id, request.params.templateId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async submitForReview(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {submission} = await SubmitTemplateForReview(authInfo.userId, request.params.id, request.params.templateId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {submission},
        }
        return response.json(
            responseData
        )
    }

    async cancelSubmission(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await CancelTemplateSubmission(authInfo.userId, request.params.id, request.params.templateId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {},
        }
        return response.json(
            responseData
        )
    }

    async getCreatorStats(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {stats} = await GetMyCreatorStats(authInfo.userId, request.params.id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                stats
            },
        }
        return response.json(
            responseData
        )
    }

    async getTemplateStats(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {stats} = await GetCreatorTemplateStats(authInfo.userId, request.params.id, request.params.templateId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                stats
            },
        }
        return response.json(
            responseData
        )
    }

    async getRelease(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const releases = await GetTemplateReleases(authInfo.userId, request.params.id, request.params.templateId, [request.params.releaseId])
        if (releases.length === 0) throw new NotfoundError(ErrorMessage.EntityNotFound)
        const release = releases[0]

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                release
            },
        }
        return response.json(
            responseData
        )
    }
}


