import {MigrationInterface, QueryRun<PERSON>} from "typeorm";

export class Doc<PERSON>harset1737164964115 implements MigrationInterface {
    name = '<PERSON><PERSON>harset1737164964115'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`document\` <PERSON>AN<PERSON> \`name\` \`name\` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`document\` CHANGE \`contentText\` \`contentText\` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`document_history\` CHANGE \`contentText\` \`contentText\` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`document_history\` <PERSON>AN<PERSON> \`name\` \`name\`  VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`record\` CHANGE \`summaryText\` \`summaryText\` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`document\` CHANGE \`name\` \`name\`  VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`document\` CHANGE \`contentText\` \`contentText\` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`document_history\` CHANGE \`name\` \`name\`  VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`document_history\` CHANGE \`contentText\` \`contentText\` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;`);
        await queryRunner.query(`ALTER TABLE \`record\` CHANGE \`summaryText\` \`summaryText\` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;`);
    }

}
