import {getRepository} from '../connection/db';
import {BaseService} from './service';
import {Repository} from 'typeorm/repository/Repository';
import {WorkspaceMemberSettings} from "../entity/WorkspaceMemberSettings";

export class WorkspaceMemberSettingService extends BaseService<WorkspaceMemberSettings> {

    initRepository = (): Repository<WorkspaceMemberSettings> => {
        return getRepository(WorkspaceMemberSettings);
    }


}




