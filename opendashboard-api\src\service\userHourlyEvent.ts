import {BaseService} from "./service";
import {Repository} from "typeorm";
import {getRepository} from "../connection/db";
import {UserHourlyEvent} from "../entity/UserHourlyEvent";

export interface NewEventData extends Pick<UserHourlyEvent,
    "workspaceId" | "event"> {
    databaseId?: string
    pageId?: string
    userId?: string
}

export class UserHourlyEventService extends BaseService<UserHourlyEvent> {
    initRepository = (): Repository<UserHourlyEvent> => {
        return getRepository(UserHourlyEvent);
    }

    logEvent = async (data: NewEventData) => {
        const repo = this.getRepository()

        const eventAt = new Date()
        const anchorAt = new Date()
        anchorAt.setMinutes(0, 0, 0)


        const partial: Partial<UserHourlyEvent> = {
            ...data,
            eventAt,
            anchorAt,
            eventCount: 1
        }

        const qB = repo.createQueryBuilder()
            .insert()
            .values([partial])

        let [query, params] = qB.getQueryAndParameters()
        query += ' ON DUPLICATE KEY UPDATE `eventCount` = `eventCount`+ 1'

        await repo.query(query, params)
    }
}