import {WorkspaceIntegrationStore} from "../entity/workspaceIntegrationStore";


import {BaseService} from "./service";
import {Repository} from "typeorm/repository/Repository";
import {getRepository} from "../connection/db";

export class WorkspaceIntegrationStoreService extends BaseService<WorkspaceIntegrationStore> {

    initRepository = (): Repository<WorkspaceIntegrationStore> => {
        return getRepository(WorkspaceIntegrationStore);
    }


}
