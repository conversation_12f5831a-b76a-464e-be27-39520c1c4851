import config from "../config";
import {getInstanceId, getInstances} from "../utility/cluster";

const {Logtail} = require("@logtail/node");

const logTail = config.LOGTAIL.token ? new Logtail(config.LOGTAIL.token) : null
const env = `${config.ENVIRONMENT}`

export const logInfo = (msg: string, ...optionalParams: any[]) => {
    if (!logTail) return false
    logTail.info(`Env:${env} | ${msg}`, {pm2InstanceId: getInstanceId(), pm2Instances: getInstances(), params: optionalParams}).then();
    return true
}

export const consoleLog = (msg: string, ...optionalParams: any[]) => {
    console.log(msg, ...optionalParams);

    if (logTail && config.LOGTAIL.token) {
        logInfo(msg, ...optionalParams)
    }
}

export const Log = logTail

