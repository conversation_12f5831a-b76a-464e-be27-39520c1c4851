import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import {WorkspaceMemberRole} from "./WorkspaceMember"

@Entity()
export class WorkspaceInvitation {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    email: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    userId: string

    @Column({type: 'varchar', nullable: true})
    role: WorkspaceMemberRole

    @Index()
    @Column({type: 'varchar', nullable: false})
    token: string

    @Column({type: 'timestamp', nullable: true})
    expiresAt: Date

    @Column({type: 'varchar', nullable: false})
    invitedById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

