import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {KeyValueStore} from "./WorkspaceMemberSettings";


export enum TemplatePurchaseWorkspaceLicense {
    Single = "single",
    Multi = "multi",
}


export enum PurchaseStatus {
    Pending = "pending",
    Settled = "settled",
    Abandoned = "abandoned",
}

export enum PaymentCurrency {
    USD = "usd",
    NGN = "ngn"
}

export enum PaymentProcessor {
    Stripe = "stripe",
    Paystack = "paystack"
}


@Entity()
export class TemplatePurchase {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    templateId: string

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    templateReleaseId: number

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    templateListingId: number

    @Index()
    @Column({type: 'varchar', nullable: true})
    issuedById: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    purchasedById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    discountId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    discountCode: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    purchasedByEmail: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    purchasedByName: string

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    purchasePriceInCents: number

    @Column({type: 'timestamp', nullable: true})
    purchasedAt: Date

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceLicense: TemplatePurchaseWorkspaceLicense

    @Index()
    @Column({type: 'varchar', nullable: true})
    purchaseStatus: PurchaseStatus

    @Index()
    @Column({type: 'varchar', nullable: true})
    workspaceId: string

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    amountPaidInCents: number

    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    amountInLocalCurrency: number

    @Index()
    @Column({type: "varchar", nullable: true})
    currency: PaymentCurrency

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    creatorEarningsInCents: number

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4, select: false})
    paymentProcessFeesInCents: number

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4, select: false})
    paymentProcessFeesInLocalCurrency: number

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4, select: false})
    platformProfitInCents: number

    @Index()
    @Column({type: 'varchar', nullable: true})
    paymentProcessorReference: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    paymentProcessor: PaymentProcessor

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @Index()
    @Column({type: 'int', nullable: true})
    payoutId: number

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}