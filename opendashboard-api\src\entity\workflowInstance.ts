import {Column, CreateDate<PERSON>olumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {WorkflowInstanceStatus} from "opendb-app-db-utils/lib/typings/workflow";

export interface CreateWorkflowInstanceData {
    id: string
    workflowId: string
    databaseId?: string
    recordId?: string
}

@Entity()
export class WorkflowInstance {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: "varchar"})
    workflowId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    databaseId: string

    @Index()
    @Column({type: "varchar", nullable: true})
    recordId: string

    @Column({type: 'varchar', default: WorkflowInstanceStatus.Active})
    status: WorkflowInstanceStatus

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    startedAt: Date

    @Index()
    @Column({type: 'timestamp', nullable: true})
    completedAt: Date

    @Column({type: 'json', nullable: true})
    meta: object

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}