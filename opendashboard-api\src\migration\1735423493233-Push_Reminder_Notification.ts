import {MigrationInterface, QueryRunner} from "typeorm";

export class PushReminderNotification1735423493233 implements MigrationInterface {
    name = 'PushReminderNotification1735423493233'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`reminder\` (\`id\` varchar(36) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`title\` varchar(255) NULL, \`description\` text NULL, \`databaseId\` varchar(255) NULL, \`recordId\` varchar(255) NULL, \`isResolved\` tinyint NOT NULL DEFAULT 0, \`nextNotifyAt\` timestamp NULL, \`notifyDates\` json NULL, \`createdById\` varchar(255) NULL, \`updatedById\` varchar(255) NULL, \`meta\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_22ba4a9a55e46b6959a2e12630\` (\`workspaceId\`), INDEX \`IDX_f0fa5b8025ad95b06181686128\` (\`title\`), INDEX \`IDX_f31019df0b80a5c33b390cd2a3\` (\`databaseId\`), INDEX \`IDX_f2a7b17c232fc6005367940b8f\` (\`recordId\`), INDEX \`IDX_cfcaf59bd1d3e3f2a84cf89cbe\` (\`isResolved\`), INDEX \`IDX_4005f459d20756f07bccaa0b35\` (\`createdById\`), INDEX \`IDX_51872e7a10bf520f98e4011748\` (\`updatedById\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`push_registration\` (\`id\` varchar(36) NOT NULL, \`userId\` varchar(255) NOT NULL, \`sessionId\` varchar(255) NOT NULL, \`providerToken\` text NOT NULL, \`provider\` varchar(255) NOT NULL, \`refreshedAt\` timestamp NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_de90ebba9660f571854c54452d\` (\`userId\`), INDEX \`IDX_21e9b5252506c277d7b46f0f42\` (\`sessionId\`), INDEX \`IDX_77e40094598464ccf190b1bbf8\` (\`provider\`), INDEX \`IDX_434dff27a71545957994f90cdb\` (\`refreshedAt\`), INDEX \`IDX_b53eef83b9c49e4f63520d6fa6\` (\`createdAt\`), INDEX \`IDX_5f47891f411280033e90d8905e\` (\`updatedAt\`), INDEX \`IDX_aef22dd4df031b548d6fbe4f08\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`notification\` (\`id\` varchar(36) NOT NULL, \`userId\` varchar(255) NOT NULL, \`workspaceId\` varchar(255) NOT NULL, \`title\` varchar(255) NULL, \`description\` text NULL, \`linkUrl\` varchar(255) NULL, \`isSeen\` tinyint NOT NULL DEFAULT 0, \`seenAt\` timestamp NULL, \`meta\` json NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, INDEX \`IDX_1ced25315eb974b73391fb1c81\` (\`userId\`), INDEX \`IDX_972838d477a2be7879a8cfda49\` (\`workspaceId\`), INDEX \`IDX_774d149608b9e268c4ff4d869b\` (\`title\`), INDEX \`IDX_37e0a042c98ff7805fa4e97377\` (\`isSeen\`), INDEX \`IDX_b11a5e627c41d4dc3170f1d370\` (\`createdAt\`), INDEX \`IDX_489f2762db84e32ef4d1df3533\` (\`updatedAt\`), INDEX \`IDX_5d4336ec9df2f2505d0554c291\` (\`deletedAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`document\` ADD \`databaseId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`document\` ADD \`recordId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`document\` CHANGE \`viewId\` \`viewId\` varchar(255) NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_d5ead8d2b9779f85bc781be2fc\` ON \`document\` (\`databaseId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_3f0d973b5b212eef2333408732\` ON \`document\` (\`recordId\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_3f0d973b5b212eef2333408732\` ON \`document\``);
        await queryRunner.query(`DROP INDEX \`IDX_d5ead8d2b9779f85bc781be2fc\` ON \`document\``);
        await queryRunner.query(`ALTER TABLE \`document\` CHANGE \`viewId\` \`viewId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`document\` DROP COLUMN \`recordId\``);
        await queryRunner.query(`ALTER TABLE \`document\` DROP COLUMN \`databaseId\``);
        await queryRunner.query(`DROP INDEX \`IDX_5d4336ec9df2f2505d0554c291\` ON \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_489f2762db84e32ef4d1df3533\` ON \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_b11a5e627c41d4dc3170f1d370\` ON \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_37e0a042c98ff7805fa4e97377\` ON \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_774d149608b9e268c4ff4d869b\` ON \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_972838d477a2be7879a8cfda49\` ON \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_1ced25315eb974b73391fb1c81\` ON \`notification\``);
        await queryRunner.query(`DROP TABLE \`notification\``);
        await queryRunner.query(`DROP INDEX \`IDX_aef22dd4df031b548d6fbe4f08\` ON \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_5f47891f411280033e90d8905e\` ON \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_b53eef83b9c49e4f63520d6fa6\` ON \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_434dff27a71545957994f90cdb\` ON \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_77e40094598464ccf190b1bbf8\` ON \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_21e9b5252506c277d7b46f0f42\` ON \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_de90ebba9660f571854c54452d\` ON \`push_registration\``);
        await queryRunner.query(`DROP TABLE \`push_registration\``);
        await queryRunner.query(`DROP INDEX \`IDX_51872e7a10bf520f98e4011748\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_4005f459d20756f07bccaa0b35\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_cfcaf59bd1d3e3f2a84cf89cbe\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_f2a7b17c232fc6005367940b8f\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_f31019df0b80a5c33b390cd2a3\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_f0fa5b8025ad95b06181686128\` ON \`reminder\``);
        await queryRunner.query(`DROP INDEX \`IDX_22ba4a9a55e46b6959a2e12630\` ON \`reminder\``);
        await queryRunner.query(`DROP TABLE \`reminder\``);
    }

}
