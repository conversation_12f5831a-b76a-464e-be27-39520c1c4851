import { MigrationInterface, QueryRunner } from "typeorm";

export class WorkflowCampaignAnalytic1739575996577 implements MigrationInterface {
    name = 'WorkflowCampaignAnalytic1739575996577'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_f5181cf32304848d54109cc141\` ON \`workflow_task\``);
        await queryRunner.query(`DROP INDEX \`IDX_3aceab20f0ba5dd17fa3224c78\` ON \`campaign_analytic\``);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` ADD \`workflowId\` varchar(255) NOT NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_342d06b0d97e9295e185b0e2af\` ON \`workflow_task\` (\`errorCount\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_516981a2d3521cd507af7f916f\` ON \`campaign_analytic\` (\`workflowId\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_bb6e8f56ccb50624a2a6fce50e\` ON \`campaign_analytic\` (\`campaignId\`, \`workflowId\`, \`emailId\`, \`linkId\`, \`eventAt\`)`);
        await queryRunner.query(`ALTER TABLE \`workspace_usage\` ADD \`workflowTask\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_usage\` DROP COLUMN \`workflowTask\``);
        await queryRunner.query(`DROP INDEX \`IDX_bb6e8f56ccb50624a2a6fce50e\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_516981a2d3521cd507af7f916f\` ON \`campaign_analytic\``);
        await queryRunner.query(`DROP INDEX \`IDX_342d06b0d97e9295e185b0e2af\` ON \`workflow_task\``);
        await queryRunner.query(`ALTER TABLE \`campaign_analytic\` DROP COLUMN \`workflowId\``);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_3aceab20f0ba5dd17fa3224c78\` ON \`campaign_analytic\` (\`campaignId\`, \`emailId\`, \`linkId\`, \`eventAt\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_f5181cf32304848d54109cc141\` ON \`workflow_task\` (\`errorCount\`)`);
    }

}
