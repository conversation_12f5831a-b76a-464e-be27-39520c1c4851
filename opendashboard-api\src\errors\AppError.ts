export enum HttpStatusCode {
    OK = 200,
    BAD_REQUEST = 400,
    NOT_FOUND = 404,
    INTERNAL_SERVER = 500,
    UNAUTHORIZED = 401,
    EXISTS = 409,
}

export enum ErrorMessage {
    UnableToCreateSubscription_BillingError = "Unable to create subscription as charge failed on your account. Check your payment method and try again",
    SubscriptionNotFound = "Subscription not found",
    PlanNotFound = "Plan not found",
    EntityNotFound = "Entity not found",
    IncorrectRequestDataProvided = "Incorrect data provided for request",
    NoDataFoundForSearchCriteria = "No data found for search criteria",
    IntegrationNotFound = "Integration not found",
    NoPaymentMethodAvailable = "No payment method available",
    SubscriptionNotActive = "Subscription is not active",
    WorkspaceNotFound = "Workspace not found or does not exists",
    AuthorizationNotFound = "Authorization not found",
    UnableToAuthorize = "Unable to authorize",
    FeatureAlreadyEnabled = "The requested feature is already enabled",
    FeatureNotEnabled = "The requested feature is not enabled",
    UnableToFetchEntity = "Unable to fetch requested entity",
    WhatsAppMessageTemplateNotAvailable = "Message template does not exist or is not available for us at the moment",
    UnableToSave = "Unable to save",
    UnableToVerifyPhrase = "We were enable to verify",
    UnableToVerifyDomain = "We were enable to verify the records, please check your domain provider to ensure the records are correctly configured and try again later.",
    DomainCannotContainOpendashboardSite = "Domain cannot contain opendashboard.site.",
    NotEnoughCredit = "Not enough credit to perform the requested action",
    UnableToPerformAction = "Unable to perform requested action",
    UnableToProcessRequest = "Unable to process request",
    UnableToProcessRequestTryAgainLater = "Unable to process request at this time, please try again later",
    IncorrectLoginCredentials = "Incorrect email password combination",
    CanOnlyContainAZ09_ = " can only contain a-z, 0-9 and _ and must begin with either a-z or _",
    AllIsReservedCannotBeWorkspaceNameOrDomain = "\"All\" is reserved and cannot be used as a workspace name or domain",
    NameCannotContainOpendashboard = "Name or domain cannot contain \"opendashboard\"",
    AllIsReservedCannotBeName = "\"All\" is reserved and cannot be used as a name",
    EntityAlreadyExists = "Entity already exists",
    UserAlreadyExistsInWorkspace = "User already a member in the workspace",
    MaximumMemberLimitReached = "Maximum member limit reached for workspace, upgrade plan to continue",
    MaximumCollaboratorLimitReached = "Maximum collaborator limit reached for workspace, upgrade plan to continue",
    MaximumSenderEmailLimitReached = "Maximum sender email limit reached for workspace, upgrade plan to continue",
    MaximumSenderDomainLimitReached = "Maximum sender domain limit reached for workspace, upgrade plan to continue",
    IdURLRequiredForAttachment = "Id and URL required for attachment",
    TextOrAttachmentNeeded = "Text and or attachment(s) is needed",
    ErrorOccurredWhileProcessingRequest = "An error occurred while processing your request",

    AssocEntityNotFound = "Associated entity not found",
    AssocRecordNotFound = "Associated record not found",
    // UserAlreadyExistsInWorkspace = "User already a member in the workspace",
    SenderNotVerified = "Sender is not verified",
    DomainNotVerifiedForSending = "Domain is not verified for sending",
    TargetDatabaseNotFound = "Target database not found",
    NoEmailFound = "No email found",
    EmailIsInvalid = "Email is invalid",
    CampaignNotInDraft = "Campaign not in draft",
    CampaignNotInReview = "Campaign not in review",
    CampaignNotInReviewOrDraft = "Campaign not in review or draft",
    UnableToSendOriginalCampaignNotSent = "Unable to send. The original campaign has not been sent yet",
    UnableToScheduleCannotBeEarlierThanOriginalCampaign = "Unable to schedule. It cannot be earlier than the original campaign",
    LinkIsInvalidOrExpired = "The link is invalid or has expired",
    LinkIsExpired = "The link has expired",
    WorkspaceAssociatedWithAnotherCreator = "The Workspace is associated with another creator profile",
    ReleaseInMarketPlace = "The release is active in marketplace",
    ReleaseReplaced = "The release has been replaced by a more recent version",
    ListingInMarketPlace = "The listing is active in marketplace",
    ListingReplaced = "The listing has been replaced by a more recent version",

    NoPaymentMethodAvailableAsCreatorYetToSetupPayment = "No payment method available as the creator is yet to activate payment on the templates they own, we have notified them. You can also contact them for faster resolution",

    PreviousPurchaseFoundForUser = "Previous purchase found for user with email",
    PurchaseNotFoundForTemplate = "Purchase not found for template",
    TemplateReleaseNotFound = "Template release not found",
    TemplateListingNotFound = "Template listing not found",
    TemplateNotFeaturedInMarketplace_UnableToProceed = "Template not featured in marketplace, unable to proceed",
    DiscountNotFoundOrExpired = "Discount not found or may have expired",
    DiscountUsageLimitReached = "Discount usage limit reached",
    WorkflowHasNoStepsYet = "The workflow has no steps yet",
    WorkflowHasStepsThatAreNotConfiguredProperly = "The workflow has steps that are not configured properly",
    WorkflowHasNoTrigger = "The workflow has no trigger",
    WorkflowTriggerNotFound = "The workflow trigger is not found",
    WorkflowHasNoActionSteps = "The workflow has no action steps and cannot be published",
    WorkflowInstanceNotFound = "The workflow instance is not found",
    WorkflowInstanceNotPendingApproval = "The workflow instance is not pending approval",
    WorkflowStepNotFound = "The workflow step is not found",
    WorkflowStepTypeNotSupported = "The workflow step type is not supported",
    IfYouAreTryingToUseWebhook = "This endpoint is provided as a guide only, if you are trying to use webhook use /webhooks/{webhookId}/dispatch. Add a /test at the end for testing.",
    UnableToSaveConnectionInvalidCredentials = "Unable to save the connection, the credentials is invalid.",
    WorkflowNotPublished = "Workflow is not published.",
}

class BaseError extends Error {
    public readonly name: string
    public readonly statusCode: HttpStatusCode
    public readonly isOperational: boolean

    constructor(
        name: string,
        statusCode: HttpStatusCode,
        isOperational: boolean,
        description: string
    ) {
        super(description)
        Object.setPrototypeOf(this, new.target.prototype)

        this.name = name
        this.statusCode = statusCode
        this.isOperational = isOperational

        Error.captureStackTrace(this)
    }
}

export class BadRequestError extends BaseError {
    constructor(param: string) {
        super(`BadRequestError`, HttpStatusCode.BAD_REQUEST, true, `${param}.`)
    }
}

export class RequiredParameterError extends BaseError {
    constructor(param: string) {
        super(`RequiredParameterError`, HttpStatusCode.BAD_REQUEST, true, `${param} cannot be empty.`)
    }
}

export class MessageBrokerError extends BaseError {
    constructor(param: string) {
        super(`MessageBrokerError`, HttpStatusCode.BAD_REQUEST, true, `${param}`)
    }
}

export class InvalidParameterError extends BaseError {
    constructor(param: string) {
        super(`InvalidParameterError`, HttpStatusCode.BAD_REQUEST, true, `${param}`)
    }
}

export class NotfoundError extends BaseError {
    constructor(param: string) {
        super(`NotfoundError`, HttpStatusCode.NOT_FOUND, true, `${param}`)
    }
}

export class UniqueConstraintError extends BaseError {
    constructor(param: string) {
        super('UniqueConstraintError', HttpStatusCode.EXISTS, true, `${param} must be unique.`)
    }
}

export class UnauthorizedError extends BaseError {
    constructor(param: string) {
        super('UnauthorizedError', HttpStatusCode.UNAUTHORIZED, true, `${param}`)
    }
}

export class ChargebeeException extends BaseError {
    constructor(param: string) {
        super('ChargebeeException', HttpStatusCode.INTERNAL_SERVER, true, `${param}`)
    }
}

export class ImageServiceException extends BaseError {
    constructor(param: string) {
        super('ImageServiceException', HttpStatusCode.INTERNAL_SERVER, true, `${param}`)
    }
}

export class ServerProcessingError extends BaseError {
    constructor(param: string) {
        super('ServerProcessingError', HttpStatusCode.INTERNAL_SERVER, true, `${param}`)
    }
}

export class InternalServerError extends BaseError {
    constructor(param: string) {
        super('InternalServerError', HttpStatusCode.INTERNAL_SERVER, true, `${param}`)
    }
}

export class UserActionWorkflowError extends BaseError {
    constructor(param: string) {
        super('UserActionWorkflowError', HttpStatusCode.INTERNAL_SERVER, true, `${param}`)
    }
}

