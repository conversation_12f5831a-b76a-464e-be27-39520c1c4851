import {FileType, ProcessFormToSpaceUpload, uploadBufferToS3} from "./doUpload";
import {GetProfile} from "./account";
import {Request} from "express"
import {RequiredParameterError} from "../errors/AppError";
import {In} from "typeorm";
import {WorkspaceUploadService} from "../service/workspaceUpload";
import {WorkspaceUpload} from "../entity/WorkspaceUpload";
import {generateUUID} from "opendb-app-db-utils/lib";

export const UploadFile = async (userId: string, workspaceId: string, request: Request, anonymousUser = false): Promise<{ upload: WorkspaceUpload }> => {
    let date = new Date()
    if (!anonymousUser) {
        const user = await GetProfile(userId);
        date = user.createdAt
    }

    const month = date.getMonth() + 1
    const year = date.getFullYear()
    const day = date.getDate()

    const id = generateUUID()

    const data = await ProcessFormToSpaceUpload(request, `w/${workspaceId}/${year}/${month}/${day}/u/${userId}`, `${id}`, FileType.File)
    const s = new WorkspaceUploadService()

    const d: Partial<WorkspaceUpload> = {
        finalUrl: data.location,
        height: data.height,
        mimeType: data.mime,
        name: data.name,
        size: data.size,
        thumbnailUrl: undefined,
        type: data.type,
        doSpaceKey: data.key,
        userId: userId,
        workspaceId,
        width: data.width
    }
    const upload = await s.insert(d)
    return {upload}
}

export const UploadBuffer = async (userId: string, workspaceId: string, fileContent: Buffer, file: { name: string, type: string, size:number }): Promise<{ upload: WorkspaceUpload }> => {
    let date = new Date()
    const month = date.getMonth() + 1
    const year = date.getFullYear()
    const day = date.getDate()

    const filePath = `w/${workspaceId}/${year}/${month}/${day}/u/${userId}/${file.name}`

    // calculate size from fileContent
    const size = fileContent.length

    // const file = {
    //     ...fileInfo,
    //     size,
    // }

    const data = await uploadBufferToS3({
        fileContent,
        filePath,
        file,
        imageDimen: {width: 0, height: 0, type: file.type}
    })
    const s = new WorkspaceUploadService()

    const d: Partial<WorkspaceUpload> = {
        finalUrl: data.location,
        height: data.height,
        mimeType: data.mime,
        name: data.name,
        size: data.size,
        thumbnailUrl: undefined,
        type: data.type,
        doSpaceKey: data.key,
        userId: userId,
        workspaceId,
        width: data.width
    }
    const upload = await s.insert(d)
    return {upload}
}

export const GetUpload = async (workspaceId: string, id: number) => {
    const service = new WorkspaceUploadService()

    return await service.findOne({id, workspaceId}, {
        id: "DESC"
    })
}

export const GetUploads = async (userId: string, workspaceId: string) => {
    const service = new WorkspaceUploadService()

    const uploads = await service.find({userId, workspaceId}, {
        id: "DESC"
    })

    return {uploads}
}

export const DeleteUploads = async (userId: string, workspaceId: string, ids: number[]) => {
    if (!Array.isArray(ids)) {
        throw new RequiredParameterError('ids')
    }
    const service = new WorkspaceUploadService()

    return await service.remove({userId, workspaceId, id: In(ids)})
}

