"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/flairup";
exports.ids = ["vendor-chunks/flairup"];
exports.modules = {

/***/ "(ssr)/./node_modules/flairup/dist/esm/index.js":
/*!************************************************!*\
  !*** ./node_modules/flairup/dist/esm/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSheet: () => (/* binding */ createSheet),\n/* harmony export */   cx: () => (/* binding */ cx)\n/* harmony export */ });\n// src/utils/asArray.ts\nfunction asArray(v) {\n  return [].concat(v);\n}\n\n// src/utils/is.ts\nfunction isPsuedoSelector(selector) {\n  return selector.startsWith(\":\");\n}\nfunction isStyleCondition(selector) {\n  return isString(selector) && (selector === \"*\" || selector.length > 1 && \":>~.+*\".includes(selector.slice(0, 1)) || isImmediatePostcondition(selector));\n}\nfunction isValidProperty(property, value) {\n  return (isString(value) || typeof value === \"number\") && !isCssVariables(property) && !isPsuedoSelector(property) && !isMediaQuery(property);\n}\nfunction isMediaQuery(selector) {\n  return selector.startsWith(\"@media\");\n}\nfunction isDirectClass(selector) {\n  return selector === \".\";\n}\nfunction isCssVariables(selector) {\n  return selector === \"--\";\n}\nfunction isString(value) {\n  return value + \"\" === value;\n}\nfunction isImmediatePostcondition(value) {\n  return isString(value) && (value.startsWith(\"&\") || isPsuedoSelector(value));\n}\n\n// src/utils/joinTruthy.ts\nfunction joinTruthy(arr, delimiter = \"\") {\n  return arr.filter(Boolean).join(delimiter);\n}\n\n// src/utils/stableHash.ts\nfunction stableHash(prefix, seed) {\n  let hash = 0;\n  if (seed.length === 0)\n    return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash;\n  }\n  return `${prefix ?? \"cl\"}_${hash.toString(36)}`;\n}\n\n// src/utils/stringManipulators.ts\nfunction handlePropertyValue(property, value) {\n  if (property === \"content\") {\n    return `\"${value}\"`;\n  }\n  return value;\n}\nfunction camelCaseToDash(str) {\n  return str.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\nfunction joinedProperty(property, value) {\n  return `${property}:${value}`;\n}\nfunction toClass(str) {\n  return str ? `.${str}` : \"\";\n}\nfunction appendString(base, line) {\n  return base ? `${base}\n${line}` : line;\n}\n\n// src/Rule.ts\nvar Rule = class _Rule {\n  constructor(sheet, property, value, selector) {\n    this.sheet = sheet;\n    this.property = property;\n    this.value = value;\n    this.selector = selector;\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(\n      this.selector.postconditions\n    );\n    this.hash = this.selector.hasConditions ? this.selector.scopeClassName : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n  toString() {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash\n    });\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors\n    });\n    return `${selectors} {${_Rule.genRule(this.property, this.value)}}`;\n  }\n  static genRule(property, value) {\n    const transformedProperty = camelCaseToDash(property);\n    return joinedProperty(\n      transformedProperty,\n      handlePropertyValue(property, value)\n    ) + \";\";\n  }\n};\nfunction mergeSelectors(selectors, { left = \"\", right = \"\" } = {}) {\n  const output = selectors.reduce((selectors2, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors2 + current;\n    }\n    if (isImmediatePostcondition(current)) {\n      return selectors2 + current.slice(1);\n    }\n    return joinTruthy([selectors2, current], \" \");\n  }, left);\n  return joinTruthy([output, toClass(right)], \" \");\n}\nvar Selector = class _Selector {\n  constructor(sheet, scopeName = null, {\n    preconditions,\n    postconditions\n  } = {}) {\n    this.sheet = sheet;\n    this.preconditions = [];\n    this.scopeClassName = null;\n    this.scopeName = null;\n    this.postconditions = [];\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n  setScope(scopeName) {\n    if (!scopeName) {\n      return this;\n    }\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(\n        this.sheet.name,\n        // adding the count guarantees uniqueness across style.create calls\n        scopeName + this.sheet.count\n      );\n    }\n    return this;\n  }\n  get hasConditions() {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n  addScope(scopeName) {\n    return new _Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions\n    });\n  }\n  addPrecondition(precondition) {\n    return new _Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition)\n    });\n  }\n  addPostcondition(postcondition) {\n    return new _Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition)\n    });\n  }\n  createRule(property, value) {\n    return new Rule(this.sheet, property, value, this);\n  }\n};\n\n// src/Sheet.ts\nvar Sheet = class {\n  constructor(name, rootNode) {\n    this.name = name;\n    this.rootNode = rootNode;\n    // Hash->css\n    this.storedStyles = {};\n    // styles->hash\n    this.storedClasses = {};\n    this.style = \"\";\n    this.count = 0;\n    this.id = `flairup-${name}`;\n    this.styleTag = this.createStyleTag();\n  }\n  getStyle() {\n    return this.style;\n  }\n  append(css) {\n    this.style = appendString(this.style, css);\n  }\n  apply() {\n    this.count++;\n    if (!this.styleTag) {\n      return;\n    }\n    this.styleTag.innerHTML = this.style;\n  }\n  isApplied() {\n    return !!this.styleTag;\n  }\n  createStyleTag() {\n    if (typeof document === \"undefined\" || this.isApplied() || // Explicitly disallow mounting to the DOM\n    this.rootNode === null) {\n      return this.styleTag;\n    }\n    const styleTag = document.createElement(\"style\");\n    styleTag.type = \"text/css\";\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n  addRule(rule) {\n    const storedClass = this.storedClasses[rule.key];\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n    this.append(rule.toString());\n    return rule.hash;\n  }\n};\n\n// src/utils/forIn.ts\nfunction forIn(obj, fn) {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n\n// src/cx.ts\nfunction cx(...args) {\n  const classes = args.reduce((classes2, arg) => {\n    if (arg instanceof Set) {\n      classes2.push(...arg);\n    } else if (typeof arg === \"string\") {\n      classes2.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes2.push(cx(...arg));\n    } else if (typeof arg === \"object\") {\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes2.push(key);\n        }\n      });\n    }\n    return classes2;\n  }, []);\n  return joinTruthy(classes, \" \").trim();\n}\n\n// src/index.ts\nfunction createSheet(name, rootNode) {\n  const sheet = new Sheet(name, rootNode);\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet)\n  };\n  function create(styles) {\n    const scopedStyles = {};\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(\n      ([scopeName, styles2, selector]) => {\n        iterateStyles(sheet, styles2, selector).forEach(\n          (className) => {\n            addScopedStyle(scopeName, className);\n          }\n        );\n      }\n    );\n    sheet.apply();\n    return scopedStyles;\n    function addScopedStyle(name2, className) {\n      scopedStyles[name2] = scopedStyles[name2] ?? /* @__PURE__ */ new Set();\n      scopedStyles[name2].add(className);\n    }\n  }\n}\nfunction iteratePreconditions(sheet, styles, selector) {\n  const output = [];\n  forIn(styles, (key, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(\n        sheet,\n        value,\n        selector.addPrecondition(key)\n      ).forEach((item) => output.push(item));\n    }\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n  return output;\n}\nfunction iterateStyles(sheet, styles, selector) {\n  const output = /* @__PURE__ */ new Set();\n  forIn(styles, (property, value) => {\n    let res = [];\n    if (isStyleCondition(property)) {\n      res = iterateStyles(\n        sheet,\n        value,\n        selector.addPostcondition(property)\n      );\n    } else if (isDirectClass(property)) {\n      res = asArray(value);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value, property, selector);\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value, selector);\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n    return addEachClass(res, output);\n  });\n  return output;\n}\nfunction addEachClass(list, to) {\n  list.forEach((className) => to.add(className));\n  return to;\n}\nfunction cssVariablesBlock(sheet, styles, selector) {\n  const classes = /* @__PURE__ */ new Set();\n  const chunkRows = [];\n  forIn(styles, (property, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n  if (chunkRows.length) {\n    const output = chunkRows.join(\" \");\n    sheet.append(\n      `${mergeSelectors(selector.preconditions, {\n        right: selector.scopeClassName\n      })} {${output}}`\n    );\n  }\n  classes.add(selector.scopeClassName);\n  return classes;\n}\nfunction handleMediaQuery(sheet, styles, mediaQuery, selector) {\n  sheet.append(mediaQuery + \" {\");\n  const output = iterateStyles(sheet, styles, selector);\n  sheet.append(\"}\");\n  return output;\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/flairup/dist/esm/index.js\n");

/***/ })

};
;