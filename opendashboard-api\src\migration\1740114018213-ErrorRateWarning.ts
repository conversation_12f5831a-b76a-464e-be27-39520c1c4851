import { MigrationInterface, QueryRunner } from "typeorm";

export class ErrorRateWarning1740114018213 implements MigrationInterface {
    name = 'ErrorRateWarning1740114018213'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workflow\` ADD \`errorRateWarningAt\` timestamp NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_079c8b279cd96f4fc64fc3dc91\` ON \`workflow\` (\`errorRateWarningAt\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_079c8b279cd96f4fc64fc3dc91\` ON \`workflow\``);
        await queryRunner.query(`ALTER TABLE \`workflow\` DROP COLUMN \`errorRateWarningAt\``);
    }

}
