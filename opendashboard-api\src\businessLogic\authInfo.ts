import {Request} from "express";
import {ApiToken, ValidApiToken} from "../service/token";

export interface AuthInfo {
    userId: string
    apiToken: ApiToken
}

export const getAuthInfo = (request: Request): AuthInfo => {
    const userId = request['userId']
    const apiToken: ApiToken = request['token']

    return {userId, apiToken}
}

export const saveAuthInfo = (request: Request, validApiToken: ValidApiToken): AuthInfo => {
    request['userId'] = validApiToken.userId
    request['token'] = validApiToken.apiToken

    const {userId, apiToken} = validApiToken
    return {userId, apiToken}
}