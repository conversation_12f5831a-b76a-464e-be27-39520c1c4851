"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-phrasing";
exports.ids = ["vendor-chunks/hast-util-phrasing"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-phrasing/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-phrasing/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   phrasing: () => (/* binding */ phrasing)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-is-body-ok-link */ \"(ssr)/./node_modules/hast-util-is-body-ok-link/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n\n\n\n\n\nconst basic = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)([\n  'a',\n  'abbr',\n  // `area` is in fact only phrasing if it is inside a `map` element.\n  // However, since `area`s are required to be inside a `map` element, and it’s\n  // a rather involved check, it’s ignored here for now.\n  'area',\n  'b',\n  'bdi',\n  'bdo',\n  'br',\n  'button',\n  'cite',\n  'code',\n  'data',\n  'datalist',\n  'del',\n  'dfn',\n  'em',\n  'i',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'map',\n  'mark',\n  'meter',\n  'noscript',\n  'output',\n  'progress',\n  'q',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'select',\n  'small',\n  'span',\n  'strong',\n  'sub',\n  'sup',\n  'template',\n  'textarea',\n  'time',\n  'u',\n  'var',\n  'wbr'\n])\n\nconst meta = (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_0__.convertElement)('meta')\n\n/**\n * Check if the given value is *phrasing* content.\n *\n * @param {Nodes} value\n *   Node to check.\n * @returns {boolean}\n *   Whether `value` is phrasing content.\n */\nfunction phrasing(value) {\n  return Boolean(\n    value.type === 'text' ||\n      basic(value) ||\n      (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_1__.embedded)(value) ||\n      (0,hast_util_is_body_ok_link__WEBPACK_IMPORTED_MODULE_2__.isBodyOkLink)(value) ||\n      (meta(value) && (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_3__.hasProperty)(value, 'itemProp'))\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-phrasing/lib/index.js\n");

/***/ })

};
;