import {getRepository} from '../connection/db';
import {BaseService} from './service';
import {Repository} from 'typeorm/repository/Repository';
import {ServerProcessingError, UniqueConstraintError} from "../errors/AppError";
import {Workspace} from "../entity/Workspace";
import {suggest} from "opendb-app-db-utils/lib/methods/suggest";
import {Column, In} from "typeorm";
import {KeyValueStore} from "../entity/WorkspaceMemberSettings";

export interface CreateWorkspaceData extends Pick<Workspace, 'ownerId' | 'createdById' | 'name' | 'domain'> {
    logo?: string
    orgUseCase?: string
    orgType?: string
    orgSize?: string
    orgPhoneNumber?: string
    meta?: KeyValueStore
    isSetupCompleted?: boolean
    affiliateId?: string
}

export class WorkspaceService extends BaseService<Workspace> {

    initRepository = (): Repository<Workspace> => {
        return getRepository(Workspace);
    }

    createWorkspace = async (data: CreateWorkspaceData) => {
        try {
            return await this.insert(data);
        } catch (err) {
            if (err.message.includes("Duplicate entry")) throw new UniqueConstraintError('domain')
            throw new ServerProcessingError(err.message)
        }
    }

    generateUniqueDomain = async (name: string, maxCount=10): Promise<string[]> => {
        const repository = this.getRepository();

        const suggestions = suggest(name, maxCount, true);

        const takenNames = await repository
            .createQueryBuilder()
            .select()
            .withDeleted()
            .where({
                domain: In(suggestions)
            })
            .getMany()
            .then(workspaces => workspaces.map(workspace => workspace.domain));

        return suggestions.filter(suggestion => !takenNames.includes(suggestion));
    }


}




