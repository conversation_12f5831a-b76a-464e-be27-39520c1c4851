import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"

@Entity()
@Index(["workspaceId", "hour", "day", "month", "year"], {unique: true})
export class WorkspaceUsage {

    @PrimaryGeneratedColumn("increment")
    id: number

    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Column({type: "int", default: 0})
    hour: number

    @Column({type: "int", default: 0})
    day: number

    @Column({type: "int", default: 0})
    month: number

    @Column({type: "int", default: 0})
    year: number

    @Column({type: "int", default: 0})
    aiGeneration: number

    @Column({type: "int", default: 0})
    enrichment: number

    @Column({type: "int", default: 0})
    emailSent: number

    @Column({type: "int", default: 0})
    workflowTask: number

    @Column({type: "int", default: 0})
    creditBilledInCents: number

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @Index()
    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date


}