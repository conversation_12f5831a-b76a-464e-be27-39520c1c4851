import {MigrationInterface, QueryRunner} from "typeorm";

export class TemplatePurchaseEmailDiscount1737078024902 implements MigrationInterface {
    name = 'TemplatePurchaseEmailDiscount1737078024902'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`template_purchase\` CHANGE \`id\` \`id\` varchar(36) NOT NULL`);
        await queryRunner.query(`CREATE TABLE \`discount\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`creatorId\` varchar(255) NOT NULL, \`createdById\` varchar(255) NOT NULL, \`updatedById\` varchar(255) NOT NULL, \`code\` varchar(255) NOT NULL, \`amount\` int NOT NULL DEFAULT '0', \`isPercent\` tinyint NOT NULL DEFAULT 0, \`isActive\` tinyint NOT NULL DEFAULT 0, \`templateIds\` json NULL, \`isAllTemplates\` tinyint NOT NULL DEFAULT 0, \`isValidForPeriod\` tinyint NOT NULL DEFAULT 0, \`validTo\` timestamp NULL, \`validFrom\` timestamp NULL, \`isUsageLimited\` tinyint NOT NULL DEFAULT 0, \`usageLimit\` int NOT NULL DEFAULT '0', \`usage\` int NOT NULL DEFAULT '0', \`reservedUsage\` int NOT NULL DEFAULT '0', \`lastUsed\` timestamp NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`auditLog\` json NULL, INDEX \`IDX_5b3957849aad68f2e1ba4bb563\` (\`createdById\`), INDEX \`IDX_6ca78d7d85aa0dfe1e9b125df9\` (\`updatedById\`), INDEX \`IDX_bab0a51e6ee779c13e30e0e9dc\` (\`isPercent\`), INDEX \`IDX_35bff203acae36226f86b4ed84\` (\`isActive\`), INDEX \`IDX_cd63e316df9eecaa467f6c2da0\` (\`isAllTemplates\`), INDEX \`IDX_67d1404848f32b221d6e4b08c1\` (\`isValidForPeriod\`), INDEX \`IDX_2cf36a86e7433af0bd39811ba2\` (\`isUsageLimited\`), UNIQUE INDEX \`IDX_db3e9fe7c17656e69e62c3f591\` (\`creatorId\`, \`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` ADD \`issuedById\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` ADD \`discountId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` ADD \`discountCode\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` ADD \`purchasedByEmail\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` ADD \`purchasedByName\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` ADD \`purchasePriceInCents\` decimal(12,4) NOT NULL DEFAULT '0.0000'`);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_90f560410b14dc61d8ef117687\` ON \`template_purchase\` (\`issuedById\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_88c551ea99d564fa46d5d9575b\` ON \`template_purchase\` (\`discountId\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_87d413e4d1602e8a8d1083b557\` ON \`template_purchase\` (\`discountCode\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_675208c86fde203eb23c0f8923\` ON \`template_purchase\` (\`purchasedByEmail\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c0225697700cdb6ca990688693\` ON \`template_purchase\` (\`purchasedByName\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_18e302b3c2d7f735df100f9406\` ON \`template_purchase\` (\`purchasePriceInCents\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_18e302b3c2d7f735df100f9406\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_c0225697700cdb6ca990688693\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_675208c86fde203eb23c0f8923\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_87d413e4d1602e8a8d1083b557\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_88c551ea99d564fa46d5d9575b\` ON \`template_purchase\``);
        await queryRunner.query(`DROP INDEX \`IDX_90f560410b14dc61d8ef117687\` ON \`template_purchase\``);
        await queryRunner.query(`ALTER TABLE \`push_registration\` CHANGE \`refreshedAt\` \`refreshedAt\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` DROP COLUMN \`purchasePriceInCents\``);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` DROP COLUMN \`purchasedByName\``);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` DROP COLUMN \`purchasedByEmail\``);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` DROP COLUMN \`discountCode\``);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` DROP COLUMN \`discountId\``);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` DROP COLUMN \`issuedById\``);
        await queryRunner.query(`DROP INDEX \`IDX_db3e9fe7c17656e69e62c3f591\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_2cf36a86e7433af0bd39811ba2\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_67d1404848f32b221d6e4b08c1\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_cd63e316df9eecaa467f6c2da0\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_35bff203acae36226f86b4ed84\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_bab0a51e6ee779c13e30e0e9dc\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_6ca78d7d85aa0dfe1e9b125df9\` ON \`discount\``);
        await queryRunner.query(`DROP INDEX \`IDX_5b3957849aad68f2e1ba4bb563\` ON \`discount\``);
        await queryRunner.query(`DROP TABLE \`discount\``);
        await queryRunner.query(`ALTER TABLE \`template_purchase\` CHANGE \`id\` \`id\` int NOT NULL AUTO_INCREMENT`);
    }

}
