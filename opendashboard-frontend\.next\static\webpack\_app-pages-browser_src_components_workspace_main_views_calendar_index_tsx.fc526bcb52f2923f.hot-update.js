"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/WeekView.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isSameDay,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, selectedEvent, onClick, canEditData } = param;\n    const handleDragStart = (e)=>{\n        if (!canEditData) return;\n        e.dataTransfer.setData(\"text/plain\", JSON.stringify(event));\n        // Get the actual event element to match its size and appearance\n        const eventElement = e.currentTarget;\n        const eventRect = eventElement.getBoundingClientRect();\n        const containerElement = document.getElementById(\"week-view-container\");\n        const containerRect = containerElement === null || containerElement === void 0 ? void 0 : containerElement.getBoundingClientRect();\n        // Calculate the actual width available for events in week view (1/7 of container minus time column)\n        const timeColumnWidth = 80; // w-20 = 80px\n        const availableWidth = containerRect ? (containerRect.width - timeColumnWidth) / 7 - 8 : 150; // divide by 7 days, subtract padding\n        // Create a drag image that exactly matches the original event size\n        const dragImg = document.createElement(\"div\");\n        dragImg.innerHTML = '\\n      <div style=\"\\n        background: #1e293b;\\n        color: white;\\n        border: 1px solid #475569;\\n        border-radius: 6px;\\n        padding: 8px 12px;\\n        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n        font-size: 12px;\\n        font-weight: 500;\\n        width: '.concat(Math.max(availableWidth, 120), \"px;\\n        height: \").concat(Math.max(eventRect.height, 40), 'px;\\n        display: flex;\\n        flex-direction: column;\\n        justify-content: center;\\n        opacity: 0.95;\\n      \">\\n        <div style=\"font-weight: 600; margin-bottom: 2px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: white; line-height: 1.3;\">\\n          ').concat(event.title, '\\n        </div>\\n        <div style=\"font-size: 10px; opacity: 0.7; color: #cbd5e1; line-height: 1.3;\">\\n          ').concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n        </div>\\n      </div>\\n    \");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-2000px\";\n        dragImg.style.left = \"-2000px\";\n        dragImg.style.pointerEvents = \"none\";\n        document.body.appendChild(dragImg);\n        // Use the center of the drag image for better visual feedback\n        const dragWidth = Math.max(availableWidth, 120);\n        e.dataTransfer.setDragImage(dragImg, dragWidth / 2, eventRect.height / 2);\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: handleDragStart,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute left-1 right-1 px-2 py-1.5 rounded-md text-xs shadow-sm border cursor-pointer\", \"transition-all duration-200 hover:shadow-md\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700\"),\n        style: style,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium truncate leading-tight\",\n                children: event.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-[10px] mt-0.5 opacity-75\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(new Date(event.start), \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CalendarEventItem;\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onClick, onDrop, canEditData } = param;\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: handleDragOver,\n        onDrop: onDrop,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative border-r border-b border-gray-100\", \"cursor-pointer\"),\n        onClick: onClick,\n        style: {\n            height: \"60px\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TimeSlot;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, onEventDrop } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_4__.useScreenSize)();\n    const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, {\n        weekStartsOn: 0\n    });\n    const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, {\n        weekStartsOn: 0\n    });\n    const days = Array.from({\n        length: 7\n    }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(weekStart, i));\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day));\n    const currentTimePosition = todayIndex !== -1 ? {\n        dayIndex: todayIndex,\n        hour: new Date().getHours(),\n        minutes: new Date().getMinutes()\n    } : null;\n    // Helper function for event duration calculation\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(30, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    // Check if there are any events for the current week\n    const weekEvents = events.filter((event)=>{\n        const eventStart = new Date(event.start);\n        return eventStart >= weekStart && eventStart <= weekEnd;\n    });\n    const handleDrop = (day, hour, e)=>{\n        e.preventDefault();\n        if (!canEditData) return;\n        try {\n            const eventData = JSON.parse(e.dataTransfer.getData(\"text/plain\"));\n            const originalDate = new Date(eventData.start);\n            // Calculate minutes based on drop position within the time slot\n            const timeSlotHeight = 60; // height of time slot in pixels\n            const rect = e.target.getBoundingClientRect();\n            const relativeY = e.clientY - rect.top;\n            const minutes = Math.floor(relativeY / timeSlotHeight * 60);\n            // Create new date with calculated minutes\n            const newDate = new Date(day);\n            newDate.setHours(hour, minutes, 0, 0);\n            // Check if the new date is the same as the original\n            if (newDate.getTime() === originalDate.getTime()) {\n                return;\n            }\n            onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(eventData, newDate);\n        } catch (error) {\n            console.error(\"Error handling drop:\", error);\n        }\n    };\n    if (weekEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b bg-secondary sticky top-0 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex\", isMobile && \"overflow-x-auto\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-r sticky left-0 bg-secondary z-10\", isMobile ? \"w-14\" : \"w-20\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-1\", isMobile && \"min-w-[700px]\"),\n                                children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 text-center cursor-pointer transition-colors\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, selectedDate) ? \"bg-accent\" : \"hover:bg-accent/50\", isMobile ? \"py-3 px-1\" : \"py-4 px-2\"),\n                                        onClick: ()=>setSelectedDate(day),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-xs\" : \"text-sm\"),\n                                                children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, isMobile ? \"EEE\" : \"EEE\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                                children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, \"d\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events this week\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: \"Your week is completely free. Add some events to get organized!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>openAddEventForm(selectedDate),\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-secondary sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex\", isMobile && \"overflow-x-auto\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-r sticky left-0 bg-secondary z-10\", isMobile ? \"w-14\" : \"w-20\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-1\", isMobile && \"min-w-[700px]\"),\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 text-center cursor-pointer transition-colors\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(day, selectedDate) ? \"bg-accent\" : \"hover:bg-accent/50\", isMobile ? \"py-3 px-1\" : \"py-4 px-2\"),\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-xs\" : \"text-sm\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, isMobile ? \"EEE\" : \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative bg-white\", isMobile ? \"overflow-x-auto\" : \"overflow-auto\"),\n                id: \"week-view-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(isMobile && \"min-w-[700px]\"),\n                        children: hours.map((hour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex border-b border-gray-50 hover:bg-gray-25 transition-colors\",\n                                style: {\n                                    height: \"60px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 bg-white z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(), hour), \"h\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-[10px] text-gray-400\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(new Date(), hour), \"a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    days.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                            day: day,\n                                            hour: hour,\n                                            canEditData: canEditData,\n                                            onClick: ()=>{\n                                                if (canEditData) {\n                                                    const newDate = (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(day, hour);\n                                                    openAddEventForm(newDate);\n                                                }\n                                            },\n                                            onDrop: (e)=>handleDrop(day, hour, e),\n                                            children: events.filter((event)=>{\n                                                const eventDate = new Date(event.start);\n                                                return (0,_barrel_optimize_names_addDays_endOfWeek_format_isSameDay_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(eventDate, day) && eventDate.getHours() === hour;\n                                            }).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                                    event: event,\n                                                    selectedEvent: selectedEvent,\n                                                    canEditData: canEditData,\n                                                    style: {\n                                                        top: \"\".concat(new Date(event.start).getMinutes() / 60 * 100, \"%\"),\n                                                        height: \"\".concat(Math.max(30, getEventDurationInMinutes(event) / 60 * 100), \"%\"),\n                                                        zIndex: selectedEvent === event.id ? 20 : 10\n                                                    },\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        const container = document.getElementById(\"week-view-container\");\n                                                        if (container) {\n                                                            savedScrollTop.current = container.scrollTop;\n                                                        }\n                                                        setSelectedEvent(event.id);\n                                                        handleEventClick(event);\n                                                    }\n                                                }, event.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, hour, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 right-0 flex items-center z-50 pointer-events-none\",\n                        style: {\n                            top: \"\".concat(currentTimePosition.hour * 60 + currentTimePosition.minutes, \"px\"),\n                            width: isMobile ? \"calc(100% - 14px)\" : \"auto\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex justify-end pr-3 sticky left-0 z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-red-500 border-2 border-white rounded-full shadow-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\WeekView.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WeekView, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_4__.useScreenSize\n    ];\n});\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"TimeSlot\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/WeekView.tsx\n"));

/***/ })

});