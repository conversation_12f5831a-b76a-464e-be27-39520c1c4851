import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import {DatabaseDefinition} from "opendb-app-db-utils/lib/typings/db";


@Entity()
export class Database {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    name: string

    @Column({type: 'json', nullable: true})
    definition: DatabaseDefinition

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    srcPackageName: string

    @Column({type: 'int', nullable: true})
    srcVersionNumber: number

    @Column({type: 'varchar', nullable: true})
    srcVersionName: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    packageName: string

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateReleaseId: number

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateInstallId: number

    @Index()
    @Column({default: false, type: 'boolean'})
    isMessagingEnabled: boolean

    @Column({type: 'int', nullable: true})
    versionNumber: number

    @Column({type: 'varchar', nullable: true})
    versionName: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    createdById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}



