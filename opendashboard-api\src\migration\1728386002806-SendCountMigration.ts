import { MigrationInterface, QueryRunner } from "typeorm";

export class SendCountMigration1728386002806 implements MigrationInterface {
    name = 'SendCountMigration1728386002806'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`campaign_email\` ADD \`sendCount\` int NOT NULL DEFAULT '0'`);
        await queryRunner.query(`CREATE INDEX \`IDX_ef51296d3ee99cd3971945ce29\` ON \`campaign_email\` (\`sendCount\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_ef51296d3ee99cd3971945ce29\` ON \`campaign_email\``);
        await queryRunner.query(`ALTER TABLE \`campaign_email\` DROP COLUMN \`sendCount\``);
    }

}
