import {CampaignEmailService} from "../../service/campaignEmailService";
import {In} from "typeorm";
import {CampaignEmail} from "../../entity/CampaignEmail";
import {CampaignService} from "../../service/campaign";
import {arrayDeDuplicate} from "opendb-app-db-utils/lib";
import {Campaign} from "../../entity/Campaign";
import {SaveMessagingBouncedUnsubscribed} from "../database";


export const processSGWebhook = async (header: any, body: any[]) => {
    const cES = new CampaignEmailService()
    const cS = new CampaignService()

    const smtpIds: string[] = body.map(e => e['smtp-id'])

    const emails = await cES.find({messageId: In(smtpIds)})
    const campaignIds = arrayDeDuplicate(emails.map(e => e.campaignId))
    const campaigns = await cS.find({id: In(campaignIds)})

    const campaignsMap: { [id: string]: Campaign } = {}
    const emailsMap: { [id: string]: CampaignEmail } = {}

    for (let email of emails) {
        emailsMap[email.messageId] = email
    }
    for (let campaign of campaigns) {
        campaignsMap[campaign.id] = campaign
    }


    const toSave: CampaignEmail[] = []
    for (let event of body) {
        // const email: string = event.email
        const timestamp: number = event.timestamp
        const smtpId: string = event['smtp-id']
        const eventName = event.event as 'open' | 'delivered' | 'bounce' | 'dropped' | 'spamreport' | 'unsubscribe'
        const reason = event.reason as string | undefined

        if (!emailsMap[smtpId]) continue
        const campaignEmail = emailsMap[smtpId]
        const campaign = campaignsMap[campaignEmail.campaignId]
        if (!campaign) continue

        if (eventName === 'bounce' || eventName === 'dropped') {
            campaignEmail.isBounced = true
            campaignEmail.bouncedAt = new Date(timestamp * 1000)
            campaignEmail.bounceReason = reason

            await SaveMessagingBouncedUnsubscribed(campaign.targetDatabaseId, {
                recordIds: [campaignEmail.targetRecordId],
                data: {
                    bounced: true,
                    bouncedReason: campaignEmail.bounceReason
                }
            })
            toSave.push(campaignEmail)
        } else if (eventName === "delivered") {
            campaignEmail.isDelivered = true
            campaignEmail.deliveredAt = new Date(timestamp * 1000)

            toSave.push(campaignEmail)
        } else if (eventName === "spamreport") {
            campaignEmail.isSpamReported = true
            campaignEmail.spamReportedAt = new Date(timestamp * 1000)

            toSave.push(campaignEmail)
        } else if (eventName === "unsubscribe") {
            campaignEmail.isUnsubscribed = true
            campaignEmail.unsubscribedAt = new Date(timestamp * 1000)

            await SaveMessagingBouncedUnsubscribed(campaign.targetDatabaseId, {
                recordIds: [campaignEmail.targetRecordId],
                data: {
                    unsubscribed: true,
                }
            })
            toSave.push(campaignEmail)
        }
    }
    await cES.getRepository().save(toSave)

}

