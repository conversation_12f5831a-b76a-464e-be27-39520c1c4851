import {Template} from "../entity/template";
import {TemplateListing} from "../entity/templateListing";
import {TemplateService} from "../service/template";
import {PaymentCurrency, PaymentProcessor, TemplatePurchase, PurchaseStatus, TemplatePurchaseWorkspaceLicense} from "../entity/templatePurchase";
import {TemplateInstall, TemplateInstallMethod, TemplateInstallPrepareData, TemplateInstallStatus} from "../entity/templateInstall";
import {TemplateReview} from "../entity/templateReview";
import {Brackets, In, <PERSON>Null, <PERSON><PERSON>han, <PERSON><PERSON><PERSON>, Not} from "typeorm";
import {CreatorMember, CreatorMemberRole} from "../entity/creatorMember";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, ServerProcessingError, UnauthorizedError} from "../errors/AppError";
import {GetCreatorMembersN<PERSON><PERSON><PERSON>, Get<PERSON>y<PERSON>reator, MyCreatorStats} from "./creator";
import {TemplateListingService} from "../service/templateListing";
import {DatabaseDependencyMap, ObjectMappings, TemplateHomePage, TemplatePrepareData, TemplateRelease} from "../entity/templateRelease";
import {TemplateReleaseService} from "../service/templateRelease";
import {TemplatePurchaseService} from "../service/templatePurchase";
import {User} from "../entity/User";
import {TemplateReviewService} from "../service/templateReview";
import {CreateUser, UserService} from "../service/user";
import {Database} from "../entity/Database";
import {CreateDatabaseData, DatabaseService} from "../service/database";
import {DatabaseFieldDataType, Match, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {arrayDeDuplicate, generateUUID, isDateObjValid, strReplaceAll} from "opendb-app-db-utils/lib";
import {PageService} from "../service/page";
import {ViewService} from "../service/view";
import {DashboardElementType, DashboardViewDefinition, DocumentViewDefinition, InfoboxElement, LineChartElement, PieChartElement, TableViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {PermissiblePage} from "./page";
import {Page} from "../entity/Page";
import {Creator} from "../entity/creator";
import {getCompanyDbDefinition, getCustomerDbDefinition, getDatabasePackageName} from "opendb-app-db-utils/lib/utils/onboarding";
import {CreateRecordData, OnDuplicateAction, RecordService} from "../service/record";
import {DocumentService} from "../service/document";
import {Document} from "../entity/Document";
import {View} from "../entity/View";
import {Record} from "../entity/Record";
import {inclusivePick} from "../utility/object";
import {TemplateSubmissionService} from "../service/templateSubmission";
import {TagService} from "../service/tag";
import {TemplateSubmission, TemplateSubmissionResult} from "../entity/templateSubmission";
import {TemplateTopPickService} from "../service/templateTopPick";
import {CategoryService} from "../service/category";
import {Tag} from "../entity/tag";
import {CreatorService} from "../service/creator";
import {EmailUser, SendEmailWithContent} from "./email";
import {apiUrl, appUrl, isProduction, isStage} from "../config";
import {datePlusDays, dateToMySQL} from "opendb-app-db-utils/lib/methods/date";
import {nameToSlug} from "../utility/slug";
import {createPaymentCheckoutSession} from "./stripe";
import {CreatorSettings} from "./subscription";
import {WorkspaceService} from "../service/workspace";
import {TemplateInstallService} from "../service/templateInstall";
import {GetMyWorkspace} from "./workspace";
import {WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {broadcastTemplateInstalled} from "../socketio/workspace";
import {getUSDNGNRate} from "./currencyConverter";
import {KeyValueStore} from "../entity/WorkspaceMemberSettings";
import {getVerifiedTransaction, initializeTransaction} from "./paystack";
import {consoleLog} from "./logtail";
import {SelectData} from "opendb-app-db-utils/lib/typings/common";
import {saveRecordsCreatedActivities} from "./database";
import {AutoApproveNonProdTemplateSubmission} from "./admin";
import {validateEmail} from "../utility/validator";
import {DiscountService} from "../service/discount";
import {Discount} from "../entity/discount";
import {redisAcquireLock} from "../connection/redis";
import {CRON_LOCK_KEYS} from "./billing";
import {WorkflowService} from "../service/workflow";
import {Workflow} from "../entity/workflow";
import {WorkflowStatus} from "opendb-app-db-utils/lib/typings/workflow";

const isEqual = require('lodash.isequal');


export interface MyTemplate {
    template: Template
    listing: TemplateListing
    stats: {
        purchasedCount: number
        installedCount: number
        reviewsCount: number
        averageRating: number
        releasesCount: number
    }
}

export interface GetMyTemplatesParams {
    perPage?: number
    page?: number
    isFeaturedInMarketplace?: number
}

export const GetMyTemplates = async (userId: string, creatorId: string, inIds: string[] = [], params: GetMyTemplatesParams = {}): Promise<MyTemplate[]> => {
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    let offset = 0
    let page = Number(params.page)
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 20
    if (Number.isNaN(page) || page < 1) page = 1
    if (page > 1) {
        offset = (page - 1) * perPage
    }

    const qB1 = new TemplateService().getRepository()
        .createQueryBuilder("t")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplatePurchase, "tP")
                .where("tP.templateId = t.id")
        }, "sT_purchasedCount")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplateInstall, "tI")
                .where("tI.templateId = t.id")
        }, "sT_installedCount")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplateReview, "tR")
                .where("tR.templateId = t.id AND tR.parentId is NULL")
        }, "sT_reviewsCount")
        .addSelect(qb => {
            return qb.select("AVG(tR.rating)")
                .from(TemplateReview, "tR")
                .where("tR.templateId = t.id AND tR.rating IS NOT NULL")
        }, "sT_averageRating")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplateRelease, "tR")
                .where("tR.templateId = t.id")
        }, "sT_releasesCount")
        .where({creatorId})
        .offset(offset)
        .limit(perPage)

    if (inIds && inIds.length > 0) {
        qB1.andWhere({id: In(inIds)})
    }
    if (params.isFeaturedInMarketplace) {
        qB1.andWhere({isFeaturedInMarketplace: true})
    }
    const r1 = await qB1.getRawMany()

    const tNoListing: Omit<MyTemplate, 'listing'>[] = r1.map(r => {
        const result: Omit<MyTemplate, 'listing'> = {
            template: {} as Template,
            // listing: {} as TemplateListing,
            stats: {
                purchasedCount: 0,
                installedCount: 0,
                reviewsCount: 0,
                averageRating: 0,
                releasesCount: 0
            }
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "t") {
                result.template[field] = r[key]
            }
                // else if (pre === "tL") {
                //     result.listing[field] = r[key]
            // }
            else if (pre === "sT") {
                result.stats[field] = Number(r[key])
            }
        }
        return result
    })

    const tMap: { [id: string]: Omit<MyTemplate, 'listing'> } = {}
    for (let t of tNoListing) {
        tMap[t.template.id] = t
    }
    const ids = Object.keys(tMap)

    const sQ1 = new TemplateListingService().getRepository()
        .createQueryBuilder()
        .select("MAX(id)", "maxId")
        .where({templateId: In(ids)})
        .groupBy("templateId")
    if (params.isFeaturedInMarketplace) {
        sQ1.andWhere({isFeaturedInMarketplace: true})
    }

    const q2 = new TemplateListingService().getRepository()
        .createQueryBuilder("tL")
        .select()
        .where(`id IN (${sQ1.getQuery()})`)
        .setParameters(sQ1.getParameters())
        .orderBy({"name": "ASC"})

    const listings = await q2.getMany()

    const templates: MyTemplate[] = []

    for (let listing of listings) {
        const myT = tMap[listing.templateId]
        if (!myT) continue
        templates.push({
            ...myT,
            listing
        })
    }
    return templates
}

export const GetMyTemplate = async (userId: string, creatorId: string, templateId: string): Promise<MyTemplate> => {
    const template = (await GetMyTemplates(userId, creatorId, [templateId]))[0]
    if (!template) throw new NotfoundError(ErrorMessage.EntityNotFound)
    return template
}

export interface CreateTemplateData {
    name: string
}

export const CreateTemplate = async (userId: string, creatorId: string, data: CreateTemplateData) => {
    if (!data.name) {
        throw new RequiredParameterError("name");
    }
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    const s = new TemplateService()
    const tLS = new TemplateListingService()

    // create the template, template listing
    const template = await s.insert({
        createdById: userId,
        creatorId,
    })
    const listing = await tLS.insert({
        templateId: template.id,
        name: data.name,
        createdById: userId,
    })
    const myTemplate: MyTemplate = {
        template,
        listing,
        stats: {
            purchasedCount: 0,
            installedCount: 0,
            reviewsCount: 0,
            averageRating: 0,
            releasesCount: 0
        }
    }
    return {
        myTemplate
    }

}

const getMyCreatorTemplate = async (userId: string, creatorId: string, templateId: string) => {
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    const template = await new TemplateService().findOne({id: templateId})
    if (!template || template.creatorId !== creatorId) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    return {
        myCreator, template
    }
}

export const GetTemplateReleases = async (userId: string, creatorId: string, templateId: string, inIds: string[] = []): Promise<TemplateRelease[]> => {
    await getMyCreatorTemplate(userId, creatorId, templateId)

    const s = new TemplateReleaseService()
    const repo = s.getRepository()

    const qBQuery = repo.createQueryBuilder("tR")
        // .where("tR.templateId=:templateId")
        .where({
            templateId,
            isReady: true
        })
        .orderBy("tR.createdAt", "DESC")
    if (inIds && inIds.length > 0) {
        qBQuery.andWhere({id: In(inIds)})
    }

    return await qBQuery.getMany()
}

export const GetTemplateRelease = async (userId: string, creatorId: string, templateId: string, releaseId: string) => {
    const release = (await GetTemplateReleases(userId, creatorId, templateId, [releaseId]))[0]
    if (!release) throw new NotfoundError(ErrorMessage.EntityNotFound)
    return release
}

export interface TemplateReleaseWithAssets {
    databases: Database[]
    pages: Page[]
    views: View[]
    records: Record[]
    docs: Document[]
    workflows: Workflow[]
    release: TemplateRelease
}

export const GetTemplateReleaseWithAssets = async (templateId: string, releaseId: string | 'featured', allProps = false): Promise<TemplateReleaseWithAssets> => {
    let release: TemplateRelease = null
    // if (allProps) {
    //     const qB = new TemplateReleaseService()
    //         .getRepository()
    //         .createQueryBuilder()
    //         .select("*")
    //         .where({templateId})
    //     if (releaseId === 'featured') {
    //         qB.andWhere({isFeaturedInMarketplace: true})
    //     } else {
    //         qB.andWhere({id: Number(releaseId)})
    //     }
    //     release = await qB.getOne()
    // } else {
    //     if (releaseId === 'featured') {
    //         release = await (new TemplateReleaseService().findOne({templateId, isFeaturedInMarketplace: true}))
    //     } else {
    //         release = await (new TemplateReleaseService().findOne({templateId, id: Number(releaseId)}))
    //     }
    // }
    const tRS = new TemplateReleaseService()
    if (releaseId === 'featured') {
        release = await (tRS.getRepository().findOne({
            where: {templateId, isFeaturedInMarketplace: true},
            select: allProps ? tRS.getCols() : undefined
        }))
    } else {
        release = await (tRS.getRepository().findOne({
            where: {templateId, id: Number(releaseId)},
            select: allProps ? tRS.getCols() : undefined
        }))
    }

    if (!release) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (!release.isReady) throw new NotfoundError(ErrorMessage.UnableToFetchEntity)

    const databases = await new DatabaseService().find({templateReleaseId: Number(releaseId)})
    const pages = await new PageService().find({templateReleaseId: Number(releaseId)})
    const views = await new ViewService().find({templateReleaseId: Number(releaseId)})
    const records = await new RecordService().find({templateReleaseId: Number(releaseId)})
    const docs = await new DocumentService().find({templateReleaseId: Number(releaseId)})
    const workflows = await new WorkflowService().find({templateReleaseId: Number(releaseId)})

    return {
        databases,
        pages,
        views,
        records,
        docs,
        release,
        workflows
    }
}

export const DiscardTemplateRelease = async (userId: string, creatorId: string, templateId: string, releaseId: string) => {
    const release = await GetTemplateRelease(userId, creatorId, templateId, releaseId)
    if (!release) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (release.isFeaturedInMarketplace) {
        throw new BadRequestError(ErrorMessage.ReleaseInMarketPlace)
    }
    if (release.replacedAt) {
        throw new BadRequestError(ErrorMessage.ReleaseReplaced)
    }
    const s = new TemplateReleaseService()
    await s.remove({id: Number(releaseId)})

    return true
}

export interface UpdateTemplateReleaseData {
    releaseNote: string
    releaseId: string
}

export const UpdateTemplateRelease = async (userId: string, creatorId: string, templateId: string, data: UpdateTemplateReleaseData) => {
    if (!data.releaseId) {
        throw new RequiredParameterError("releaseId");
    }
    if (!data.releaseNote) {
        throw new RequiredParameterError("releaseNote");
    }
    const releaseId = data.releaseId
    const release = await GetTemplateRelease(userId, creatorId, templateId, releaseId)
    if (!release) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (release.isFeaturedInMarketplace) {
        throw new BadRequestError(ErrorMessage.ReleaseInMarketPlace)
    }
    if (release.replacedAt) {
        throw new BadRequestError(ErrorMessage.ReleaseReplaced)
    }
    const s = new TemplateReleaseService()
    await s.update({id: Number(releaseId)}, {releaseNote: data.releaseNote})

    return true
}

export interface BuildTemplateReleaseDependenciesData extends TemplatePrepareData {
}

export interface BuildTemplateReleaseDependenciesResponseData {
    dbMap: DatabaseMap
    // dbDependency: {
    //     orderedNodes: SelectData[]
    //     rootNodes: GraphNode[]
    //     dependencyMap: DatabaseDependencyMap
    // }
    pageMap: {
        [p: string]: {
            page: PermissiblePage
            dependsOn: string[]
        }
    }
    workflowMap: {
        [p: string]: {
            workflow: Workflow
            dependsOn: string[]
        }
    }
    // template: Template
    // myCreator: MyCreator
}

export const BuildTemplateReleaseDependencies = async (userId: string, creatorId: string, templateId: string, data: BuildTemplateReleaseDependenciesData) => {
    if (!data.databases) {
        throw new RequiredParameterError("databases")
    }
    if (!data.pages) {
        throw new RequiredParameterError("pages")
    }
    if (!data.workflows) {
        throw new RequiredParameterError("workflows")
    }
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    const template = await (new TemplateService().findOne({creatorId, id: templateId}))
    if (!template) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const map: DatabaseMap = {}

    const viewIds: string[] = []
    for (let value of Object.values(data.pages)) {
        viewIds.push(...value.viewIds)
    }
    for (let value of Object.values(data.databases)) {
        viewIds.push(...value.viewIds)
    }

    const {dbMap, dbDependency, pageMap, workflowMap} = await buildPageDatabaseDependencies(myCreator.creator.workspaceId, Object.keys(data.pages), Object.keys(data.databases), viewIds, Object.keys(data.workflows), map)

    return {dbMap, dbDependency, pageMap, workflowMap, template, myCreator}
}

export interface DatabaseMap {
    [key: string]: {
        database: Database,
        processed?: boolean,
        dependsOn: string[]
    }
}

export const buildDatabaseDependencies = async (workspaceId: string, databaseIds: string[], map: DatabaseMap) => {
    let bucket: string[] = [...databaseIds]

    const s = new DatabaseService()
    while (bucket.length > 0) {
        const ids = [...bucket]
        bucket = []

        const toFetch: string[] = ids.filter(id => !map[id])
        const fetchDbs = await s.find({id: In(toFetch), workspaceId})
        for (let fetchDb of fetchDbs) {
            map[fetchDb.id] = {database: fetchDb, dependsOn: []}
        }
        const databases: Database[] = []
        for (let id of ids) {
            const db = map[id]
            if (!db) throw new NotfoundError(ErrorMessage.EntityNotFound + ' - ' + `Database: ${id}`)
            databases.push(db.database)
        }
        for (const database of databases) {
            for (const col of Object.values(database.definition.columnsMap)) {
                if (col.type === DatabaseFieldDataType.Linked && col.databaseId) {
                    // linkedColIdsMap[col.id] = col.databaseId
                    // linkedColIds.push(col.id)
                    // console.log(`Linked: ${database.id} -> ${col.databaseId}`)
                    if (col.databaseId !== database.id) {
                        // && (!map[col.databaseId] || !map[col.databaseId].processed)
                        bucket.push(col.databaseId)
                    }
                    if (!map[database.id].dependsOn.includes(col.databaseId)) map[database.id].dependsOn.push(col.databaseId)
                }
            }
            map[database.id].processed = true
        }
        for (let id of Object.keys(map)) {
            const mapData = map[id]
            bucket.push(id)
            bucket.push(...mapData.dependsOn)
        }
        bucket = arrayDeDuplicate(bucket).filter(id => !(map[id] && map[id].processed))
        // bucket.push(...Object.values(map).filter(v => !v.processed).map(v => v.database.id))
    }

    // const nodeMap: { [key: string]: GraphNode } = {}
    // let rootNodes: GraphNode[] = [];
    // for (const db of Object.values(map)) {
    //     let currNode = nodeMap[db.database.id]
    //     if (!currNode) currNode = nodeMap[db.database.id] = new GraphNode(db.database.id, db.database.name)
    //
    //     if (db.dependsOn.length > 0) {
    //         for (let id of db.dependsOn) {
    //             let parentNode = nodeMap[id]
    //             if (!parentNode) parentNode = nodeMap[id] = new GraphNode(id, map[id].database.name)
    //             parentNode.addChild(currNode)
    //             // currNode.addParent(parentNode)
    //         }
    //     } else {
    //         rootNodes.push(currNode)
    //     }
    // }
    // const orderedNodes = iterativeTopologicalSort(rootNodes)
    const orderedNodes: SelectData[] = []

    const dependencyMap: DatabaseDependencyMap = {}
    for (const db of Object.values(map)) {
        dependencyMap[db.database.id] = {dependsOn: db.dependsOn}
        orderedNodes.push({label: db.database.name, value: db.database.id})
    }

    return {orderedNodes, dependencyMap}
}

export const buildPageDatabaseDependencies = async (workspaceId: string, pageIds: string[], databaseIds: string[], viewIds: string[], workflowIds: string[], dbMap: DatabaseMap) => {
    let dbIds: string[] = [...databaseIds]

    const s = new PageService()
    const vS = new ViewService()
    const wS = new WorkflowService()


    let pages: Page[] = []

    if (pageIds.length > 0 && dbIds.length > 0) {
        const brackets = new Brackets(qb => {
            qb.where({id: In(pageIds)});
            qb.orWhere({databaseId: In(databaseIds)})
        })
        const qB = s.getRepository()
            .createQueryBuilder()
            .select()
            .where({workspaceId})
            .andWhere(brackets)
        pages = await qB.getMany()
    } else if (pageIds.length > 0) {
        pages = await s.find({workspaceId, id: In(pageIds)})
    } else if (dbIds.length > 0) {
        pages = await s.find({workspaceId, databaseId: In(dbIds)})
    }
    let views: View[] = []
    if (viewIds.length > 0) {
        views = await vS.getRepository()
            .createQueryBuilder()
            .select()
            .where({pageId: In(pageIds)})
            .orWhere({id: In(viewIds)})
            .getMany()
    } else {
        views = await vS.find({pageId: In(pageIds)})
    }
    // for (const page of pages) {
    //     if (page.databaseId) dbIds.push(page.databaseId)
    // }
    for (let pageId of pageIds) {
        const page = pages.find(p => p.id === pageId)
        if (!page) throw new NotfoundError(ErrorMessage.EntityNotFound + ' - ' + `Page: ${pageId}`)
        if (page.databaseId) dbIds.push(page.databaseId)
    }
    for (const view of views) {
        if (view.type === ViewType.Document) continue
        const definition = view.definition
        if ((definition as TableViewDefinition).databaseId) {
            dbIds.push((definition as TableViewDefinition).databaseId)
            continue
        }
        if (definition.type === ViewType.Dashboard) {
            const dashboardDefinition = definition as DashboardViewDefinition
            if (dashboardDefinition && dashboardDefinition.definition && dashboardDefinition.definition.elementMap) {
                for (let element of Object.values(dashboardDefinition.definition.elementMap)) {
                    let databaseId = ''
                    switch (element.type) {
                        case DashboardElementType.BarChart:
                        case DashboardElementType.FunnelChart:
                        case DashboardElementType.LineChart:
                            databaseId = (element as LineChartElement).recordsResolve?.databaseId
                            break
                        case DashboardElementType.PieChart:
                            databaseId = (element as PieChartElement).recordsResolve?.databaseId
                            break
                        case DashboardElementType.Infobox:
                            databaseId = (element as InfoboxElement).valueResolve?.databaseId
                            break

                    }
                    if (databaseId) dbIds.push(databaseId)
                }
            }

        }
    }
    const pageMap: { [id: string]: { page: PermissiblePage, dependsOn: string[] } } = {}
    for (let page of pages) {
        pageMap[page.id] = {page: {page, views: [], accessLevel: page.accessLevel}, dependsOn: []}
    }
    for (const view of views) {
        if (!pageMap[view.pageId]) throw new NotfoundError(ErrorMessage.EntityNotFound + ' - ' + `Page: ${view.pageId}`)
        pageMap[view.pageId].page.views.push(view)
    }

    const workflowMap: { [id: string]: { workflow: Workflow, dependsOn: string[] } } = {}
    if (workflowIds.length > 0) {
        const workflows = await wS.find({id: In(workflowIds), workspaceId, status: WorkflowStatus.Published})
        for (let workflow of workflows) {
            workflowMap[workflow.id] = {workflow, dependsOn: []}

            for (let entry of Object.entries(workflow.definition.map)) {
                const node = entry[1]
                const config = node.data?.configs
                if (config && config.databaseId) {
                    dbIds.push(config.databaseId)
                    workflowMap[workflow.id].dependsOn.push(config.databaseId)
                }
            }
            workflowMap[workflow.id].dependsOn = arrayDeDuplicate(workflowMap[workflow.id].dependsOn)
        }
    }
    dbIds = arrayDeDuplicate(dbIds)
    const dbDependency = await buildDatabaseDependencies(workspaceId, dbIds, dbMap)

    const extraDbIds = dbDependency.orderedNodes.map(n => n.value).filter(id => !databaseIds.includes(id))
    if (extraDbIds.length > 0) {
        const extraPages = await s.find({workspaceId, databaseId: In(extraDbIds)})
        for (let page of extraPages) {
            pageMap[page.id] = {page: {page, views: [], accessLevel: page.accessLevel}, dependsOn: []}
        }
    }
    for (const view of views) {
        let databaseId = ''
        if (view.type === ViewType.Document) continue
        const definition = view.definition
        if ((definition as TableViewDefinition).databaseId) {
            databaseId = (definition as TableViewDefinition).databaseId
        } else if (definition.type === ViewType.Dashboard) {
            const dashboardDefinition = definition as DashboardViewDefinition
            if (dashboardDefinition && dashboardDefinition.definition && dashboardDefinition.definition.elementMap) {
                for (let element of Object.values(dashboardDefinition.definition.elementMap)) {
                    switch (element.type) {
                        case DashboardElementType.BarChart:
                        case DashboardElementType.FunnelChart:
                        case DashboardElementType.LineChart:
                            databaseId = (element as LineChartElement).recordsResolve?.databaseId
                            break
                        case DashboardElementType.PieChart:
                            databaseId = (element as PieChartElement).recordsResolve?.databaseId
                            break
                        case DashboardElementType.Infobox:
                            databaseId = (element as InfoboxElement).valueResolve?.databaseId
                            break
                    }
                }
            }
        }
        if (databaseId) {
            pageMap[view.pageId].dependsOn.push(databaseId)
            pageMap[view.pageId].dependsOn = arrayDeDuplicate(pageMap[view.pageId].dependsOn)
        }
    }

    return {pageMap, dbMap, dbDependency, workflowMap}
}

export const PrepareTemplateRelease = async (userId: string, creatorId: string, templateId: string, data: BuildTemplateReleaseDependenciesData) => {
    const {myCreator, pageMap, dbDependency, dbMap, workflowMap, template} = await BuildTemplateReleaseDependencies(userId, creatorId, templateId, data)
    const s = new TemplateReleaseService()

    const lastRelease = await s.findOne({templateId}, {id: "DESC"})
    const releaseData: Partial<TemplateRelease> = {
        templateId: template.id,
        prepareData: data,
        createdById: userId,
        dbProcessingOrder: dbDependency.orderedNodes,
        dependencyMap: dbDependency.dependencyMap,
        versionNumber: (lastRelease ? lastRelease.versionNumber : 0) + 1
    }
    const release = await s.insert(releaseData)

    // copy the databases and the pages.
    const objectMappings: ObjectMappings = {
        databases: {},
        records: {},
        pages: {},
        views: {},
        documents: {},
        workflows: {}
    }
    // const databases: Database[] = []
    const extraDatabaseIds: string[] = []
    const selectedDbIds: string[] = Object.keys(data.databases)

    const creator = myCreator.creator

    const dS = new DatabaseService()
    const rS = new RecordService()

    console.log({orderedNodes: dbDependency.orderedNodes})
    // process the databases first and records next
    const databaseToInsert: {
        data: CreateDatabaseData,
        oldId: string
        newId: string
    }[] = []
    for (let dbToClone of dbDependency.orderedNodes) {
        if (!selectedDbIds.includes(dbToClone.value)) extraDatabaseIds.push(dbToClone.value)

        const databaseId = dbToClone.value
        const db = dbMap[databaseId]

        // const templateDb: Partial<Database> = {...db.database}
        const oldDefinition = {...db.database.definition}
        let definitionText = JSON.stringify(oldDefinition)
        for (let oldId of Object.keys(objectMappings.databases)) {
            const newId = objectMappings.databases[oldId]
            definitionText = strReplaceAll(oldId, definitionText, newId)
        }
        const newDefinition = JSON.parse(definitionText)
        const packageName = getDatabasePackageName({
            definition: newDefinition,
            domain: creator.domain,
            templateName: databaseId,
            versionNumber: release.versionNumber,
            versionName: ''
        })
        const templateDbData: CreateDatabaseData = {
            id: generateUUID(),
            createdById: null,
            definition: newDefinition,
            name: db.database.name,
            packageName: db.database.packageName || db.database.srcPackageName || packageName,
            versionName: db.database.versionName || String(release.versionNumber),
            versionNumber: db.database.versionNumber || release.versionNumber,
            workspaceId: "",
            templateReleaseId: release.id,
            srcPackageName: db.database.srcPackageName,
            srcVersionNumber: db.database.srcVersionNumber,
            srcVersionName: db.database.srcVersionName,
            isMessagingEnabled: db.database.isMessagingEnabled,
        }
        databaseToInsert.push({
            oldId: databaseId,
            newId: templateDbData.id,
            data: {...templateDbData}
        })
        objectMappings.databases[databaseId] = templateDbData.id
        console.log(`Object mapping for database updated for ${templateDbData.name}: `, {databases: objectMappings.databases, oldId: databaseId, newDbId: templateDbData.id, newId: templateDbData.id})

    }
    const checkIdChange = () => {
        // check the Ids to detect change
        for (let databaseToInsertElement of databaseToInsert) {
            if (databaseToInsertElement.newId !== databaseToInsertElement.data.id) {
                throw `Id changed for ${databaseToInsertElement.data.name}: OldId: ${databaseToInsertElement.oldId}, NewId: ${databaseToInsertElement.newId}, NewDbId: ${databaseToInsertElement.data.id}`
            }
        }
    }

    checkIdChange()

    console.log(`Object mapping for database finalized: `, {databases: objectMappings.databases, databaseToInsert})


    // re-execute this to handle all dependencies
    for (const datum of databaseToInsert) {
        const oldDefinition = {...datum.data.definition}
        let definitionText = JSON.stringify(oldDefinition)
        // const replaceText = substituteVars(definitionText, obj)
        for (let oldId of Object.keys(objectMappings.databases)) {
            const newId = objectMappings.databases[oldId]
            definitionText = strReplaceAll(oldId, definitionText, newId)
        }
        datum.data.definition = JSON.parse(definitionText)
    }
    await dS.getRepository().save(databaseToInsert.map(d => d.data))
    // await dS.batchAdd(databaseToInsert.map(d => d.data))

    checkIdChange()

    const recordsData: {
        data: CreateRecordData,
        oldId: string
    }[] = []

    console.log(`Object mapping for database after Db inserted: `, {databases: objectMappings.databases, databaseToInsert})

    for (let d of databaseToInsert) {
        const databaseId = d.oldId
        const database = d.data

        const records = await rS.find({databaseId})

        console.log(`Preparing records for ${d.data.name} `, {oldId: d.oldId, newDbId: database.id, newId: d.newId})

        const newRecordsData: {
            data: CreateRecordData,
            oldId: string
        }[] = []
        for (const record of records) {
            let valuesText = JSON.stringify(record.recordValues)
            for (let oldId of Object.keys(objectMappings.records)) {
                const newId = objectMappings.records[oldId]
                valuesText = strReplaceAll(oldId, valuesText, newId)
            }
            const recordValues: RecordValues = JSON.parse(valuesText)
            const d: CreateRecordData = {
                id: generateUUID(),
                createdById: userId,
                databaseId: database.id,
                recordValues: recordValues,
                uniqueValue: record.uniqueValue,
                updatedById: userId,
                templateReleaseId: release.id,
                meta: {
                    template_source: {
                        oldId: record.id
                    }
                }
            }
            newRecordsData.push({
                oldId: record.id,
                data: d
            })
            objectMappings.records[record.id] = d.id
        }
        console.log(`Records for ${d.data.name} `, {oldId: d.oldId, newId: database.id, count: records.length, newCount: newRecordsData.length})

        recordsData.push(...newRecordsData)
    }
    console.log(`Object mapping for database after Db preparing records: `, {databases: objectMappings.databases, databaseToInsert})

    // re-execute this to handle all dependencies
    for (const r of recordsData) {
        let valuesText = JSON.stringify(r.data.recordValues)
        for (let oldId of Object.keys(objectMappings.records)) {
            const newId = objectMappings.records[oldId]
            valuesText = strReplaceAll(oldId, valuesText, newId)
        }
        r.data.recordValues = JSON.parse(valuesText)
    }

    console.log({recordsData})
    const addedRecords = await rS.addRecords(recordsData.map(r => r.data))

    console.log('All Added Records:', {count: addedRecords.length, rawCount: recordsData.length})
    console.log(`Object mapping for database after Db after adding records: `, {databases: objectMappings.databases, databaseToInsert})

    // process the pages
    // process the views
    // process the documents
    // process the workflows
    await clonePagesAndViewsForTemplate(userId, myCreator.creator, release, extraDatabaseIds, pageMap, objectMappings)
    await cloneWorkflowsForTemplate(userId, myCreator.creator, release, workflowMap, objectMappings)

    const homePage: TemplateHomePage = data.homePage || {}
    homePage.databaseId = homePage.databaseId ? objectMappings.databases[homePage.databaseId] || '' : ''
    homePage.pageId = homePage.pageId ? objectMappings.pages[homePage.pageId] || '' : ''
    homePage.viewId = homePage.viewId ? objectMappings.views[homePage.viewId] || '' : ''

    const tRS = new TemplateReleaseService()
    await tRS.update({id: release.id}, {isReady: true, objectMappings, meta: {homePage}})

    release.isReady = true
    release.objectMappings = objectMappings

    return {release}
}

const clonePagesAndViewsForTemplate = async (userId: string, creator: Creator, release: TemplateRelease, extraDatabaseIds: string[], pageMap: { [id: string]: { page: PermissiblePage, dependsOn: string[] } }, objectMappings: ObjectMappings) => {
    const dS = new DocumentService()
    const vS = new ViewService()
    const pS = new PageService()

    const pageData: Partial<Page>[] = []
    const viewData: Partial<View>[] = []
    const docsData: Partial<Document>[] = []


    const docViewIds: string[] = []


    if (extraDatabaseIds.length > 0) {
        const pages = await (pS.find({workspaceId: creator.workspaceId, databaseId: In(extraDatabaseIds)}))
        for (let page of pages) {
            const oldDbId = page.databaseId
            const newDbId = objectMappings.databases[oldDbId]
            if (!newDbId) throw new ServerProcessingError("New Db not found for dependent database")
            if (!pageMap[page.id]) {
                pageMap[page.id] = {page: {page, views: [], accessLevel: page.accessLevel}, dependsOn: []}
            }
            const oldPage = pageMap[page.id]
            const pgData: Partial<Page> = {
                createdById: userId,
                icon: oldPage.page.page.icon,
                name: oldPage.page.page.name,
                ownerId: "",
                workspaceId: "",
                id: generateUUID(),
                visibility: oldPage.page.page.visibility,
                accessLevel: oldPage.page.page.accessLevel,
                viewsOrder: [],
                templateReleaseId: release.id,
                databaseId: newDbId
            }
            pageData.push(pgData)

            const viewDef: TableViewDefinition = {
                type: ViewType.Table,
                databaseId: newDbId,
                filter: {
                    conditions: [],
                    match: Match.All
                },
                sorts: [],
                columnsOrder: [],
                columnPropsMap: {},
                lockContent: false,
            }
            const viewsData: Partial<View> = {
                pageId: pgData.id,
                name: `All ${page.name}`,
                type: ViewType.Table,
                definition: viewDef,
                createdById: userId,
                id: generateUUID(),
                templateReleaseId: release.id,
            }
            viewData.push(viewsData)


            objectMappings.pages[page.id] = pgData.id
        }
    }
    // process the pages
    for (const oldId of Object.keys(pageMap)) {
        const oldPage = pageMap[oldId]
        if (oldPage.page.page.databaseId && extraDatabaseIds.includes(oldPage.page.page.databaseId)) {
            // already processed, skip
            continue
        }
        const pgData: Partial<Page> = {
            createdById: userId,
            icon: oldPage.page.page.icon,
            name: oldPage.page.page.name,
            ownerId: "",
            workspaceId: "",
            id: generateUUID(),
            visibility: oldPage.page.page.visibility,
            accessLevel: oldPage.page.page.accessLevel,
            viewsOrder: oldPage.page.page.viewsOrder || oldPage.page.views.map(v => v.id),
            templateReleaseId: release.id,
            databaseId: oldPage.page.page.databaseId ? objectMappings.databases[oldPage.page.page.databaseId] : null
        }
        pageData.push(pgData)

        objectMappings.pages[oldId] = pgData.id

        let viewsOrderText = pgData.viewsOrder.join(',')


        // process the views
        for (const view of oldPage.page.views) {
            let defText = JSON.stringify(view.definition)
            for (let oldId of Object.keys(objectMappings.databases)) {
                const newId = objectMappings.databases[oldId]
                defText = strReplaceAll(oldId, defText, newId)
            }
            const vData: Partial<View> = {
                createdById: userId,
                definition: JSON.parse(defText),
                name: view.name,
                pageId: pgData.id,
                type: view.type,
                id: generateUUID(),
                templateReleaseId: release.id
            }
            viewData.push(vData)
            objectMappings.views[view.id] = vData.id

            viewsOrderText = strReplaceAll(view.id, viewsOrderText, vData.id)

            if (view.type === ViewType.Document) {
                docViewIds.push(view.id)
            }
        }
        pgData.viewsOrder = viewsOrderText.split(",")
    }

    await pS.batchAdd(pageData)
    await vS.batchAdd(viewData)

    if (docViewIds.length) {
        // process the documents
        const oldDocuments = await dS.find({viewId: In(docViewIds)})

        for (let oldDoc of oldDocuments) {
            const docData: Partial<Document> = {
                id: generateUUID(),
                workspaceId: '',
                name: oldDoc.name,
                contentJSON: oldDoc.contentJSON,
                contentText: oldDoc.contentText,
                viewId: objectMappings.views[oldDoc.viewId],
                createdById: userId,
                updatedById: userId,
                templateReleaseId: release.id
            }
            docsData.push(docData)

            objectMappings.documents[oldDoc.id] = docData.id
        }

        if (docsData.length > 0) {
            await dS.batchAdd(docsData)
        }
    }

}

const cloneWorkflowsForTemplate = async (userId: string, creator: Creator, release: TemplateRelease, workflowMap: { [id: string]: { workflow: Workflow, dependsOn: string[] } }, objectMappings: ObjectMappings) => {
    const wS = new WorkflowService()

    const workflowData: Partial<Workflow>[] = []

    for (const oldId of Object.keys(workflowMap)) {
        const oldWorkflow = workflowMap[oldId]
        const wfData: Partial<Workflow> = {
            createdById: userId,
            name: oldWorkflow.workflow.name,
            description: oldWorkflow.workflow.description,
            id: generateUUID(),
            templateReleaseId: release.id,
            status: WorkflowStatus.Published,
            definition: oldWorkflow.workflow.definition,
            triggerType: oldWorkflow.workflow.triggerType,
            triggerObjectId: oldWorkflow.workflow.triggerObjectId,
            workspaceId: "",
        }
        workflowData.push(wfData)

        objectMappings.workflows[oldId] = wfData.id

    }
    // in case there are additional references in the workflow definition
    for (let workflow of workflowData) {
        let defText = JSON.stringify(workflow.definition)
        for (let [id, newId] of Object.entries(objectMappings.databases)) {
            defText = strReplaceAll(id, defText, newId)
            if (workflow.triggerObjectId && workflow.triggerObjectId === id) {
                workflow.triggerObjectId = newId
            }
        }
        for (let [id, newId] of Object.entries(objectMappings.workflows)) {
            defText = strReplaceAll(id, defText, newId)
            if (workflow.triggerObjectId && workflow.triggerObjectId === id) {
                workflow.triggerObjectId = newId
            }
        }
        for (let [id, newId] of Object.entries(objectMappings.records)) {
            defText = strReplaceAll(id, defText, newId)
            if (workflow.triggerObjectId && workflow.triggerObjectId === id) {
                workflow.triggerObjectId = newId
            }
        }
        workflow.definition = JSON.parse(defText)
    }

    await wS.batchAdd(workflowData)
}

export const GetTemplateListings = async (userId: string, creatorId: string, templateId: string): Promise<TemplateListing[]> => {
    await getMyCreatorTemplate(userId, creatorId, templateId)
    return new TemplateListingService().find({templateId})
}

export interface UploadTemplateListingData {
    name: string
    shortDescription: string
    fullDescription: string
    images: string[]
    categoryId: string
    tagsText: string
    isPaid: boolean
    isMultiLicensePricing: boolean
    singleLicensePrice: number
    multiLicensePrice: number
}

export const UpdateTemplateListing = async (userId: string, creatorId: string, templateId: string, reqData: UploadTemplateListingData): Promise<{ listing: TemplateListing }> => {
    if (!reqData.name) throw new RequiredParameterError('name')
    if (!reqData.shortDescription) throw new RequiredParameterError('shortDescription')
    if (!reqData.fullDescription) throw new RequiredParameterError('fullDescription')
    if (!reqData.images) throw new RequiredParameterError('images')
    if (!Array.isArray(reqData.images) || reqData.images.length === 0) throw new RequiredParameterError("images")
    if (!reqData.categoryId) throw new RequiredParameterError('categoryId')
    if (reqData.isPaid) {
        if (reqData.isMultiLicensePricing) {
            if (Number.isNaN(Number(reqData.singleLicensePrice)) || Number(reqData.singleLicensePrice) < 5) throw new InvalidParameterError("Single license price needs to be atleast $5")
            if (Number.isNaN(Number(reqData.multiLicensePrice)) || Number(reqData.multiLicensePrice) < 5) throw new InvalidParameterError("Multi license price needs to be atleast $5")

        } else {
            if (!reqData.singleLicensePrice) throw new RequiredParameterError('price')
            if (Number.isNaN(Number(reqData.singleLicensePrice)) || Number(reqData.singleLicensePrice) < 5) throw new InvalidParameterError("Price needs to be atleast $5")
        }
    }
    const data = inclusivePick<UploadTemplateListingData>(reqData)

    await getMyCreatorTemplate(userId, creatorId, templateId)

    const s = new TemplateListingService()
    const tS = new TagService()
    const latestListing = await s.findOne({templateId}, {versionNumber: "DESC"})
    let listing = latestListing

    const listingData = inclusivePick<UploadTemplateListingData>(latestListing)
    if (isEqual(listingData, data)) return {listing}

    const tags = await tS.tagTextToTags(data.tagsText)
    const tagIds = tags.map(t => t.id)

    const update: Partial<TemplateListing> = {...data, tagIds, templateId}

    if (listing.isFeaturedInMarketplace) {
        listing = await s.insert({...update, versionNumber: latestListing.versionNumber + 1, createdById: userId})
    } else {
        await s.update({id: listing.id}, update)
        listing = {...listing, ...update}
    }

    return {
        listing
    }
}

export interface DiscardTemplateListingData {
    id: string
}

export const DiscardTemplateListing = async (userId: string, creatorId: string, templateId: string, data: DiscardTemplateListingData) => {
    if (!data.id) throw new RequiredParameterError("id")
    const listingId = data.id
    await getMyCreatorTemplate(userId, creatorId, templateId)

    const s = new TemplateListingService()
    const listing = await s.findOne({id: Number(listingId)}, {createdAt: "DESC"})

    if (!listing) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (listing.isFeaturedInMarketplace) {
        throw new BadRequestError(ErrorMessage.ListingInMarketPlace)
    }
    if (listing.replacedAt) {
        throw new BadRequestError(ErrorMessage.ListingReplaced)
    }
    await s.remove({id: Number(listingId)})

    return true
}

export interface MyTemplatePurchase {
    purchase: TemplatePurchase
    listing: TemplateListing
    // template: Template
}

export const GetMyTemplatePurchases = async (userId: string, creatorId: string, params: GetMyTemplatePurchasesParams): Promise<MyTemplatePurchase[]> => {
    const {templateId, purchaseId} = params
    const myCreator = await GetMyCreator(userId, creatorId)
    if (!myCreator || ![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(myCreator.creatorMember.role)) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    let offset = 0
    let page = Number(params.page)
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 20
    if (Number.isNaN(page) || page < 1) page = 1
    if (page > 1) {
        offset = (page - 1) * perPage
    }
    const qB = new TemplatePurchaseService()
        .getRepository()
        .createQueryBuilder("tP")
        // .addSelect('t')
        .addSelect('tL')
        .leftJoin(Template, "t", "tP.templateId=t.id")
        .leftJoin(TemplateListing, "tL", "tP.templateListingId=tL.id")
        .where("t.creatorId=:creatorId", {creatorId})
        .andWhere({purchaseStatus: PurchaseStatus.Settled})
        .offset(offset)
        .limit(perPage)
        .orderBy('tP.createdAt', "DESC")

    if (templateId) {
        qB.andWhere({templateId})
    }
    if (purchaseId) {
        qB.andWhere("tP.id=:purchaseId", {purchaseId})
    }

    const r1 = await qB.getRawMany()

    return r1.map(r => {
        const result: MyTemplatePurchase = {
            listing: {} as TemplateListing,
            purchase: {} as TemplatePurchase,
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "tL") {
                result.listing[field] = r[key]
            } else if (pre === "tP") {
                result.purchase[field] = r[key]
            }
        }
        return result
    })
}


export interface AddManualPurchaseData {
    templateId: string
    name: string
    email: string
    sendNotification: boolean
}

export const AddManualPurchase = async (userId: string, creatorId: string, data: AddManualPurchaseData): Promise<MyTemplatePurchase> => {
    if (!data.email) throw new RequiredParameterError("email")
    if (!validateEmail(data.email)) throw new InvalidParameterError(ErrorMessage.EmailIsInvalid)
    if (!data.name) throw new RequiredParameterError("name")
    if (!data.templateId) throw new RequiredParameterError("templateId")

    const {templateId, name, email} = data
    const {myCreator, template} = await getMyCreatorTemplate(userId, creatorId, templateId)
    if (!template.isListedInMarketplace || !template.marketplaceListingId) throw new BadRequestError(ErrorMessage.TemplateNotFeaturedInMarketplace_UnableToProceed)
    const listing = await new TemplateListingService().findOne({id: template.marketplaceListingId})

    const s = new UserService()
    let user = await s.findOne({email})
    if (!user) {
        const uData: CreateUser = {
            firstName: "",
            lastName: "",
            email: data.email,
        }
        user = await s.insert(uData)
    }

    const purchases = await new TemplatePurchaseService().find({templateId, purchasedById: user.id, purchaseStatus: PurchaseStatus.Settled, workspaceLicense: TemplatePurchaseWorkspaceLicense.Multi})

    if (purchases.length > 0) {
        return {
            purchase: purchases[0],
            listing
        }
    }
    const purchase = await new TemplatePurchaseService().insert({
        purchasedByName: data.name,
        purchasedByEmail: data.email,
        purchasedById: user.id,
        issuedById: userId,
        templateId: data.templateId,
        templateReleaseId: template.marketplaceReleaseId,
        templateListingId: template.marketplaceListingId,
        purchasedAt: new Date(),
        purchaseStatus: PurchaseStatus.Settled,
        workspaceLicense: TemplatePurchaseWorkspaceLicense.Multi,
    })
    const tP: MyTemplatePurchase = {
        purchase,
        listing
    }
    if (data.sendNotification) {
        const buyer = user
        const buyerName = `${buyer.firstName || ''} ${buyer.lastName || ''}`.trim()
        const buyerUser: EmailUser = {
            email: buyer.email,
            name: buyerName
        }

        const members = await GetCreatorMembersNoAuth(template.creatorId)
        const owner = members.find(m => m.creatorMember.role === CreatorMemberRole.Owner)
        const ownerUser: EmailUser = {
            email: owner.user.email,
            name: `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim()
        }
        const membersCC = members
            .filter(m => m.creatorMember.role !== CreatorMemberRole.Owner)
            .map(m => m.user.email)
        const bcc = ['<EMAIL>']
        await sendPurchaseNotifications(myCreator.creator, template, listing, purchase, ownerUser, buyerUser, membersCC, bcc)
    }
    return tP
}

export const GetTemplateDashboardAnalytics = async (userId: string, creatorId: string, templateId: string): Promise<FullTemplateReview[]> => {
    await getMyCreatorTemplate(userId, creatorId, templateId)

    throw new ServerProcessingError(ErrorMessage.UnableToProcessRequest)
}

export interface SubmitTemplateForReviewData {
    releaseId: number
    listingId: number
}

export const SubmitTemplateForReview = async (userId: string, creatorId: string, templateId: string, data: SubmitTemplateForReviewData) => {
    if (!data.releaseId) throw new RequiredParameterError('releaseId')
    if (!data.listingId) throw new RequiredParameterError('listingId')

    await getMyCreatorTemplate(userId, creatorId, templateId)

    const listing = await new TemplateListingService().findOne({templateId, id: data.listingId})
    if (!listing) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const release = await new TemplateReleaseService().findOne({templateId, id: data.releaseId, isReady: true})
    if (!release) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const tSS = new TemplateSubmissionService()
    const submissions = await tSS.find({templateId, reviewResult: IsNull()})
    if (submissions.length > 0) {
        await tSS.update({templateId, reviewResult: IsNull()}, {reviewInternalNote: "Overriden by a new submission", reviewResult: TemplateSubmissionResult.Rejected})
    }
    const submission = await tSS.insert({
        templateId, releaseId: release.id, listingId: listing.id, createdById: userId, creatorId: creatorId
    })
    if (!isProduction()) {
        AutoApproveNonProdTemplateSubmission(submission.id).then()
    }
    return {
        submission
    }
}

export const CancelTemplateSubmission = async (userId: string, creatorId: string, templateId: string) => {
    await getMyCreatorTemplate(userId, creatorId, templateId)

    const tSS = new TemplateSubmissionService()
    const submissions = await tSS.find({templateId, reviewResult: IsNull()})
    if (submissions.length > 0) {

    }
    await tSS.update({templateId, reviewResult: IsNull()}, {reviewNote: "Cancelled by creator", reviewResult: TemplateSubmissionResult.Rejected, reviewedAt: new Date()})
}

const opendashboardCreatorIds = isProduction() ? [] : isStage() ? [] : ['a188516f-e80f-4a5c-a5dd-13278a5f7aa4']

export interface GetMarketplaceTemplatesParams {
    isTopPick?: boolean
    category?: string
    tag?: string
    madeBy?: 'opendashboard' | 'community'
    creatorId?: string
    pricing?: 'free' | 'paid'
    sortBy?: 'popular' | 'installed' | 'added'
    query?: string
    searchTs?: number
    perPage?: number
    page?: number
    templateId?: string
}

export interface MarketplaceTemplate {
    template: Template
    templateListing: TemplateListing
    creator: Creator
    stats: {
        installs: number
        rating: number
        reviewsCount: number
        currentUserPurchases: number
    }
    userIsCreator?: boolean
}

export const GetMarketplaceTemplates = async (params: GetMarketplaceTemplatesParams, userId?: string): Promise<MarketplaceTemplate[]> => {
    let searchTs = new Date().getTime()
    if (params.searchTs && isDateObjValid(new Date(params.searchTs))) {
        searchTs = params.searchTs
    }
    let offset = 0
    let page = 1
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 12
    if (params.page && !Number.isNaN(params.page)) page = Number(params.page)
    if (page > 1) {
        offset = (page - 1) * perPage
    }

    const s = new TemplateService()
    const qB = s.getRepository()
        .createQueryBuilder("t")
        .select()
        .addSelect("c")
        .addSelect("tL")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplateInstall, "tI")
                .where("tI.templateId=t.id")
        }, "s_installs")
        .addSelect(qb => {
            return qb.select("AVG(rating)")
                .from(TemplateReview, "tR")
                .where("tR.templateId=t.id AND tR.rating IS NOT NULL")
        }, "s_rating")
        .addSelect(qb => {
            return qb.select("COUNT(1)")
                .from(TemplateReview, "tR")
                .where("tR.templateId = t.id AND tR.parentId is NULL")
        }, "s_reviewsCount")
        .leftJoin(Creator, 'c', 'c.id=t.creatorId')
        .leftJoin(TemplateListing, 'tL', 'tL.id=t.marketplaceListingId')
        .where({isListedInMarketplace: true})
        .limit(perPage)

    if (userId) {
        qB.addSelect(qb => {
            return qb.select("COUNT(id)")
                .from(TemplatePurchase, "tP")
                .where("tP.templateId = t.id AND tP.purchasedById = :userId AND tP.purchaseStatus=:settled", {userId, settled: PurchaseStatus.Settled})
        }, "s_currentUserPurchases")
        qB.addSelect(qb => {
            return qb.select("1")
                .from(CreatorMember, "cM")
                .where("t.creatorId=cM.creatorId AND cM.userId=:userId", {})
        }, "userIsCreator")
    }
    if (params.creatorId) {
        qB.andWhere("t.creatorId=:creatorId", {creatorId: params.creatorId})
    }

    let rawResults;
    if (params.templateId) {
        rawResults = await qB
            .andWhere({id: params.templateId})
            .getRawMany()
    } else if (params.isTopPick) {
        const topPicks = await new TemplateTopPickService().find({}, {createdAt: 'DESC'}, params.perPage, offset)
        const templateIds = arrayDeDuplicate(topPicks.map(p => p.templateId))

        rawResults = await qB
            .andWhere({id: In(templateIds)})
            .getRawMany()
    } else {
        if (params.category) {
            const categories = await new CategoryService().find({})
            const category = categories.find(c => c.slug == params.category)

            if (category) qB.andWhere('tL.categoryId=:categoryId', {categoryId: category.id})
        }
        if (params.tag) {
            const tags = await new TagService().tagTextToTags(params.tag)
            const tagIds = tags.map(t => t.id)
            qB.andWhere("JSON_CONTAINS(tL.tagIds, :tagId)", {tagId: JSON.stringify(tagIds)});
        }
        if (params.madeBy === 'opendashboard') {
            qB.andWhere("t.creatorId IN(:...creatorIds)", {creatorIds: opendashboardCreatorIds})
        } else if (params.madeBy === 'community') {
            qB.andWhere("t.creatorId NOT IN(:...creatorIds)", {creatorIds: opendashboardCreatorIds})
        }
        if (params.pricing === 'free') {
            qB.andWhere('tL.isPaid=0')
        } else if (params.pricing === 'paid') {
            qB.andWhere('tL.isPaid=1')
        }
        if (params.query && params.query.trim()) {
            qB.andWhere("tL.name LIKE :query", {query: `%%${params.query.trim()}%`})
        }
        if (params.sortBy === 'installed') {
            qB.orderBy('s_installs', "DESC")
        } else if (params.sortBy === 'added') {
            qB.orderBy('t.listedAt', "ASC")
        } else {
            qB.orderBy('tL.name', "ASC")
        }
        if (offset > 0) qB.offset(offset)

        rawResults = await qB
            .getRawMany()
    }
    if (rawResults && Array.isArray(rawResults) && rawResults.length > 0) {
        return rawResults.map(r => {
            const row: MarketplaceTemplate = {
                creator: {} as Creator,
                stats: {installs: 0, rating: 0, reviewsCount: 0, currentUserPurchases: 0},
                template: {} as Template,
                templateListing: {} as TemplateListing,
                userIsCreator: !!r["userIsCreator"]
            }
            for (let key of Object.keys(r)) {
                const [pre, field] = key.split("_")
                if (pre === "c") {
                    row.creator[field] = r[key]
                } else if (pre === "s") {
                    row.stats[field] = Number(r[key])
                } else if (pre === "tL") {
                    row.templateListing[field] = r[key]
                } else if (pre === "t") {
                    row.template[field] = r[key]
                }
            }
            return row
        })
    }


    return []
}

export const GetMarketplaceTemplate = async (id: string, userId?: string): Promise<{ template: MarketplaceTemplate, tags: Tag[], purchases: TemplatePurchase[] }> => {
    const templates = await GetMarketplaceTemplates({templateId: id}, userId)
    if (templates.length == 0) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const template = templates[0]
    const tags: Tag[] = []

    if (template.templateListing.tagIds.length > 0) {
        tags.push(...await new TagService().find({id: In(template.templateListing.tagIds)}))
    }
    let purchases: TemplatePurchase[] = []
    if (userId) {
        purchases = await new TemplatePurchaseService().find({purchasedById: userId, templateId: template.template.id, purchaseStatus: PurchaseStatus.Settled})
    }
    return {
        template, tags, purchases
    }
}

export interface FullTemplateReview {
    review: TemplateReview,
    user: User
}

export interface GetTemplateReviewsParams {
    page?: number
    perPage?: number
    isFromCreator?: boolean
    isRating?: boolean
}

export const GetTemplateReviews = async (userId: string, templateId: string, params: GetTemplateReviewsParams): Promise<FullTemplateReview[]> => {
    const page = params.page && !Number.isNaN(Number(params.page)) ? Number(params.page) : 1
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 50
    const offset = page > 1 ? (page - 1) * perPage : 0

    const qB1 = new TemplateReviewService()
        .getRepository()
        .createQueryBuilder("tR")
        .addSelect("u")
        .leftJoin(User, 'u', 'u.id=tR.userId')
        .where({templateId})
        .limit(perPage)
        .offset(offset)

    if (params.isFromCreator) {
        qB1.andWhere("tR.isPublishedAsCreator=:isPublishedAsCreator", {isPublishedAsCreator: true})
    }
    if (params.isRating) {
        qB1.andWhere("tR.rating IS NOT NULL")
    }

    const r1 = await qB1.getRawMany()

    return r1.map(r => {
        const result: FullTemplateReview = {
            review: {} as TemplateReview,
            user: {} as User,
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "u") {
                result.user[field] = r[key]
            } else if (pre === "tR") {
                result.review[field] = r[key]
            }
        }
        return result
    })
}

export interface PostTemplateReviewCreatorDiscussionData {
    parentId?: number
    reviewText: string
    rating?: number
}

export const PostTemplateReview = async (userId: string, templateId: string, data: PostTemplateReviewCreatorDiscussionData): Promise<{
    review: FullTemplateReview,
    replaces?: FullTemplateReview
}> => {
    if (!data.reviewText && !data.rating) {
        throw new RequiredParameterError("reviewText or rating")
    }
    let rating: number = undefined
    if (data.rating) {
        if (Number.isNaN(Number(data.rating)) || Number(data.rating) < 1 || Number(data.rating) > 5) {
            throw new InvalidParameterError("Rating should be between 1 and 5")
        }
        if (data.parentId) {
            throw new InvalidParameterError("Review with rating cannot have a parent")
        }
        rating = Number(data.rating)
    }
    const template = await new TemplateService().findOne({id: templateId})
    if (!template) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const creatorId = template.creatorId

    const templateListing = await new TemplateListingService().findOne({id: template.marketplaceListingId})
    const creator = await new CreatorService().findOne({id: creatorId})

    const members = await GetCreatorMembersNoAuth(creatorId)

    let prevReview: TemplateReview = undefined

    if (rating) {
        prevReview = await new TemplateReviewService().findOne({templateId, userId, rating: Not(IsNull())})
        if (prevReview) {
            await new TemplateReviewService().remove({id: prevReview.id})
        }
    }
    const user = await new UserService().findOne({id: userId})

    let isAsCreator = false
    for (let member of members) {
        if (member.user.id === userId && [CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member].includes(member.creatorMember.role)) {
            isAsCreator = true
            break
        }
    }

    const s = new TemplateReviewService()
    const review = await s.insert({
        reviewText: data.reviewText,
        parentId: data.parentId,
        templateId,
        isPublishedAsCreator: isAsCreator,
        rating,
        templateReleaseId: template.marketplaceReleaseId,
        templateListingId: template.marketplaceListingId,
        userId
    })

    if (!isAsCreator) {
        const owner = members.find(m => m.creatorMember.role === CreatorMemberRole.Owner)
        const to: EmailUser = {
            email: owner.user.email,
            name: `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim()
        }
        const cc = members
            .filter(m => m.creatorMember.role !== CreatorMemberRole.Owner)
            .map(m => m.user.email)

        const templateLink = appUrl(`/creators/${creator.domain}/templates/${templateListing.templateId}`)

        const commenterName = `${user.firstName || ''} ${user.lastName || ''}`.trim()
        const subject = `New Review Posted on Your Template ${templateListing.name}`
        const message = `
        Hello there, <br/> A new comment was posted on your template ${templateListing.name} from ${commenterName}<br/>
        <p style="padding: 25px; background-color: #f5f5f5">
        ${data.reviewText}
        </p>
        Please review it and provide necessary response if needed.
        `
        const button = {
            label: 'View Template →',
            url: templateLink
        }
        const messageId = await SendEmailWithContent(to, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, cc)
    }

    const replaces: FullTemplateReview = prevReview ? {
        review: prevReview,
        user
    } : undefined

    return {
        review: {
            review,
            user
        },
        replaces
    }
}

export interface MarketplaceCreator {
    creator: Creator
    topCategories: { categoryId: number, count: number }[]
}

export const GetMarketplaceCreator = async (domain: string): Promise<MarketplaceCreator> => {
    const creator = await new CreatorService().findOne({domain})
    if (!creator) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const qB = new TemplateService()
        .getRepository()
        .createQueryBuilder("t")
        .select("tL.categoryId", 'categoryId')
        .addSelect("count(tL.id)", 'count')
        .leftJoin(TemplateListing, 'tL', 'tL.templateId=t.id AND tL.id=t.marketplaceListingId')
        .where("t.creatorId=:creatorId AND t.isListedInMarketplace = true", {creatorId: creator.id})
        .groupBy("tL.categoryId")
        .orderBy("count", "DESC")
        .limit(5)

    const topCategories = await qB.getRawMany()

    return {
        creator, topCategories
    }
}

export interface PurchaseTemplateData {
    workspaceId: string
    discountCode?: string
    workspaceLicense: TemplatePurchaseWorkspaceLicense
    currency: string
}

export interface PurchaseTemplateResponse {
    purchase?: TemplatePurchase
    checkoutUrl?: string
}

export const PurchaseTemplate = async (userId: string, templateId: string, data: PurchaseTemplateData): Promise<PurchaseTemplateResponse> => {
    if (!data.workspaceLicense) throw new RequiredParameterError("workspaceLicense")
    if (!data.workspaceId) throw new RequiredParameterError("workspaceId")
    if (data.currency !== PaymentCurrency.USD && data.currency !== PaymentCurrency.NGN) throw new InvalidParameterError("Invalid currency")

    const template = await new TemplateService().findOne({id: templateId, isListedInMarketplace: true})
    if (!template) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const workspace = await new WorkspaceService().findOne({id: data.workspaceId})
    const templateRelease = await new TemplateReleaseService().findOne({id: template.marketplaceReleaseId})
    const templateListing = await new TemplateListingService().findOne({id: template.marketplaceListingId})
    const creator = await new CreatorService().findOne({id: template.creatorId})
    const members = await GetCreatorMembersNoAuth(template.creatorId)

    let savedDiscount: DiscountToApply = undefined
    if (data.discountCode) {
        savedDiscount = await getSavedDiscount(templateId, template.creatorId, data.discountCode)
    }

    const owner = members.find(m => m.creatorMember.role === CreatorMemberRole.Owner)
    const ownerUser: EmailUser = {
        email: owner.user.email,
        name: `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim()
    }
    const membersCC = members
        .filter(m => m.creatorMember.role !== CreatorMemberRole.Owner)
        .map(m => m.user.email)
    const bcc = ['<EMAIL>']

    let previewPurchase: TemplatePurchase = undefined

    const purchases = await new TemplatePurchaseService().find({templateId, purchasedById: userId, purchaseStatus: PurchaseStatus.Settled})

    for (const purchase of purchases) {
        if (purchase.workspaceLicense === TemplatePurchaseWorkspaceLicense.Multi) {
            previewPurchase = purchase
            break
        }
        if (data.workspaceId === purchase.workspaceId) {
            previewPurchase = purchase
            break
        }
    }
    if (previewPurchase) {
        return {
            purchase: previewPurchase
        }
    }
    let license: TemplatePurchaseWorkspaceLicense = data.workspaceLicense
    // let purchaseWorkspaceId = data.workspaceId
    let purchaseAmountUSD = 0
    if (templateListing.isPaid && !templateListing.isMultiLicensePricing) {
        purchaseAmountUSD = templateListing.singleLicensePrice
    }
    if (templateListing.isPaid && templateListing.isMultiLicensePricing) {
        if (license === TemplatePurchaseWorkspaceLicense.Single) {
            purchaseAmountUSD = templateListing.singleLicensePrice
        } else {
            purchaseAmountUSD = templateListing.multiLicensePrice
        }
    }

    const buyer = await new UserService().findOne({id: userId})
    const buyerName = `${buyer.firstName || ''} ${buyer.lastName || ''}`.trim()
    const buyerUser: EmailUser = {
        email: buyer.email,
        name: buyerName
    }
    const purchasePriceUSD = purchaseAmountUSD
    let discountApplied = false

    const purchasePriceInCents = purchasePriceUSD * 100

    const dS = new DiscountService()

    // apply discount code
    if (purchaseAmountUSD > 0 && savedDiscount) {
        if (savedDiscount.isPercent) {
            purchaseAmountUSD = purchaseAmountUSD - (savedDiscount.amount / 100 * purchaseAmountUSD)
        } else {
            purchaseAmountUSD = purchaseAmountUSD - savedDiscount.amount
        }
        purchaseAmountUSD = Math.max(purchaseAmountUSD, 0)
        discountApplied = true
    }

    if (purchaseAmountUSD === 0) {
        const purchase = await new TemplatePurchaseService().insert({
            templateId: template.id,
            templateListingId: templateListing.id,
            templateReleaseId: templateRelease.id,
            purchasedById: userId,
            purchasedAt: new Date(),
            workspaceLicense: license,
            purchaseStatus: PurchaseStatus.Settled,
            workspaceId: data.workspaceId,
            purchasePriceInCents,
            discountCode: discountApplied ? savedDiscount?.code : undefined,
            discountId: discountApplied ? savedDiscount?.id : undefined,
        })
        await sendPurchaseNotifications(creator, template, templateListing, purchase, ownerUser, buyerUser, membersCC, bcc)
        if (discountApplied && savedDiscount) {
            await dS.incrementUsage(savedDiscount.id, {usage: 1, reservedUsage: 0})
        }
        return {
            purchase
        }
    }
    const amountPaidInCents = purchaseAmountUSD * 100
    const creatorEarningsInCents = (100 - CreatorSettings.CommissionPercent) / 100 * amountPaidInCents
    let amountInLocalCurrency = amountPaidInCents
    let currency: PaymentCurrency = PaymentCurrency.USD
    let paymentProcessor: PaymentProcessor = PaymentProcessor.Stripe
    let meta: KeyValueStore = {}
    if (data.currency === PaymentCurrency.NGN) {
        const rates = await getUSDNGNRate()
        currency = PaymentCurrency.NGN
        paymentProcessor = PaymentProcessor.Paystack
        amountInLocalCurrency = Math.round(rates.rate * amountPaidInCents)
        meta['rate'] = rates
    }

    const purchase = await new TemplatePurchaseService().insert({
        templateId: template.id,
        templateListingId: templateListing.id,
        templateReleaseId: templateRelease.id,
        purchasedById: userId,
        workspaceLicense: license,
        purchaseStatus: PurchaseStatus.Pending,
        workspaceId: data.workspaceId,
        amountPaidInCents,
        amountInLocalCurrency,
        currency,
        creatorEarningsInCents,
        paymentProcessor,
        paymentProcessFeesInCents: 0,
        meta,
        purchasePriceInCents,
        discountCode: discountApplied ? savedDiscount?.code : undefined,
        discountId: discountApplied ? savedDiscount?.id : undefined,
    })
    if (discountApplied && savedDiscount) {
        await dS.incrementUsage(savedDiscount.id, {usage: 0, reservedUsage: 1})
    }
    let checkoutUrl = ''
    if (paymentProcessor === PaymentProcessor.Stripe) {
        const successLink = appUrl(`/${workspace.domain}/templates?install=true&tId=${templateId}&rId=${templateRelease.id}`)
        const templateLink = appUrl(`/templates/${nameToSlug(templateListing.name)}/${template.id}?action=purchase&wId=${data.workspaceId}`)

        const session = await createPaymentCheckoutSession({
            success_url: successLink,
            cancel_url: `${templateLink}&status=cancelled`,
            line_items: [
                {name: templateListing.name, unit_amount_in_cents: purchaseAmountUSD * 100, quantity: 1}
            ],
            client_reference_id: purchase.id.toString(),
            customer_email: ownerUser.email,
            metadata: {
                type: "template_purchase",
                templateId: template.id,
                templateListingId: templateListing.id,
                templateReleaseId: templateRelease.id,
                workspaceId: data.workspaceId,
                templatePurchaseId: purchase.id
            }
        })
        checkoutUrl = session.session.url
    } else if (paymentProcessor === PaymentProcessor.Paystack) {
        const callback = apiUrl(`/api/v1/templates/${templateId}/purchases/${purchase.id}/verify`);
        const transaction = await initializeTransaction(ownerUser.email, amountInLocalCurrency, callback)

        await new TemplatePurchaseService().update({id: purchase.id}, {paymentProcessorReference: transaction.reference})

        checkoutUrl = transaction.authorization_url
    }

    return {
        checkoutUrl
    }
}

export interface ApplyTemplateDiscountData {
    discountCode: string
}

export interface DiscountToApply extends Pick<Discount, 'id' | 'code' | 'amount' | 'isPercent'> {
}

export const applyTemplateDiscount = async (templateId: string, data: ApplyTemplateDiscountData) => {
    if (!data.discountCode) throw new RequiredParameterError("discountCode")
    const template = await new TemplateService().findOne({id: templateId, isListedInMarketplace: true})
    if (!template) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    return await getSavedDiscount(templateId, template.creatorId, data.discountCode)
}

const getSavedDiscount = async (templateId: string, creatorId: string, discountCode: string) => {
    const s = new DiscountService()
    const savedDiscount = await s.findOne({creatorId, code: discountCode})
    if (!savedDiscount) throw new NotfoundError(ErrorMessage.DiscountNotFoundOrExpired)
    if (savedDiscount.isUsageLimited && savedDiscount.usage + savedDiscount.reservedUsage >= savedDiscount.usageLimit) throw new BadRequestError(ErrorMessage.DiscountUsageLimitReached)
    if (!savedDiscount) throw new NotfoundError(ErrorMessage.DiscountNotFoundOrExpired)
    if (!savedDiscount.isAllTemplates && !savedDiscount.templateIds.includes(templateId)) throw new BadRequestError(ErrorMessage.DiscountNotFoundOrExpired)
    if (savedDiscount.isValidForPeriod) {
        if (new Date(savedDiscount.validFrom) > new Date() || new Date(savedDiscount.validTo) < new Date()) {
            throw new BadRequestError(ErrorMessage.DiscountNotFoundOrExpired)
        }
    }
    const discount: DiscountToApply = {
        code: savedDiscount.code,
        isPercent: savedDiscount.isPercent,
        amount: savedDiscount.amount,
        id: savedDiscount.id
    }
    return discount
}

export const HandleTemplatePurchaseCompletedWebhook = async (id: string, paymentProcessor: PaymentProcessor, paymentProcessorReference: string, feeInCents?: number, feesInLocalCurrency?: number) => {
    const s = new TemplatePurchaseService()
    const purchase = await s.findOne({id, purchaseStatus: PurchaseStatus.Pending, paymentProcessor})
    if (!purchase) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const paymentProcessFeesInCents = feeInCents
    const paymentProcessFeesInLocalCurrency = feesInLocalCurrency

    let platformProfitInCents = undefined
    if (paymentProcessFeesInCents) {
        platformProfitInCents = purchase.amountPaidInCents - paymentProcessFeesInCents - purchase.creatorEarningsInCents
    }

    await s.update({id}, {
        purchaseStatus: PurchaseStatus.Settled,
        paymentProcessFeesInCents,
        paymentProcessFeesInLocalCurrency,
        paymentProcessorReference,
        platformProfitInCents,
        purchasedAt: new Date()
    })

    if (purchase.discountCode && purchase.discountId) await new DiscountService().incrementUsage(purchase.discountId, {usage: 1, reservedUsage: -1})
    await initNotification(purchase)
}

export const HandleNGNTemplatePurchaseCompletedWebhook = async (templateId: string, purchaseId: string, transactionRef: string) => {
    const s = new TemplatePurchaseService()
    const purchase = await s.findOne({paymentProcessorReference: transactionRef, templateId, purchaseStatus: PurchaseStatus.Pending, id: purchaseId})
    if (!purchase) throw new NotfoundError(ErrorMessage.EntityNotFound)

    // const template = await new TemplateService().findOne({id: templateId})
    const listing = await new TemplateListingService().findOne({templateId, id: purchase.templateListingId})
    if (!listing) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const transaction = await getVerifiedTransaction(transactionRef)
    if (!transaction) {
        throw new BadRequestError("Payment not confirmed")
    }
    if (transaction.status !== "success") {
        throw new BadRequestError("Transaction was not successful")
    }
    if (Math.round(purchase.amountInLocalCurrency) !== Math.round(transaction.amount)) {
        consoleLog(`Paystack Payment amount mismatch`, {amountInLocalCurrency: purchase.amountInLocalCurrency, transactionAmount: transaction.amount})
        throw new BadRequestError("Payment amount mismatch")
    }
    const rates: { rate: number } = purchase.meta?.['rate'] as { rate: number } || (await getUSDNGNRate())
    const fees = transaction.fees
    const paymentProcessFeesInLocalCurrency = fees
    let paymentProcessFeesInCents = undefined

    if (rates && rates.rate) {
        // the rate is USD to NGN, the fees is in NGN, now we want to get paymentProcessFeesInCents in USD Cents
        paymentProcessFeesInCents = Math.round(fees / rates.rate);
    }
    let platformProfitInCents = undefined
    if (paymentProcessFeesInCents) {
        platformProfitInCents = purchase.amountPaidInCents - paymentProcessFeesInCents - purchase.creatorEarningsInCents
    }

    if (purchase.discountCode && purchase.discountId) await new DiscountService().incrementUsage(purchase.discountId, {usage: 1, reservedUsage: -1})

    await s.update({id: purchase.id}, {
        purchaseStatus: PurchaseStatus.Settled,
        paymentProcessFeesInCents,
        paymentProcessFeesInLocalCurrency,
        platformProfitInCents,
        purchasedAt: new Date()
    })
    await initNotification(purchase)

    const workspace = await new WorkspaceService().findOne({id: purchase.workspaceId})

    const successLink = appUrl(`/${workspace.domain}/templates?install=true&tId=${templateId}&rId=${purchase.templateReleaseId}`)

    return {
        nextUrl: successLink
    }
}

const initNotification = async (purchase: TemplatePurchase) => {
    const templateListing = await new TemplateListingService().findOne({id: purchase.templateListingId})
    const template = await new TemplateService().findOne({id: purchase.templateId})
    const creator = await new CreatorService().findOne({id: template.creatorId})
    const members = await GetCreatorMembersNoAuth(template.creatorId)

    const owner = members.find(m => m.creatorMember.role === CreatorMemberRole.Owner)
    const ownerUser: EmailUser = {
        email: owner.user.email,
        name: `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim()
    }
    const membersCC = members
        .filter(m => m.creatorMember.role !== CreatorMemberRole.Owner)
        .map(m => m.user.email)
    const bcc = ['<EMAIL>']

    const buyer = await new UserService().findOne({id: purchase.purchasedById})
    const buyerName = `${buyer.firstName || ''} ${buyer.lastName || ''}`.trim()
    const buyerUser: EmailUser = {
        email: buyer.email,
        name: buyerName
    }
    await sendPurchaseNotifications(creator, template, templateListing, purchase, ownerUser, buyerUser, membersCC, bcc)
}

export const HandleTemplatePurchaseAbandonedWebhook = async (id: string, paymentProcessor: PaymentProcessor, paymentProcessorReference: string) => {
    const s = new TemplatePurchaseService()
    await s.update({id, paymentProcessor}, {
        purchaseStatus: PurchaseStatus.Abandoned,
        paymentProcessorReference
    })
    const purchase = await s.findOne({id})
    if (purchase.discountCode && purchase.discountId) await new DiscountService().incrementUsage(purchase.discountId, {usage: 0, reservedUsage: -1})
}

const sendPurchaseNotifications = async (creator: Creator, template: Template, listing: TemplateListing, purchase: TemplatePurchase, ownerUser: EmailUser, buyerUser: EmailUser, cc: string[], bcc: string[]) => {

    const buyer: EmailUser = buyerUser

    const orderId = `${purchase.id}`.padStart(12, "0")

    {
        // template purchase notification to creator
        const templateLink = appUrl(`/creators/${creator.domain}/templates/${template.id}/purchases`)
        const subject = `New Purchase Notification for Your Template ${listing.name}`
        const message = `
        Hello ${creator.name}, <br/> You have received a new purchase on your template ${listing.name}! Here are the details:<br/>
        <p style="padding: 25px; background-color: #f5f5f5">
        <strong>Order Id:</strong> ${orderId}<br/>
        <strong>Order Date:</strong> ${dateToMySQL(new Date(purchase.createdAt))}<br/>
        <strong>Template:</strong> ${listing.name}<br/>
        <strong>Total Price:</strong> USD ${purchase.amountPaidInCents / 100}<br/>
        
        </p>
        If you have any questions or need support, feel free to reach out to <NAME_EMAIL>. <br/>
        Thank you for your continued partnership! <br/>
        Best regards, <br/>
        `
        const button = {
            label: 'View Template →',
            url: templateLink
        }
        const messageId = await SendEmailWithContent(ownerUser, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, cc, bcc)
    }
    {
        // template purchase notification to buyer
        const templateLink = appUrl(`/${nameToSlug(listing.name)}/${template.id}`)
        const subject = `Your Purchase Confirmation for ${listing.name}`
        const message = `
        Hello ${buyer.name}, <br/> Thank you for your purchase of ${listing.name}! Here are the details:<br/>
        <p style="padding: 25px; background-color: #f5f5f5">
        <strong>Order Id:</strong> ${orderId}<br/>
        <strong>Order Date:</strong> ${dateToMySQL(new Date(purchase.createdAt))}<br/>
        <strong>Template:</strong> ${listing.name}<br/>
        <strong>Total Price:</strong> USD ${purchase.amountPaidInCents / 100}<br/>
        
        </p>
        If you have any questions or need support, feel free to reach out to <NAME_EMAIL>. <br/>
        Best regards, <br/>
        `

        const button = {
            label: 'View Template →',
            url: templateLink
        }
        const messageId = await SendEmailWithContent(buyer, subject, message, button, null, true, undefined, undefined, undefined, undefined, false, cc, bcc)
    }

}

export const ExpireTemplatePurchasesAfterTimeout = async (abandonTimeoutInMinutes = 20) => {
    const lock = await redisAcquireLock(CRON_LOCK_KEYS.EXPIRE_TEMPLATE_PURCHASE_AFTER_TIMEOUT)
    if (!lock) {
        throw new ServerProcessingError('Failed to acquire lock')
    }
    // get the purchases that are pending and older than the timeout
    // get the ones that have discount code, group them by discountCode
    // mark the purchases as abandoned
    // get the discounts and decrement the reserved usage for each of them
    const s = new TemplatePurchaseService()

    const abandonTimeout = new Date(Date.now() - abandonTimeoutInMinutes * 60000);
    const pendingPurchases = await s.find({purchaseStatus: PurchaseStatus.Pending, createdAt: LessThan(abandonTimeout)});

    const discountUsageUpdates: { [discountId: string]: number } = {};

    for (const purchase of pendingPurchases) {
        if (purchase.discountId) {
            discountUsageUpdates[purchase.discountId] = (discountUsageUpdates[purchase.discountId] || 0) + 1;
        }
    }
    const purchaseIds = pendingPurchases.map(p => p.id)
    await s.update({id: In(purchaseIds)}, {purchaseStatus: PurchaseStatus.Abandoned});

    const dS = new DiscountService();
    for (const [discountId, usage] of Object.entries(discountUsageUpdates)) {
        await dS.incrementUsage(discountId, {usage: 0, reservedUsage: -usage});
    }
    return {
        discountUsageUpdates, purchaseIds
    }
}

export interface BuildTemplateInstallOptionsData {
    releaseId?: number
    listingId?: number
    workspaceId: string
}

export interface BuildTemplateInstallOptionsResponse {
    // databaseUpgrades: {
    //     [databaseId: string]: string[]
    // }
    previousInstall?: TemplateInstall
    template: Template
    listing: TemplateListing
    assets: TemplateReleaseWithAssets
}

export const BuildTemplateInstallOptions = async (userId: string, templateId: string, data: BuildTemplateInstallOptionsData) => {
    if (!data.workspaceId) throw new RequiredParameterError("workspaceId")
    const member = await GetMyWorkspace(userId, data.workspaceId)

    if (!member || ![WorkspaceMemberRole.Owner, WorkspaceMemberRole.Admin, WorkspaceMemberRole.Member, WorkspaceMemberRole.SupportUser].includes(member.workspaceMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const purchases = await new TemplatePurchaseService().find({purchasedById: userId, templateId, purchaseStatus: PurchaseStatus.Settled})
    const template = await new TemplateService().findOne({id: templateId})

    let previewPurchase: TemplatePurchase = undefined

    for (const purchase of purchases) {
        if (purchase.workspaceLicense === TemplatePurchaseWorkspaceLicense.Multi) {
            previewPurchase = purchase
            break
        }
        if (data.workspaceId === purchase.workspaceId) {
            previewPurchase = purchase
            break
        }
    }
    if (!previewPurchase) {
        throw new BadRequestError(ErrorMessage.PurchaseNotFoundForTemplate)
    }
    const releaseId = data.releaseId || template.marketplaceReleaseId
    const listingId = data.listingId || template.marketplaceListingId
    const listing = await new TemplateListingService().findOne({id: listingId})
    if (!listing) {
        throw new NotfoundError(ErrorMessage.TemplateListingNotFound)
    }
    const assets = await GetTemplateReleaseWithAssets(templateId, String(releaseId), true)

    const previousInstall = await new TemplateInstallService().findOne({workspaceId: data.workspaceId, templateId, installStatus: TemplateInstallStatus.Completed}, {createdAt: "DESC"})
    const res: BuildTemplateInstallOptionsResponse = {
        template,
        listing,
        assets,
        previousInstall
    }
    return res
}

export interface InstallTemplateData {
    workspaceId: string,
    prepareData: TemplateInstallPrepareData
    templateId: string
    releaseId?: number
}

export const InstallTemplate = async (userId: string, data: InstallTemplateData, {contactsDbId, companiesDbId}: { contactsDbId?: string, companiesDbId?: string } = {}) => {
    if (!data.workspaceId) throw new RequiredParameterError("workspaceId")
    if (!data.prepareData) throw new RequiredParameterError("prepareData")
    if (!data.templateId) throw new RequiredParameterError("templateId")

    const templateId = data.templateId

    const options = await BuildTemplateInstallOptions(userId, templateId, {workspaceId: data.workspaceId, releaseId: data.releaseId})

    if (data.prepareData.installAll) {
        const companyDef = getCompanyDbDefinition()
        const contactsDef = getCustomerDbDefinition('')

        const contactsPackageName = getDatabasePackageName(contactsDef)
        const companyPackageName = getDatabasePackageName(companyDef)

        for (const database of options.assets.databases) {
            let upgradeId = ''
            const id = database.id
            if (database.srcPackageName === companyPackageName && companiesDbId) {
                upgradeId = companiesDbId
            }
            if (database.srcPackageName === contactsPackageName && contactsDbId) {
                upgradeId = contactsDbId
            }
            data.prepareData.databases[id] = {
                installMethod: upgradeId ? TemplateInstallMethod.Upgrade : TemplateInstallMethod.Install,
                installData: true,
                installViews: true,
                upgradeId
            }
        }
        for (const id of Object.keys(options.assets.release.objectMappings.pages)) {
            data.prepareData.pages[id] = {
                installMethod: TemplateInstallMethod.Install,
                installData: true,
                installViews: true
            }
        }
        for (const id of Object.keys(options.assets.release.objectMappings.workflows)) {
            data.prepareData.workflows[id] = {}
        }
    }

    const upgradeDatabaseIds = Object.values(data.prepareData.databases)
        .filter(db => db.installMethod === TemplateInstallMethod.Upgrade && !!db.upgradeId).map(db => db.upgradeId)

    const databases: Database[] = []

    if (upgradeDatabaseIds.length > 0) {
        databases.push(...await new DatabaseService().find({workspaceId: data.workspaceId, id: In(upgradeDatabaseIds)}))
    }
    const databasesMap: { [id: string]: Database } = {}

    for (let database of databases) {
        databasesMap[database.id] = database
    }
    if (Object.keys(data.prepareData.databases).length > 0) {
        for (const id of Object.keys(data.prepareData.databases)) {
            const templateDb = data.prepareData.databases[id]
            if (templateDb.installMethod === TemplateInstallMethod.Upgrade) {
                const upgradeId = templateDb.upgradeId
                if (!upgradeId) {
                    templateDb.installMethod = TemplateInstallMethod.Install
                } else {
                    if (!databasesMap[upgradeId]) {
                        throw new NotfoundError(ErrorMessage.EntityNotFound + `Database(${upgradeId})`)
                    }
                }
            }
        }
    }

    const template = options.template
    const assets = options.assets

    const prevInstall = await new TemplateInstallService().findOne({workspaceId: data.workspaceId, templateId, installStatus: TemplateInstallStatus.Completed}, {createdAt: "DESC"})
    let install = await new TemplateInstallService().insert({
        workspaceId: data.workspaceId,
        templateId,
        templateReleaseId: options.assets.release.id,
        installedById: userId,
        prepareData: data.prepareData,
        installStatus: TemplateInstallStatus.Pending,
        upgradedFromReleaseId: prevInstall ? prevInstall.templateReleaseId : undefined
    })
    install = await backgroundInstallTemplate(install, assets, databasesMap)

    return {
        install
    }
}

export const backgroundInstallTemplate = async (install: TemplateInstall, assets: TemplateReleaseWithAssets, upgradableDatabaseMap: { [id: string]: Database }) => {
    const objectMappings: ObjectMappings = {
        databases: {},
        records: {},
        pages: {},
        views: {},
        documents: {},
        workflows: {}
    }
    const userId = install.installedById

    const templateDatabaseMap: {
        [id: string]: Database
    } = {}
    for (const database of assets.databases) {
        templateDatabaseMap[database.id] = database
    }

    const s = new DatabaseService()
    const rS = new RecordService()

    // handle databases to be installed/upgraded along with their records, add the object mapping
    // handle pages to be installed(databases pages or regular pages), add the object mapping as well

    const dbsToInsert: { oldId: string, newId: string, data: CreateDatabaseData }[] = []
    const dbsToUpdate: { id: string, data: Database }[] = []

    console.log("Prepare data: ", install.prepareData)
    console.log("Processing order: ", assets.release.dbProcessingOrder)

    // handle databases
    for (let dbToClone of assets.release.dbProcessingOrder) {
        console.log(`Start processing of ${dbToClone.label} with Id: ${dbToClone.value}`)
        const id = assets.release.objectMappings.databases[dbToClone.value]
        const option = install.prepareData.databases[id]
        const templateDb = templateDatabaseMap[id]

        let database: Database = undefined

        // handle database install or upgrade
        if (option.installMethod === TemplateInstallMethod.Upgrade && option.upgradeId && upgradableDatabaseMap[option.upgradeId]) {
            database = upgradableDatabaseMap[option.upgradeId]
            for (const colId of Object.keys(templateDb.definition.columnsMap)) {
                const col = templateDb.definition.columnsMap[colId]
                if (!database.definition.columnsMap[colId]) {
                    database.definition.columnsMap[colId] = col
                    database.definition.columnIds = arrayDeDuplicate([...database.definition.columnIds, colId])
                }
            }
            const oldDefinition = {...database.definition}
            let definitionText = JSON.stringify(oldDefinition)
            for (let oldId of Object.keys(objectMappings.databases)) {
                const newId = objectMappings.databases[oldId]
                definitionText = strReplaceAll(oldId, definitionText, newId)
            }
            database.definition = JSON.parse(definitionText)
            database.isMessagingEnabled = database.isMessagingEnabled || templateDb.isMessagingEnabled

            dbsToUpdate.push({id: database.id, data: database})
            objectMappings.databases[id] = database.id
        } else {
            const oldDefinition = {...templateDb.definition}
            let definitionText = JSON.stringify(oldDefinition)
            for (let oldId of Object.keys(objectMappings.databases)) {
                const newId = objectMappings.databases[oldId]
                definitionText = strReplaceAll(oldId, definitionText, newId)
            }
            const newDefinition = JSON.parse(definitionText)
            const templateDbData: CreateDatabaseData = {
                id: generateUUID(),
                createdById: install.installedById,
                definition: newDefinition,
                name: templateDb.name,
                srcPackageName: templateDb.packageName,
                srcVersionName: templateDb.versionName,
                srcVersionNumber: templateDb.versionNumber,
                workspaceId: install.workspaceId,
                isMessagingEnabled: templateDb.isMessagingEnabled,
            }
            dbsToInsert.push({oldId: id, newId: templateDbData.id, data: templateDbData})
            objectMappings.databases[id] = templateDbData.id
        }

        console.log("Object mapping:", objectMappings.databases)
    }
    for (const db of [...dbsToInsert, ...dbsToUpdate]) {
        const oldDefinition = {...db.data.definition}
        let definitionText = JSON.stringify(oldDefinition)
        for (let oldId of Object.keys(objectMappings.databases)) {
            const newId = objectMappings.databases[oldId]
            definitionText = strReplaceAll(oldId, definitionText, newId)
        }
        db.data.definition = JSON.parse(definitionText)
    }

    const finalDatabases = await s.getRepository().save(dbsToInsert.map(db => db.data))
    for (let database of dbsToUpdate) {
        await s.update({id: database.id}, {definition: database.data.definition})
        finalDatabases.push(database.data)
    }
    const finalDbMap: { [id: string]: Database } = {}

    for (let finalDatabase of finalDatabases) {
        finalDbMap[finalDatabase.id] = finalDatabase
    }

    console.log(finalDbMap)
    const recordsData: CreateRecordData[] = []
    for (let dbToClone of assets.release.dbProcessingOrder) {
        const id = assets.release.objectMappings.databases[dbToClone.value]
        const option = install.prepareData.databases[id]

        const newDbId = objectMappings.databases[id]
        if (option.installData) {
            const records = assets.records.filter(r => r.databaseId === id)

            for (const record of records) {
                let valuesText = JSON.stringify(record.recordValues)
                for (let oldId of Object.keys(objectMappings.records)) {
                    const newId = objectMappings.records[oldId]
                    valuesText = strReplaceAll(oldId, valuesText, newId)
                }
                const recordValues: RecordValues = JSON.parse(valuesText)
                const d: CreateRecordData = {
                    id: generateUUID(),
                    createdById: userId,
                    databaseId: newDbId,
                    recordValues: recordValues,
                    uniqueValue: record.uniqueValue,
                    updatedById: userId,
                    meta: {
                        template_source: {
                            oldId: record.id
                        }
                    }
                }
                recordsData.push(d)
                objectMappings.records[record.id] = d.id
            }
        }
    }
    for (let record of recordsData) {
        let valuesText = JSON.stringify(record.recordValues)
        for (let oldId of Object.keys(objectMappings.records)) {
            const newId = objectMappings.records[oldId]
            valuesText = strReplaceAll(oldId, valuesText, newId)
        }
        record.recordValues = JSON.parse(valuesText)
    }

    for (let dbToClone of assets.release.dbProcessingOrder) {
        const id = assets.release.objectMappings.databases[dbToClone.value]
        const newId = objectMappings.databases[id]
        const database = finalDbMap[newId]

        console.log(`About to start cloning records for ${dbToClone.label}`, {oldId: dbToClone.value, newId})

        const records = recordsData.filter(r => r.databaseId === newId)

        const createdRecords = await rS.addRecords(records, OnDuplicateAction.Ignore)

        await saveRecordsCreatedActivities(userId, database, createdRecords)
    }

    // handle pages
    const dS = new DocumentService()
    const vS = new ViewService()
    const pS = new PageService()

    const pageData: Partial<Page>[] = []
    const viewData: Partial<View>[] = []
    const docsData: Partial<Document>[] = []

    const templatePageMap: { [id: string]: Page } = {}
    for (const page of assets.pages) {
        templatePageMap[page.id] = page
    }

    const skippedPageIds: string[] = []

    for (const oldPage of assets.pages) {
        let canInstall = true
        if (!oldPage.databaseId) {
            // const option = install.prepareData.databases[oldPage.databaseId]
            const option = install.prepareData.pages[oldPage.id]
            canInstall = !!option
        }
        if (!canInstall) {
            skippedPageIds.push(oldPage.id)
            continue
        }
        const pgData: Partial<Page> = {
            createdById: userId,
            icon: oldPage.icon,
            name: oldPage.name,
            ownerId: userId,
            workspaceId: install.workspaceId,
            id: generateUUID(),
            visibility: oldPage.visibility,
            accessLevel: oldPage.accessLevel,
            viewsOrder: oldPage.viewsOrder,
            databaseId: oldPage.databaseId ? objectMappings.databases[oldPage.databaseId] : null
        }
        pageData.push(pgData)
        objectMappings.pages[oldPage.id] = pgData.id
    }

    const skippedViewIds: string[] = []

    for (const view of assets.views) {
        if (skippedPageIds.includes(view.pageId)) {
            skippedViewIds.push(view.id)
            continue
        }
        let defText = JSON.stringify(view.definition)
        for (let oldId of Object.keys(objectMappings.databases)) {
            const newId = objectMappings.databases[oldId]
            defText = strReplaceAll(oldId, defText, newId)
        }
        for (let oldId of Object.keys(objectMappings.records)) {
            const newId = objectMappings.records[oldId]
            defText = strReplaceAll(oldId, defText, newId)
        }
        const vData: Partial<View> = {
            createdById: userId,
            definition: JSON.parse(defText),
            name: view.name,
            pageId: objectMappings.pages[view.pageId],
            type: view.type,
            id: generateUUID()
        }
        viewData.push(vData)
        objectMappings.views[view.id] = vData.id
    }
    for (let datum of pageData) {
        datum.viewsOrder = (datum.viewsOrder || []).map(v => objectMappings.views[v])
    }

    // const skippedDocIds: string[] = []
    for (let oldDoc of assets.docs) {
        if (skippedViewIds.includes(oldDoc.viewId)) {
            // skippedDocIds.includes(oldDoc.id)
            continue
        }
        const docData: Partial<Document> = {
            id: generateUUID(),
            workspaceId: install.workspaceId,
            name: oldDoc.name,
            contentJSON: oldDoc.contentJSON,
            contentText: oldDoc.contentText,
            viewId: objectMappings.views[oldDoc.viewId],
            createdById: userId,
            updatedById: userId
        }
        docsData.push(docData)
        objectMappings.documents[oldDoc.id] = docData.id
    }
    for (let viewDatum of viewData) {
        if (viewDatum.type !== ViewType.Document) continue
        const def = viewDatum.definition as DocumentViewDefinition
        def.itemsOrder = (def.itemsOrder || []).map(d => objectMappings.documents[d])
    }

    await pS.batchAdd(pageData)
    await vS.batchAdd(viewData)
    if (docsData.length > 0) {
        await dS.batchAdd(docsData)
    }

    // handle workflows
    const wS = new WorkflowService()
    const workflowData: Partial<Workflow>[] = []
    for (const oldWorkflow of assets.workflows) {
        let canInstall = !!install.prepareData.workflows[oldWorkflow.id]
        if (!canInstall) {
            continue
        }
        let defText = JSON.stringify(oldWorkflow.definition)
        for (let [id, newId] of Object.entries(objectMappings.databases)) {
            defText = strReplaceAll(id, defText, newId)
        }
        const wData: Partial<Workflow> = {
            id: generateUUID(),
            workspaceId: install.workspaceId,
            name: oldWorkflow.name,
            definition: JSON.parse(defText),
            createdById: userId,
            updatedById: userId
        }
        workflowData.push(wData)
        objectMappings.workflows[oldWorkflow.id] = wData.id
    }
    // in case there are additional references in the workflow definition
    for (let workflow of workflowData) {
        let defText = JSON.stringify(workflow.definition)
        for (let [id, newId] of Object.entries(objectMappings.databases)) {
            defText = strReplaceAll(id, defText, newId)
            if (workflow.triggerObjectId && workflow.triggerObjectId === id) {
                workflow.triggerObjectId = newId
            }
        }
        for (let [id, newId] of Object.entries(objectMappings.workflows)) {
            defText = strReplaceAll(id, defText, newId)
            if (workflow.triggerObjectId && workflow.triggerObjectId === id) {
                workflow.triggerObjectId = newId
            }
        }
        for (let [id, newId] of Object.entries(objectMappings.records)) {
            defText = strReplaceAll(id, defText, newId)
            if (workflow.triggerObjectId && workflow.triggerObjectId === id) {
                workflow.triggerObjectId = newId
            }
        }
        workflow.definition = JSON.parse(defText)
    }
    if (workflowData.length > 0) {
        await wS.batchAdd(workflowData)
    }

    const homePage: TemplateHomePage = assets.release.meta?.homePage || {}
    homePage.databaseId = homePage.databaseId ? objectMappings.databases[homePage.databaseId] || '' : ''
    homePage.pageId = homePage.pageId ? objectMappings.pages[homePage.pageId] || '' : ''
    homePage.viewId = homePage.viewId ? objectMappings.views[homePage.viewId] || '' : ''

    await new TemplateInstallService().update({id: install.id}, {
        installStatus: TemplateInstallStatus.Completed,
        completedAt: new Date(),
        objectMappings,
        meta: {homePage}
    })

    install = {
        ...install,
        installStatus: TemplateInstallStatus.Completed,
        completedAt: new Date(),
        objectMappings,
        meta: {homePage}
    }

    // broadcast template installed
    broadcastTemplateInstalled(install.workspaceId, install)

    return install
}

export interface InstallTemplateViaCodeData {
    workspaceId: string
    code: string
}

export const InstallTemplateViaCode = async (userId: string, data: InstallTemplateViaCodeData) => {
    if (!data.code) throw new RequiredParameterError("code")
    if (!data.workspaceId) throw new RequiredParameterError("workspaceId")
    const purchase = await new TemplatePurchaseService().findOne({purchasedById: userId, id: data.code})
    if (!purchase) {
        throw new NotfoundError(ErrorMessage.PurchaseNotFoundForTemplate)
    }
    if (purchase.workspaceLicense !== TemplatePurchaseWorkspaceLicense.Multi && purchase.workspaceId !== data.workspaceId) {
        throw new NotfoundError(ErrorMessage.PurchaseNotFoundForTemplate)
    }
    const databases = await new DatabaseService().find({workspaceId: data.workspaceId, srcPackageName: In([getDatabasePackageName(getCustomerDbDefinition('')), getDatabasePackageName(getCompanyDbDefinition())])})
    const companiesDb = databases.find(db => db.srcPackageName === getDatabasePackageName(getCompanyDbDefinition()))
    const contactsDb = databases.find(db => db.srcPackageName === getDatabasePackageName(getCustomerDbDefinition('')))

    const installData: TemplateInstallPrepareData = {
        databases: {},
        pages: {},
        workflows: {},
        installAll: true
    }
    return await InstallTemplate(userId, {workspaceId: data.workspaceId, prepareData: installData, templateId: purchase.templateId}, {contactsDbId: contactsDb?.id, companiesDbId: companiesDb?.id})
}

export interface InstallStats {
    allTime: number
    last7Days: number
    thisMonth: number
    templateId: string
}

export interface ReviewStats {
    templateId: string
    averageRatings: number
    all: number
    withRatings: number
}

export interface EarningStats {
    templateId: string
    balance: number,
    last7Days: number
    thisMonth: number
    allTime: number
}

export interface CreatorTemplateStats extends MyCreatorStats {
    submission?: TemplateSubmission
    lastRelease?: TemplateRelease
    lastListing: TemplateListing
    canSubmitToMarketplace?: boolean
}

export const GetCreatorTemplateStats = async (userId: string, creatorId: string, templateId: string) => {
    const creator = await GetMyCreator(userId, creatorId)
    if (![CreatorMemberRole.Owner, CreatorMemberRole.Admin, CreatorMemberRole.Member, CreatorMemberRole.SupportUser].includes(creator.creatorMember.role)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const stats: CreatorTemplateStats = {
        earnings: await GetTemplateEarningStats(creatorId, templateId),
        installed: await GetTemplateInstallStats(creatorId, templateId),
        reviews: await GetTemplateReviewStats(creatorId, templateId),
        submission: await new TemplateSubmissionService().findOne({templateId}, {createdAt: "DESC"}),
        lastRelease: await new TemplateReleaseService().findOne({templateId, isReady: true}, {createdAt: "DESC"}),
        lastListing: await new TemplateListingService().findOne({templateId}, {createdAt: "DESC"}),
    }
    if (!stats.submission || stats.submission.reviewResult === TemplateSubmissionResult.Rejected || stats.submission.listingId !== stats.lastListing.id || stats.submission.releaseId !== stats.lastRelease.id) {
        stats.canSubmitToMarketplace = true
    }
    return {stats}
}

export const GetTemplateInstallStats = async (creatorId: string, templateId?: string) => {
    const s = new TemplateInstallService()

    const now = new Date()

    const thisMonthStarts = new Date(now.setDate(1))
    const _7_days_ago = datePlusDays(now, -7)

    const qB = s.getRepository().createQueryBuilder('tI')
        .select("t.creatorId", 'creatorId')
        .select("tI.templateId", 'templateId')
        .addSelect("COUNT(1)", "allTime")
        .addSelect("COUNT(CASE WHEN completedAt>=:_7_days_ago THEN 1 END)", "last7Days")
        .addSelect("COUNT(CASE WHEN completedAt>=:thisMonthStarts THEN 1 END)", "thisMonth")
        .innerJoin(Template, "t", "t.Id=tI.templateId")
        .setParameters({
            thisMonthStarts,
            _7_days_ago
        })
        .where({
            installStatus: TemplateInstallStatus.Completed
        })
        .andWhere('t.creatorId=:creatorId', {creatorId})
        .groupBy("tI.templateId, t.creatorId")
    if (templateId) {
        qB.andWhere('t.id=:templateId', {templateId})
    }

    const rawResult = await qB.getRawMany()
    const allStats: InstallStats[] = []

    for (let r of rawResult) {
        allStats.push({
            templateId: r['templateId'],
            last7Days: Number(r['last7Days']),
            allTime: Number(r['allTime']),
            thisMonth: Number(r['thisMonth']),
        })
    }
    return allStats
}

export const GetTemplateReviewStats = async (creatorId: string, templateId?: string) => {
    const s = new TemplateReviewService()
    const now = new Date()

    const qB = s.getRepository().createQueryBuilder('tR')
        .select("t.creatorId", 'creatorId')
        .addSelect("tR.templateId", 'templateId')
        .addSelect("COUNT(1)", "all")
        .addSelect("AVG(CASE WHEN rating IS NOT NULL THEN rating END)", "averageRatings")
        .addSelect("COUNT(CASE WHEN rating IS NOT NULL THEN 1 END)", "withRatings")
        .innerJoin(Template, "t", "t.Id=tR.templateId")
        .where('t.creatorId=:creatorId', {creatorId})
        .groupBy("tR.templateId, t.creatorId")
    if (templateId) {
        qB.andWhere('t.id=:templateId', {templateId})
    }

    const rawResult = await qB.getRawMany()
    const allStats: ReviewStats[] = []

    for (let r of rawResult) {
        allStats.push({
            all: Number(r['all']),
            averageRatings: Number(r['averageRatings']),
            withRatings: Number(r['withRatings']),
            templateId: r['templateId'],
        })
    }
    return allStats
}

export const GetTemplateEarningStats = async (creatorId: string, templateId?: string) => {
    const stats: EarningStats[] = []
    const s = new TemplatePurchaseService()

    const now = new Date()

    const thisMonthStarts = new Date(now.setDate(1))
    const _7_days_ago = datePlusDays(now, -7)

    const qB = s.getRepository().createQueryBuilder('tP')
        .select("t.creatorId", 'creatorId')
        .addSelect("tP.templateId", 'templateId')
        .addSelect("SUM(creatorEarningsInCents)", "allTime")
        .addSelect("SUM(CASE WHEN purchasedAt>=:_7_days_ago THEN creatorEarningsInCents END)", "last7Days")
        .addSelect("SUM(CASE WHEN purchasedAt>=:thisMonthStarts THEN creatorEarningsInCents END)", "thisMonth")
        .addSelect("SUM(CASE WHEN payoutId IS NULL THEN creatorEarningsInCents END)", "balance")
        .innerJoin(Template, "t", "t.Id=tP.templateId")
        // .innerJoin(Creator, "c", "c.Id=t.creatorId")
        .setParameters({
            thisMonthStarts,
            _7_days_ago
        })
        .where({
            purchaseStatus: PurchaseStatus.Settled,
            creatorEarningsInCents: MoreThan(0)
        })
        .andWhere('t.creatorId=:creatorId', {creatorId})
        .groupBy("tP.templateId, t.creatorId")
    if (templateId) {
        qB.andWhere('t.id=:templateId', {templateId})
    }

    const rawResult = await qB.getRawMany()

    for (let rawResultElement of rawResult) {
        stats.push({
            allTime: Number(rawResultElement['allTime']),
            balance: Number(rawResultElement['balance']),
            last7Days: Number(rawResultElement['last7Days']),
            thisMonth: Number(rawResultElement['thisMonth']),
            templateId: rawResultElement['templateId']
        })
    }
    return stats
}

export interface GetMyTemplatePurchasesParams {
    templateId?: string
    purchaseId?: string
    perPage?: number
    page?: number
}

export interface MyPurchasedTemplate {
    purchase: TemplatePurchase
    listing: TemplateListing
    creator: Creator
}

export const GetPurchasedTemplates = async (userId: string, params: GetMyTemplatePurchasesParams): Promise<MyTemplatePurchase[]> => {
    const {templateId, purchaseId} = params
    let offset = 0
    let page = Number(params.page)
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 20
    if (Number.isNaN(page) || page < 1) page = 1
    if (page > 1) {
        offset = (page - 1) * perPage
    }
    const qB = new TemplatePurchaseService()
        .getRepository()
        .createQueryBuilder("tP")
        // .addSelect('t')
        .addSelect('tL')
        .addSelect('c')
        .leftJoin(Template, "t", "tP.templateId=t.id")
        .leftJoin(TemplateListing, "tL", "tP.templateListingId=tL.id")
        .leftJoin(Creator, "c", "t.creatorId=c.id")
        .where("tP.purchasedById=:userId", {userId})
        .offset(offset)
        .limit(perPage)
        .orderBy('tP.createdAt', "DESC")

    if (templateId) {
        qB.andWhere({templateId})
    }
    if (purchaseId) {
        qB.andWhere("tP.id=:purchaseId", {purchaseId})
    }

    const r1 = await qB.getRawMany()

    return r1.map(r => {
        const result: MyPurchasedTemplate = {
            listing: {} as TemplateListing,
            purchase: {} as TemplatePurchase,
            creator: {} as Creator,
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "tL") {
                result.listing[field] = r[key]
            } else if (pre === "tP") {
                result.purchase[field] = r[key]
            } else if (pre === "c") {
                result.creator[field] = r[key]
            }
        }
        return result
    })
}

export interface InstalledTemplate {
    install: TemplateInstall
    listing: TemplateListing
    creator: Creator
    versionNumber: number
}

export const GetWorkspaceInstalledTemplates = async (userId: string, workspaceId: string, params: GetMyTemplatePurchasesParams): Promise<InstalledTemplate[]> => {
    const {templateId, purchaseId} = params
    let offset = 0
    let page = Number(params.page)
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 20
    if (Number.isNaN(page) || page < 1) page = 1
    if (page > 1) {
        offset = (page - 1) * perPage
    }


    const sQ1 = new TemplateInstallService().getRepository()
        .createQueryBuilder()
        .select("MAX(id)", "maxId")
        .where({workspaceId: workspaceId})
        .groupBy("templateId")


    const qB = new TemplateInstallService()
        .getRepository()
        .createQueryBuilder("tI")
        .select('tI.templateId')
        .distinct(true)
        .addSelect("tI")
        .addSelect('tL')
        .addSelect('tR.versionNumber', 'versionNumber')
        .addSelect('c')
        .leftJoin(Template, "t", "tI.templateId=t.id")
        .leftJoin(TemplateListing, "tL", "tL.id=t.marketplaceListingId")
        .leftJoin(TemplateRelease, "tR", "tI.templateReleaseId=tR.id")
        .leftJoin(Creator, "c", "t.creatorId=c.id")
        // .where("tI.workspaceId=:workspaceId", {workspaceId})
        .where(`tI.id IN (${sQ1.getQuery()})`)
        .setParameters(sQ1.getParameters())
        .offset(offset)
        .limit(perPage)
        .orderBy('tI.completedAt', "DESC")

    if (templateId) {
        qB.andWhere({templateId})
    }
    if (purchaseId) {
        qB.andWhere("tP.id=:purchaseId", {purchaseId})
    }

    const r1 = await qB.getRawMany()

    return r1.map(r => {
        const result: InstalledTemplate = {
            listing: {} as TemplateListing,
            install: {} as TemplateInstall,
            creator: {} as Creator,
            versionNumber: r['versionNumber']
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "tL") {
                result.listing[field] = r[key]
            } else if (pre === "tI") {
                result.install[field] = r[key]
            } else if (pre === "c") {
                result.creator[field] = r[key]
            }
        }
        return result
    })
}
