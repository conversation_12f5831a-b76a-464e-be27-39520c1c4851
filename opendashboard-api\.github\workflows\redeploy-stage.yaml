# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Redeploy Stage

on:
  workflow_dispatch:

jobs:
  deploy-node:
    uses: Opendashboard-Inc/opendashboard-workflow/.github/workflows/redeploy-node.yaml@main
    with:
      PROJECT_PATH: ${{ vars.STAGE_PATH }}
      ENV_FILE_PATH: ${{ vars.STAGE_ENV_PATH }}
    secrets:
      SSH_HOST: ${{ vars.STAGE_HOST }}
      SSH_KEY: ${{ secrets.SSH_KEY }}
      SSH_USER: ${{ secrets.SSH_USER }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}




