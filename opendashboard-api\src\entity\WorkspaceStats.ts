import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"

@Entity()
export class WorkspaceStats {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Column({type: 'varchar', nullable: false, unique: true})
    workspaceId: string

    @Index()
    @Column({type: 'smallint', nullable: false, unsigned: true})
    users: number

    @Index()
    @Column({type: 'smallint', nullable: false, unsigned: true})
    collaborators: number

    @Index()
    @Column({type: 'int', nullable: false, unsigned: true})
    records: number

    @Index()
    @Column({type: 'smallint', nullable: false, unsigned: true})
    sendingEmails: number

    @Index()
    @Column({type: 'smallint', nullable: false, unsigned: true})
    sendingDomains: number

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

