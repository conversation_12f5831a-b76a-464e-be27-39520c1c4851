import {Routes} from "../../routes";
import {verifyJWT} from "../../middleware/verifyJWT";
import {WorkflowController} from "./controller";

export const workflowRoutes: Routes = {
    basePath: '/workspaces/:id/workflows',
    middleware: [verifyJWT],
    routes: {
        '/': {
            get: {controller: WorkflowController, action: "getWorkflows"},
            post: {controller: WorkflowController, action: "createWorkflow"},
            patch: {controller: WorkflowController, action: "updateWorkflow"},
            delete: {controller: WorkflowController, action: "deleteWorkflow"},
        },
        '/:workflowId/publish': {
            post: {controller: WorkflowController, action: "publishWorkflow"},
        },
        '/:workflowId/discard-draft': {
            post: {controller: WorkflowController, action: "discardDraftWorkflow"},
        },
        '/:workflowId/test-step': {
            post: {controller: WorkflowController, action: "testStep"},
        },
        '/:workflowId/instances': {
            get: {controller: WorkflowController, action: "getInstances"},
            post: {controller: WorkflowController, action: "createInstance"},
            delete: {controller: WorkflowController, action: "deleteInstance"},
        },
        '/:workflowId/instances/update-status': {
            post: {controller: WorkflowController, action: "updateInstancesStatus"},
        },
        '/:workflowId/instances/:instanceId/tasks': {
            get: {controller: WorkflowController, action: "getInstanceTasks"},
        },
        '/:workflowId/instances/:instanceId/approval': {
            post: {controller: WorkflowController, action: "approvePendingInstance"},
        },
    }
}