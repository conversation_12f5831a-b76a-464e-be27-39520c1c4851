# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Build on PR

on:
  pull_request:
    types:
      - synchronize

jobs:
  deploy-react-application:
    uses: Opendashboard-Inc/opendashboard-workflow/.github/workflows/build-pr.yaml@main
