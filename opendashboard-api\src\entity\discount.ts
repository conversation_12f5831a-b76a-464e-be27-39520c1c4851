import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";

@Entity()
@Index(["creatorId", "code"], {unique: true})
export class Discount {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: false})
    name: string

    @Column({type: 'varchar', nullable: false})
    creatorId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    updatedById: string

    @Column({type: 'varchar', nullable: false})
    code: string

    @Column({type: 'int', default: 0})
    amount: number

    @Index()
    @Column({type: 'bool', default: false})
    isPercent: boolean

    @Index()
    @Column({type: 'bool', default: false})
    isActive: boolean

    @Column({type: 'json', nullable: true})
    templateIds: string[]

    @Index()
    @Column({type: 'bool', default: false})
    isAllTemplates: boolean

    @Index()
    @Column({type: 'bool', default: false})
    isValidForPeriod: boolean

    @Column({type: 'timestamp', nullable: true})
    validTo: Date

    @Column({type: 'timestamp', nullable: true})
    validFrom: Date

    @Index()
    @Column({type: 'bool', default: false})
    isUsageLimited: boolean

    @Column({type: 'int', default: 0})
    usageLimit: number

    @Column({type: 'int', default: 0})
    usage: number

    @Column({type: 'int', default: 0})
    reservedUsage: number

    @Column({type: 'timestamp', nullable: true})
    lastUsed: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

}