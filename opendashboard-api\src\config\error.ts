import {isLocal} from "./index";
import * as Sentry from "@sentry/node";
import {consoleLog} from "../businessLogic/logtail";

export const handleError = (error: (string | Error), extra: object = {}, log = true) => {
    if (isLocal()) {
        console.log('Exception:', {error}, {extra})
    } else {
        if (log) consoleLog("Handle exception:", error, extra)
        Sentry.captureException(error, {
            extra: {...extra}
        })
    }
}