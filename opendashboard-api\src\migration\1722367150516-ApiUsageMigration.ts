import { MigrationInterface, QueryRunner } from "typeorm";

export class ApiUsageMigration1722367150516 implements MigrationInterface {
    name = 'ApiUsageMigration1722367150516'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`token_usage\` (\`id\` int NOT NULL AUTO_INCREMENT, \`tokenId\` int NOT NULL, \`createdAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`status\` tinyint NOT NULL DEFAULT '1', \`date\` date NULL, \`ipAddress\` varchar(255) NULL, \`usageCount\` int NOT NULL DEFAULT '0', INDEX \`IDX_339145cfd8f60fb4b96cd2282b\` (\`tokenId\`), INDEX \`IDX_76dba84c4aaee4158a7e5f967c\` (\`date\`), INDEX \`IDX_dfdea1ca2e1a4825ad08c67651\` (\`usageCount\`), UNIQUE INDEX \`IDX_b67a834428017824873633b234\` (\`tokenId\`, \`date\`, \`ipAddress\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_b67a834428017824873633b234\` ON \`token_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_dfdea1ca2e1a4825ad08c67651\` ON \`token_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_76dba84c4aaee4158a7e5f967c\` ON \`token_usage\``);
        await queryRunner.query(`DROP INDEX \`IDX_339145cfd8f60fb4b96cd2282b\` ON \`token_usage\``);
        await queryRunner.query(`DROP TABLE \`token_usage\``);
    }

}
