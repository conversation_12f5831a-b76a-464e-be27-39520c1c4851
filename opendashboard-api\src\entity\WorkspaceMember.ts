import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"

export enum WorkspaceMemberRole {
    Owner = "owner",
    Admin = "admin",
    Member = "member",
    Collaborator = "collaborator",
    SupportUser = "supportUser"
}

@Entity()
@Index(["workspaceId", "userId"], {unique: true})
export class WorkspaceMember {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Column({type: 'varchar', nullable: false})
    role: WorkspaceMemberRole

    @Column({type: 'varchar', nullable: false})
    invitedById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

