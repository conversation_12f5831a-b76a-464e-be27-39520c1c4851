import {AIColumn, AIColumnRecordMetadata, AutoGeneratedColumnTypes, CanBeUniqueColumnTypes, DatabaseDefinition, DatabaseFieldDataType, FileItem, Match, ProcessedDbRecord, RecordValues, TagColumnDbValue} from "opendb-app-db-utils/lib/typings/db";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, ServerProcessingError, UnauthorizedError, UniqueConstraintError} from "../errors/AppError";
import {GetMyWorkspace, getWorkspaceMemberNoCacheNoAuth, HasPermission, SaveActivities, WorkspacePermission} from "./workspace";
import {CreateDatabaseData, DatabaseService} from "../service/database";
import {CreatePageData, PageService} from "../service/page";
import {CreateViewData, ViewService} from "../service/view";
import {TableViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {getMyPages, PermissiblePage, resolvePageObjectAccessLevel} from "./page";
import {Database} from "../entity/Database";
import {AccessLevel, Visibility} from "../entity/common";
import {CreateRecordData, OnDuplicateAction, RecordService, UpdateRecordData} from "../service/record";
import {ApiMessage} from "../controller/interface";
import {Record, RecordMetadata} from "../entity/Record";
import {In} from "typeorm";
import {PagePermission} from "../entity/PagePermission";
import {constructDb, generatePseudoDbRecords, recordValueToText, transformRawRecords} from "opendb-app-db-utils/lib/utils/db";
import {ObjectType, Person} from "opendb-app-db-utils/lib/typings/common";
import {getCompanyDbDefinition, getContactDbDefinition, getCustomerDbDefinition, getDatabasePackageName} from "opendb-app-db-utils/lib/utils/onboarding";

import {RecalculateWorkspaceStats} from "./stats";
import {broadcastDatabaseColumnMadeUnique, broadcastDatabaseCreated, broadcastDatabaseDefinitionUpdated, broadcastRecordsCreated, broadcastRecordsDeleted, broadcastRecordUpdated} from "../socketio/workspace";
import {KeyValueStore} from "../entity/WorkspaceMemberSettings";
import {SubstituteVarData, substituteVars} from "opendb-app-db-utils/lib/methods/object";
import {AIProvider, generateAIContentAndLog} from "./ai/ai";
import {AIContentGenerationLog} from "../entity/AIContentGenerationLog";
import {Page} from "../entity/Page";
import {arrayDeDuplicate, generateUUID} from "opendb-app-db-utils/lib";
import {CreateActivity} from "../service/activity";
import {ActivityObjectType, ActivityType, ChangeType, RecordValuesChangeData} from "../entity/Activity";
import {handleError} from "../config/error";
import {extractTextFromDocument} from "./textExtraction";
import {ExecuteOnDatabaseActivityWorkflowHook} from "./hooks/database";
import {RestoreWorkspaceLimitedFunctionality} from "./billing";
import {Request} from "express";
import {FileType, ProcessFormToSpaceUpload} from "./doUpload";

export const SystemParams = Object.freeze({
    UserId: "system",
    Name: "System",
})


export const GetMyDatabases = async (userId: string, workspaceId: string, permission: 'shared' | 'mine' = 'mine') => {
    const pages = await getMyPages(userId, workspaceId, "database", permission)

    const ids = pages.pages.map(p => p.page.databaseId)
    const s = new DatabaseService()
    const dbs = await s.find({id: In(ids)})
    const dbIdMap: {
        [id: string]: Database
    } = {}
    for (const db of dbs) dbIdMap[db.id] = db

    const databases: PermissibleDatabaseWithPermissions[] = []
    for (const page of pages.pages) {
        const dbId = page.page.databaseId
        if (!dbIdMap[dbId]) continue
        const db: PermissibleDatabaseWithPermissions = {
            ...page,
            database: dbIdMap[dbId]
        }
        databases.push(db)
    }
    return {
        databases
    }
}

export const GetMyDatabase = async (userId: string, workspaceId: string, databaseId: string) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)

    if (!resolve) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s3 = new ViewService()
    const views = await s3.find({
        pageId: resolve.page.id
    })

    const database: PermissibleDatabaseWithPermissions = {
        page: resolve.page,
        views: views,
        accessLevel: resolve.accessLevel,
        permissions: resolve.permissions,
        database: resolve.database
    }
    return {
        database
    }
}

export interface CreateDatabaseRequestData {
    definition?: DatabaseDefinition,
    name: string
    visibility?: Visibility
}

export interface PermissibleDatabase extends PermissiblePage {
    database: Database
}

export interface PermissibleDatabaseWithPermissions extends PermissibleDatabase {
    permissions: PagePermission[]
}

export const CreateDatabase = async (userId: string, workspaceId: string, requestData: CreateDatabaseRequestData) => {
    const name = requestData.name
    if (!name) {
        throw new RequiredParameterError("name")
    }
    const visibility = requestData.visibility || Visibility.Open
    if (![Visibility.Open, Visibility.Private].includes(visibility)) {
        throw new InvalidParameterError("visibility")
    }
    let definition = requestData.definition
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.CreateDatabase)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    if (definition && !validateDefinition(definition)) {
        throw new InvalidParameterError("definition")
    }
    let values: RecordValues[] = [];
    if (!definition) {
        const person: Person = {
            firstName: "", id: userId, image: undefined, lastName: "", title: ""
        }
        const boilerplate = BoilerplateDatabase([person])
        definition = boilerplate.definition
        values = boilerplate.values
    }

    const data: CreateDatabaseData = {
        name,
        workspaceId,
        createdById: userId,
        definition
    }
    const recordsData: CreateRecordData[] = values.map(r => {
        const data: CreateRecordData = {
            createdById: userId,
            databaseId: '',
            updatedById: userId,
            recordValues: r,
            uniqueValue: null
        }
        return data
    })
    const {database, page, view, records} = await createDbPageViews(data, recordsData, visibility)
    const db: PermissibleDatabaseWithPermissions = {
        database,
        page,
        views: [view],
        accessLevel: AccessLevel.Full,
        permissions: []
    }
    broadcastDatabaseCreated(workspaceId, db)
    return {
        database: db, records
    }

}

const validateDefinition = (definition: DatabaseDefinition) => {
    return definition
        && typeof definition === 'object'
        && definition.columnsMap
        && definition.columnIds
        && Array.isArray(definition.columnIds)
}

export interface CreateRecordsRequestData {
    valuesList: RecordValues[]
    onDuplicate: OnDuplicateAction
    // typecast?: boolean
    // splitTypecastByComma?: boolean
}

export const AddRecords = async (userId: string, workspaceId: string, databaseId: string, reqData: CreateRecordsRequestData, overrideAccessLevelPermission = false) => {
    let {valuesList: values, onDuplicate} = reqData

    if (!values) {
        throw new RequiredParameterError("values")
    }
    if (!Array.isArray(values)) {
        throw new InvalidParameterError("values is invalid")
    }
    if (!onDuplicate) {
        onDuplicate = OnDuplicateAction.Update
    }

    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database} = resolve

    return AddRecordsNoAuth(userId, database, values, onDuplicate)

    //
    // const definition = database.definition
    //
    // const toInsert: CreateRecordData[] = []
    //
    // const now = new Date().getTime()
    //
    // for (let i = 0; i < values.length; i++) {
    //     const recordValues = values[i];
    //     let uniqueValue = null
    //
    //     if (definition.uniqueColumnId) {
    //         uniqueValue = recordValues[definition.uniqueColumnId] || null
    //     }
    //     const date = new Date(now + i * 10)
    //     const recordData: CreateRecordData = {
    //         createdById: userId,
    //         databaseId: database.id,
    //         updatedById: userId,
    //         recordValues: recordValues,
    //         uniqueValue,
    //         createdAt: date,
    //         updatedAt: date,
    //     }
    //     toInsert.push(recordData);
    // }
    // const service = new RecordService()
    //
    // try {
    //     const records = await service.addRecords(toInsert, onDuplicate)
    //
    //     await saveRecordsCreatedActivities(userId, database, records)
    //     broadcastRecordsCreated(workspaceId, database, records)
    //
    //     RecalculateWorkspaceStats(workspaceId).then()
    //     ExecuteOnDatabaseActivityWorkflowHook('created', workspaceId, database, records.map(r => r.id)).then()
    //
    //     return {records, database}
    //
    // } catch (err) {
    //     if (err.message.includes("Duplicate entry")) throw new BadRequestError(ApiMessage.DuplicateEntryFoundWhileAdding)
    //
    //     throw new ServerProcessingError(err.message)
    // }

}

export const AddRecordsNoAuth = async (userId: string, database: Database, values: RecordValues[], onDuplicate = OnDuplicateAction.Update) => {
    const workspaceId = database.workspaceId
    const service = new RecordService()

    const definition = database.definition

    const toInsert: CreateRecordData[] = []

    const now = new Date().getTime()

    for (let i = 0; i < values.length; i++) {
        const recordValues = values[i];
        let uniqueValue = null

        if (definition.uniqueColumnId) {
            uniqueValue = recordValues[definition.uniqueColumnId] || null
        }
        const date = new Date(now + i * 10)
        const recordData: CreateRecordData = {
            createdById: userId,
            databaseId: database.id,
            updatedById: userId,
            recordValues: recordValues,
            uniqueValue,
            createdAt: date,
            updatedAt: date,
        }
        toInsert.push(recordData);
    }

    try {
        const records = await service.addRecords(toInsert, onDuplicate)

        await saveRecordsCreatedActivities(userId, database, records)
        broadcastRecordsCreated(workspaceId, database, records)

        RecalculateWorkspaceStats(workspaceId).then()
        ExecuteOnDatabaseActivityWorkflowHook('created', workspaceId, database, records.map(r => r.id)).then()

        return {records, database}

    } catch (err) {
        if (err.message.includes("Duplicate entry")) throw new BadRequestError(ApiMessage.DuplicateEntryFoundWhileAdding)

        throw new ServerProcessingError(err.message)
    }

}

export const resolveDatabaseAccessLevel = async (userId: string, workspaceId: string, databaseId: string) => {
    const pgAccess = await resolvePageObjectAccessLevel(userId, workspaceId, "database", databaseId)
    if (!pgAccess) return null
    const s = new DatabaseService()
    const database = await s.findOne({id: databaseId, workspaceId})

    if (!database) return null
    return {
        ...pgAccess, database
    }
}

export const resolveDatabaseAccessLevelViaRefPageAndView = async (userId: string, workspaceId: string, databaseId: string, refPageId: string, refViewId: string) => {
    const pgAccess = await resolvePageObjectAccessLevel(userId, workspaceId, "page", refPageId)
    if (!pgAccess) return null

    const vS = new ViewService()
    const view = await vS.findOne({id: refViewId, pageId: refPageId})
    if (!view) return null

    const s = new DatabaseService()
    const database = await s.findOne({id: databaseId, workspaceId})
    if (!database) return null

    return {
        ...pgAccess, database
    }
}

export interface RecordIdMap {
    [id: string]: {
        record: Record
        processedRecord?: ProcessedDbRecord
    }
}

export interface AdjacentDatabases {
    [key: string]: {
        database?: Database,
        page?: Page,
        recordsMap?: RecordIdMap,
        error?: string
    }
}

export const GetRecords = async (userId: string, workspaceId: string, databaseId: string, inIds: string[] = [], overrideAccessLevelPermission = false) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit, AccessLevel.View].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database, permissions, member, accessLevel, page} = resolve

    const {records, adjacentDatabases} = await getRecordsNoAuth(database, inIds)
    return {
        records, database, permissions, accessLevel, page, adjacentDatabases
    }
}

export const GetProcessedRecords = async (userId: string, workspaceId: string, databaseId: string, inIds: string[] = [], overrideAccessLevelPermission = false) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)

    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit, AccessLevel.View].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database, permissions, member, accessLevel, page} = resolve

    const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId)
    const persons: Person[] = members.map(m => {
        const p: Person = {
            firstName: m.user.firstName, id: m.user.id,
            image: {type: ObjectType.Image, url: m.user.profilePhoto},
            lastName: m.user.lastName,
            title: `${m.user.firstName} ${m.user.lastName}`.trim()
        }

        return p
    })
    const {records, adjacentDatabases} = await getRecordsNoAuth(database, inIds)

    const processed = transformRawRecords(resolve.database.definition, records, persons)
    return {
        records, processed, database, permissions, accessLevel, page, adjacentDatabases
    }
}

export const GetAdjacentDatabase = async (userId: string, workspaceId: string, databaseId: string, databaseRefId: string) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseRefId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit, AccessLevel.View].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const databaseRef = resolve

    let grantAccess = false
    for (const column of Object.values(databaseRef.database.definition.columnsMap)) {
        if (column.type === DatabaseFieldDataType.Linked) {
            grantAccess = true
            break
        }
    }
    if (!grantAccess) throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)

    const s2 = new DatabaseService()
    const s3 = new PageService()

    const database = await s2.findOne({workspaceId, id: databaseId})
    if (!database) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const page = await s3.findOne({workspaceId, databaseId})
    if (!page) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const {records, adjacentDatabases} = await getRecordsNoAuth(database)

    return {
        records, database, permissions: [], page, adjacentDatabases
    }
}

export const getRecordsNoAuth = async (database: Database, inIds: string[] = []) => {
    const s = new RecordService()
    let records: Record[] = []

    const {id: databaseId, workspaceId} = database
    if (inIds.length > 0) records = await s.find({databaseId: databaseId, id: In(inIds)}, {createdAt: 'DESC'})
    else records = await s.find({databaseId: databaseId}, {createdAt: 'DESC'})

    const linkedColIdsMap: { [colId: string]: string } = {}
    for (const col of Object.values(database.definition.columnsMap)) {
        if (col.type === DatabaseFieldDataType.Linked && col.databaseId && col.databaseId !== databaseId) {
            linkedColIdsMap[col.id] = col.databaseId
            // linkedColIds.push(col.id)
        }
    }
    const adjacentDatabaseIds: string[] = arrayDeDuplicate(Object.values(linkedColIdsMap))

    const s2 = new DatabaseService()
    const s3 = new PageService()
    const linkedDatabases = await s2.find({workspaceId, id: In(adjacentDatabaseIds)})
    const linkedPages = await s3.find({workspaceId, databaseId: In(adjacentDatabaseIds)})

    const adjacentDatabases: AdjacentDatabases = {}

    for (const database of linkedDatabases) {
        adjacentDatabases[database.id] = adjacentDatabases[database.id] || {
            database,
            page: null,
            recordsMap: {}
        }
        adjacentDatabases[database.id].database = database
    }
    for (const page of linkedPages) {
        adjacentDatabases[page.databaseId] = adjacentDatabases[page.databaseId] || {
            database: null,
            page,
            recordsMap: {}
        }
        adjacentDatabases[page.databaseId].page = page
    }

    const linkedColIds: string[] = Object.keys(linkedColIdsMap)
    let linkedRecordIds: string[] = []
    for (const record of records) {
        for (const id of linkedColIds) {
            const value = record.recordValues[id] as TagColumnDbValue | undefined
            if (value && Array.isArray(value) && value.length > 0 && typeof value[0] === 'string') {
                linkedRecordIds.push(...value)
            }
        }
    }
    linkedRecordIds = arrayDeDuplicate(linkedRecordIds)

    for (const id of adjacentDatabaseIds) {
        if (!adjacentDatabases[id]) {
            adjacentDatabases[id] = {
                error: ErrorMessage.EntityNotFound
            }
        }
    }

    const s4 = new RecordService()
    const adjacentRecords = await s4.find({id: In(linkedRecordIds)})


    for (const r of adjacentRecords) {
        if (!adjacentDatabases[r.databaseId]) continue
        if (!adjacentDatabases[r.databaseId].recordsMap) adjacentDatabases[r.databaseId].recordsMap = {}
        adjacentDatabases[r.databaseId].recordsMap[r.id] = {
            record: r
        }
    }


    return {
        records, adjacentDatabases
    }
}

export interface UpdateRecordRequestData {
    ids: string[]
    values: RecordValues
    meta?: RecordMetadata
}

export const UpdateRecordValues = async (userId: string, workspaceId: string, databaseId: string, data: UpdateRecordRequestData, overrideAccessLevelPermission = false) => {
    let {ids, values, meta} = data
    meta = meta && typeof meta === 'object' ? meta : {}
    if (!values) {
        throw new RequiredParameterError("values")
    }
    if (!ids || !Array.isArray(ids)) {
        throw new RequiredParameterError("ids")
    }
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database} = resolve

    return updateRecordValuesNoAuth(userId, database, data)
}

export const updateRecordValuesNoAuth = async (userId: string, database: Database, data: UpdateRecordRequestData) => {
    let {ids, values, meta} = data
    meta = meta && typeof meta === 'object' ? meta : {}
    if (!values) {
        throw new RequiredParameterError("values")
    }
    if (!ids || !Array.isArray(ids)) {
        throw new RequiredParameterError("ids")
    }
    
    const definition = database.definition
    const recordData: UpdateRecordData = {
        updatedById: userId,
        recordValues: values,
        meta
    }
    if (Object.keys(values).length > 0) {
        let uniqueValue = null
        if (definition.uniqueColumnId) {
            recordData.uniqueValue = String(values[definition.uniqueColumnId] || null)
        }
    }
    const fields = {}
    for (let value of Object.values(database.definition.columnsMap)) {
        fields[value.id] = value.type
    }
    await performRecordUpdate(database, ids, recordData)

    return true
}

export const performRecordUpdate = async (database: Database, ids: string[], data: UpdateRecordData) => {
    const service = new RecordService()

    try {
        await service.updateRecords(database.id, ids, data)

        // await clearDatabaseRecordsCache(databaseId)

        broadcastRecordUpdated(database.workspaceId, database, ids, data.recordValues, data.meta, {updatedById: data.updatedById})

        const fields = {}
        for (let value of Object.values(database.definition.columnsMap)) {
            fields[value.id] = value.type
        }

        const activities = []
        for (let id of ids) {
            const changeData: RecordValuesChangeData = {
                changeType: ChangeType.RecordValues,
                oldValue: undefined,
                newValue: {
                    ...data.recordValues
                },
                meta: {
                    fields
                }
            }
            const activityData: CreateActivity = {
                parentId: "",
                workspaceId: database.workspaceId,
                createdById: data.updatedById,
                databaseId: database.id,
                recordId: id,
                pageId: null,
                objectType: ActivityObjectType.Record,
                objectId: id,
                activityType: ActivityType.ValuesUpdated,
                changeData,
                isResolved: false
            }
            activities.push(activityData)
        }

        await SaveActivities(database.workspaceId, database.id, "", activities)

        ExecuteOnDatabaseActivityWorkflowHook('updated', database.workspaceId, database, ids).then()

        return true
    } catch (err) {
        if (err.message.includes("Duplicate entry")) throw new BadRequestError(ApiMessage.DuplicateEntryFoundWhileUpdating)
        throw new ServerProcessingError(err.message)
    }
}

export interface UpdateRecordSummaryData {
    id: string
    summaryJSON: object
    summaryText: string
}

export const UpdateRecordSummary = async (userId: string, workspaceId: string, databaseId: string, data: UpdateRecordSummaryData) => {
    let {id, summaryJSON, summaryText} = data
    if (!summaryJSON) {
        throw new RequiredParameterError("summaryJSON")
    }
    summaryText = summaryText || ''
    if (!id) {
        throw new RequiredParameterError("id")
    }
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database} = resolve

    const s = new RecordService()
    await s.update({id, databaseId}, {summaryJSON, summaryText})

    // broadcastRecordUpdated(database.workspaceId, database, id, data.recordValues, data.meta, {updatedById: data.updatedById})
    broadcastRecordUpdated(database.workspaceId, database, [id], {}, {}, {summaryJSON, summaryText, updatedById: userId})

}

export interface DeleteRecordsRequestData {
    ids: string[]
}

export const DeleteRecords = async (userId: string, workspaceId: string, databaseId: string, data: DeleteRecordsRequestData, overrideAccessLevelPermission = false) => {
    let {ids} = data

    if (!ids) {
        throw new RequiredParameterError("ids")
    }
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new RecordService()


    await ExecuteOnDatabaseActivityWorkflowHook('deleted', workspaceId, resolve.database, ids)

    await service.update({
        id: In(ids), databaseId
    }, {
        uniqueValue: null,
        updatedById: userId,
        deletedAt: new Date()
    })


    await RestoreWorkspaceLimitedFunctionality(resolve.member.workspace)

    // await clearDatabaseRecordsCache(databaseId)

    // const activities = []
    // for (let id of ids) {
    //     const changeData: ChangeData = {
    //         changeType: ChangeType.NoChange,
    //         oldValue: undefined,
    //         newValue: undefined
    //     }
    //     const activityData: CreateActivity = {
    //         parentId: "",
    //         workspaceId: database.workspaceId,
    //         createdById: userId,
    //         databaseId: database.id,
    //         pageId: null,
    //         objectType: ActivityObjectType.Record,
    //         objectId: id,
    //         activityType: ActivityType.ItemDeleted,
    //         changeData,
    //         isResolved: false
    //     }
    //     activities.push(activityData)
    // }
    // await SaveActivities(database.workspaceId, database.id, "", activities)
    //
    broadcastRecordsDeleted(workspaceId, resolve.database, ids)

    return true
}

export interface MatchRecordsRequestData {
    matchByColId: string
    matchExisting?: boolean
    createMissing?: boolean
    values: RecordValues[]
}

export interface RecordMatchMap {
    [value: string]: {
        recordValue: RecordValues
        record?: Record
        compareValue: string
    }
}

export const MatchAndCreateRecords = async (userId: string, workspaceId: string, databaseId: string, data: MatchRecordsRequestData, overrideAccessLevelPermission = false) => {
    const matchMap: RecordMatchMap = {}
    const {matchByColId, matchExisting, createMissing, values} = data
    if (!matchExisting && !createMissing) {
        throw new RequiredParameterError("matchExisting and createMissing")
    }
    if (!matchByColId) {
        throw new RequiredParameterError("matchByColId")
    }
    if (!values || !Array.isArray(values) || values.length === 0) {
        throw new RequiredParameterError("values")
    }
    const resolve = await GetRecords(userId, workspaceId, databaseId, undefined, overrideAccessLevelPermission)

    const database = resolve.database
    const column = database.definition.columnsMap[matchByColId]

    if (!column) {
        throw new BadRequestError(ErrorMessage.AssocEntityNotFound)
    }
    const contactsDef = getCustomerDbDefinition('')
    const contactsSrcPackageName = getDatabasePackageName(contactsDef)
    const isContacts = database.srcPackageName === contactsSrcPackageName

    const vals: string[] = []
    for (let value of values) {
        const valStr = String(value[matchByColId] || '')
        if (valStr) {
            const valSplit = valStr.split(',')
            vals.push(...valSplit.filter(val => val.trim()).map(v => v.trim()))
        }
    }

    for (let v of arrayDeDuplicate(vals)) {
        const value: RecordValues = {}

        if (isContacts) {
            const split = v.split(/\s+/);
            const lastName = split[0]
            const firstName = split.slice(1).join(' ')

            value['firstName'] = firstName
            value['lastName'] = lastName
        } else value[matchByColId] = v

        matchMap[v.toLowerCase()] = {
            recordValue: value,
            compareValue: v.toLowerCase()
        }
    }


    const existingRecords = resolve.records
    if (matchExisting) {
        let compareValue = ''
        for (const record of existingRecords) {
            if (isContacts) {
                const lastName = String(record.recordValues['lastName'] || '')
                const firstName = String(record.recordValues['firstName'] || '')

                compareValue = `${lastName} ${firstName}`.trim().toLowerCase()
            } else {
                compareValue = String(record.recordValues[matchByColId] || '').trim().toLowerCase()
            }
            if (compareValue && matchMap[compareValue] && !matchMap[compareValue].record) {
                matchMap[compareValue].record = record
            }

        }
    }
    const toCreate = Object.values(matchMap).filter(m => !m.record).map(m => ({
        recordValues: m.recordValue,
        compareValue: m.compareValue
    }))
    let createdRecords: Record[] = []
    if (createMissing && toCreate.length > 0) {
        const rS = new RecordService()

        const crDs: CreateRecordData[] = toCreate.map(({recordValues}) => {
            const rD: CreateRecordData = {
                createdById: userId,
                databaseId: databaseId,
                uniqueValue: undefined,
                updatedById: userId,
                recordValues
            }
            if (!isContacts && database.definition.uniqueColumnId && database.definition.uniqueColumnId === matchByColId) {
                rD.uniqueValue = String(values[matchByColId])
            }
            return rD
        })
        createdRecords = await rS.addRecords(crDs, OnDuplicateAction.Update)

        await saveRecordsCreatedActivities(userId, database, createdRecords);
        broadcastRecordsCreated(workspaceId, database, createdRecords)

        RecalculateWorkspaceStats(workspaceId).then()
    }
    for (let record of createdRecords) {
        let compareValue = ''
        if (isContacts) {
            const lastName = String(record.recordValues['lastName'] || '')
            const firstName = String(record.recordValues['firstName'] || '')

            compareValue = `${lastName} ${firstName}`.trim().toLowerCase()
        } else {
            compareValue = String(record.recordValues[matchByColId] || '').trim().toLowerCase()
        }
        if (compareValue && matchMap[compareValue] && !matchMap[compareValue].record) {
            matchMap[compareValue].record = record
        }
    }

    return {matchMap}
}

export const MakeDatabaseColumnUnique = async (userId: string, workspaceId: string, databaseId: string, columnId: string, overrideAccessLevelPermission = false) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)

    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database, permissions, page, accessLevel} = resolve
    const definition = database.definition

    const column = definition.columnsMap[columnId]
    if (!column) {
        throw new BadRequestError("Column not found")
    }
    if (!CanBeUniqueColumnTypes.includes(column.type)) {
        throw new BadRequestError("Column type cannot be made unique")
    }
    const recordsService = new RecordService()
    try {
        await recordsService.makeColumnUnique(databaseId, columnId)
    } catch (e) {
        let message = e.message
        if (e instanceof UniqueConstraintError) {
            const {records} = await getRecordsNoAuth(database)
            const values = records
                .filter(r => r.recordValues[columnId] !== undefined && r.recordValues[columnId] !== null)
                .map(r => String(r.recordValues[columnId]))

            const frequencyMap: { [key: string]: number } = {};

            values.forEach(value => {
                if (frequencyMap[value]) {
                    frequencyMap[value]++;
                } else {
                    frequencyMap[value] = 1;
                }
            });

            const duplicatedValues = Object.keys(frequencyMap)
                .filter(key => frequencyMap[key] > 1)
                .map(key => `${key}(${frequencyMap[key]})`)

            message = `Column contains duplicate values, remove and try again. `
            if (duplicatedValues.length > 0) {
                message += ` Duplicated values are ${duplicatedValues.join(", ")}`
            }
        }
        throw new BadRequestError(message)
    }
    definition.uniqueColumnId = columnId
    const update: Partial<Database> = {
        definition
    }
    const service = new DatabaseService()
    await service.update({id: databaseId}, update)

    // const changeData = {
    //     changeType: ChangeType.NoChange,
    //     oldValue: null,
    //     newValue: null
    // }
    // const activityData: CreateActivity = {
    //     parentId: null,
    //     workspaceId,
    //     createdById: userId,
    //     databaseId: database.id,
    //     pageId: null,
    //     objectType: ActivityObjectType.Database,
    //     objectId: database.id,
    //     activityType: ActivityType.DatabaseColumnMadeUnique,
    //     changeData: changeData,
    //     isResolved: false
    // }
    // await SaveActivities(workspaceId, database.id, "", [activityData])
    //
    // await clearDatabaseRecordsCache(databaseId)

    broadcastDatabaseColumnMadeUnique(workspaceId, databaseId, {columnId, unique: true})

    const db = {
        database,
        page,
        accessLevel: AccessLevel.Full
    }

    return {
        database: db, permissions
    }
}

export const RemoveDatabaseColumnUnique = async (userId: string, workspaceId: string, databaseId: string, overrideAccessLevelPermission = false) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)

    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database, permissions, page} = resolve
    const definition = database.definition

    const uniqueColId = definition.uniqueColumnId
    const recordsService = new RecordService()
    await recordsService.removeColumnUnique(databaseId)
    definition.uniqueColumnId = null

    const update: Partial<Database> = {
        definition
    }
    const service = new DatabaseService()
    await service.update({id: databaseId}, update)
    // await clearDatabaseRecordsCache(databaseId)

    // const changeData = {
    //     changeType: ChangeType.NoChange,
    //     oldValue: null,
    //     newValue: null
    // }
    // const activityData: CreateActivity = {
    //     parentId: null,
    //     workspaceId,
    //     createdById: userId,
    //     databaseId: database.id,
    //     pageId: null,
    //     objectType: ActivityObjectType.Database,
    //     objectId: database.id,
    //     activityType: ActivityType.DatabaseColumnRemovedUnique,
    //     changeData: changeData,
    //     isResolved: false
    // }
    // await SaveActivities(workspaceId, database.id, "", [activityData])

    broadcastDatabaseColumnMadeUnique(workspaceId, databaseId, {columnId: uniqueColId, unique: false})

    const db = {
        database,
        page,
        accessLevel: AccessLevel.Full
    }

    return {
        database: db, permissions
    }
}

export const MakeDatabaseColumnTitle = async (userId: string, workspaceId: string, databaseId: string, columnId: string, overrideAccessLevelPermission = false) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)

    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel) && !overrideAccessLevelPermission)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database, permissions, page, accessLevel} = resolve
    const definition = database.definition

    const column = definition.columnsMap[columnId]

    definition.titleColumnId = column ? columnId : ''
    const update: Partial<Database> = {
        definition
    }
    const service = new DatabaseService()
    await service.update({id: databaseId}, update)

    broadcastDatabaseDefinitionUpdated(workspaceId, databaseId, {titleColumnId: columnId})

    const db = {
        database,
        page,
        accessLevel: AccessLevel.Full
    }

    return {
        database: db, permissions
    }
}

const RecordMetaKey = (type: "column", id: string) => {
    return `${type}:${id}`
}

export const GenerateDatabaseColumnAICellValue = async (userId: string, workspaceId: string, databaseId: string, recordId: string, columnId: string, overrideAccessLevelPermission = false) => {
    const resolve = await GetRecords(userId, workspaceId, databaseId, [recordId], overrideAccessLevelPermission)

    const records = resolve.records
    if (records.length === 0) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const database = resolve.database
    const aiColumn = database.definition.columnsMap[columnId] as AIColumn | undefined
    if (!aiColumn) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (!aiColumn.prompt || !aiColumn.prompt.trim()) {
        throw new RequiredParameterError("Prompt")
    }


    /**
     * set loading metadata for cell & broadcast
     * get the column
     * get the prompt
     * substitute the tags into the prompt
     * send to AI
     * save response/error with metadata & broadcast
     */


    {
        const colMetadata: AIColumnRecordMetadata = {
            inProgress: true,
            error: '',
            generatedAt: null,
            value: null,
            contentId: null
        }
        const meta: KeyValueStore = {}
        meta[RecordMetaKey('column', columnId)] = colMetadata
        const setLoading: UpdateRecordData = {
            updatedById: userId,
            recordValues: {},
            meta
        }
        await performRecordUpdate(database, [recordId], setLoading)
    }

    const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId)
    const persons: Person[] = members.map(m => {
        const p: Person = {
            firstName: m.user.firstName, id: m.user.id,
            image: {type: ObjectType.Image, url: m.user.profilePhoto},
            lastName: m.user.lastName,
            title: `${m.user.firstName} ${m.user.lastName}`.trim()
        }

        return p
    })
    const record = records[0]
    const processed = transformRawRecords(resolve.database.definition, [record], persons)

    const vars: SubstituteVarData = {};
    for (const key of Object.keys(processed[0].processedRecordValues)) {
        const val = processed[0].processedRecordValues[key];
        vars[key] = recordValueToText(val);
    }
    let prompt = substituteVars(aiColumn.prompt, vars);
    const maxWords = aiColumn.maxWordOutput || 1000;
    const fileItems: FileItem[] = []

    const attachmentColumnIds = Array.isArray(aiColumn.attachmentColumnIds) && aiColumn.attachmentColumnIds.length > 0 ? aiColumn.attachmentColumnIds : []
    if (attachmentColumnIds.length > 0) {
        for (const columnId of attachmentColumnIds) {
            const val = record.recordValues[columnId]
            if (val && Array.isArray(val) && val.length > 0) {
                for (let fileItem of val) {
                    if (typeof fileItem === 'object' && fileItem.name && fileItem.link && fileItem.type) {
                        fileItems.push(fileItem)
                    }
                }
            }
        }
    }

    if (fileItems.length > 0) {
        const attachments: { title: string, type: string, textContent: string }[] = []

        for (let fileItem of fileItems) {
            try {
                const textContent = await extractTextFromDocument(fileItem.link, fileItem.type)
                attachments.push({
                    title: fileItem.name,
                    type: fileItem.type,
                    textContent
                })
            } catch (e) {
                handleError(e)
            }
        }
        if (attachments.length > 0) {
            prompt += `\n\nWith attachments with their text extracted to JSON below: \n${JSON.stringify(attachments)}`
        }
    }


    let aiContentLog: AIContentGenerationLog;
    let exception: Error
    try {
        const r = await generateAIContentAndLog(workspaceId, {
            userPrompt: prompt,
            purpose: 'Generated for AI column',
            metadata: {
                columnId,
                recordId,
                databaseId,
            },
            maxWords
        })
        aiContentLog = r.aiContentLog
    } catch (e) {
        exception = e
    }

    {
        const recordValues: RecordValues = {}
        const meta: KeyValueStore = {}

        meta[RecordMetaKey('column', columnId)] = {
            inProgress: false,
            error: exception?.message || '',
            generatedAt: aiContentLog ? new Date() : null,
            value: aiContentLog ? aiContentLog.contentGenerated : null,
            contentId: aiContentLog ? aiContentLog.id : null
        }
        if (aiContentLog) {
            recordValues[columnId] = aiContentLog.contentGenerated
        }
        const setLoading: UpdateRecordData = {
            updatedById: userId,
            recordValues,
            meta
        }
        await performRecordUpdate(database, [recordId], setLoading)
    }
}

export const BoilerplateDatabase = (persons: Person[], addUniqueCol = false) => {
    const definition = constructDb()
    if (addUniqueCol) {
        let uniqueColumnId = "";
        for (const col of Object.values(definition.columnsMap)) {
            if (col.type === DatabaseFieldDataType.Text) {
                uniqueColumnId = col.id
                break
            }
        }
        definition.uniqueColumnId = uniqueColumnId
    }
    const records = generatePseudoDbRecords(definition, 5, persons)
    const values: RecordValues[] = records.map(record => record.recordValues)
    return {
        definition, values
    }
}

export const SetupInternalWorkspaceDatabases = async (workspaceId: string, userId: string) => {
    const companyDef = getCompanyDbDefinition()
    const s = new DatabaseService()

    const person: Person = {
        firstName: "", id: userId, image: undefined, lastName: "", title: ""
    }

    let companiesDatabase = await s.findOne({workspaceId, srcPackageName: getDatabasePackageName(companyDef)})
    if (!companiesDatabase) {
        const data: CreateDatabaseData = {
            name: "Companies",
            workspaceId,
            createdById: userId,
            definition: companyDef.definition,
            srcPackageName: getDatabasePackageName(companyDef),
            srcVersionNumber: companyDef.versionNumber,
            srcVersionName: companyDef.versionName
        }
        const records = generatePseudoDbRecords(companyDef.definition, 5, [person])
        const values: RecordValues[] = records.map(record => record.recordValues)
        const recordsData: CreateRecordData[] = values.map(r => {
            const data: CreateRecordData = {
                createdById: userId,
                databaseId: '',
                updatedById: userId,
                recordValues: r,
                uniqueValue: null
            }
            return data
        })

        const res = await createDbPageViews(data, recordsData)
        companiesDatabase = res.database
    }

    const contactsDef = getCustomerDbDefinition(companiesDatabase.id)
    let contactsDatabase = await s.findOne({workspaceId, srcPackageName: getDatabasePackageName(contactsDef)})
    if (!contactsDatabase) {
        const data: CreateDatabaseData = {
            name: "Contacts",
            workspaceId,
            createdById: userId,
            definition: contactsDef.definition,
            srcPackageName: getDatabasePackageName(contactsDef),
            srcVersionNumber: contactsDef.versionNumber,
            srcVersionName: contactsDef.versionName
        }
        const records = generatePseudoDbRecords(contactsDef.definition, 5, [person])
        const values: RecordValues[] = records.map(record => record.recordValues)
        const recordsData: CreateRecordData[] = values.map(r => {
            const data: CreateRecordData = {
                createdById: userId,
                databaseId: '',
                updatedById: userId,
                recordValues: r,
                uniqueValue: null
            }
            return data
        })

        const res = await createDbPageViews(data, recordsData)
        contactsDatabase = res.database
    }


    return {contactsDatabase, companiesDatabase}
}

const createDbPageViews = async (data: CreateDatabaseData, recordsData: CreateRecordData[] = [], visibility = Visibility.Open) => {
    const {workspaceId, createdById: userId} = data
    const person: Person = {
        firstName: "", id: userId, image: undefined, lastName: "", title: ""
    }

    const s = new DatabaseService()
    const s2 = new PageService()
    const s3 = new ViewService()
    const rS = new RecordService()

    const database = await s.insert(data)

    for (let data of recordsData) {
        data.databaseId = database.id
    }

    const records = await rS.addRecords(recordsData)

    await saveRecordsCreatedActivities(userId, database, records);


    const pageData: CreatePageData = {
        name: data.name,
        workspaceId,
        createdById: userId,
        ownerId: userId,
        icon: null,
        databaseId: database.id,
        visibility
    }
    const page = await s2.createPage(pageData)

    const viewDef: TableViewDefinition = {
        type: ViewType.Table,
        databaseId: database.id,
        filter: {
            conditions: [],
            match: Match.All
        },
        sorts: [],
        columnsOrder: [],
        columnPropsMap: {},
        lockContent: false
    }
    const viewData: CreateViewData = {
        pageId: page.id,
        name: `All ${page.name}`,
        type: ViewType.Table,
        definition: viewDef,
        createdById: userId
    }
    const view = await s3.insert(viewData)

    await s2.update({id: page.id}, {viewsOrder: [view.id]})
    page.viewsOrder = [view.id]

    return {
        view, records, database, page
    }
}

export const saveRecordsCreatedActivities = async (userId: string, database: Database, records: Record[]) => {

    const fields = {}
    for (let value of Object.values(database.definition.columnsMap)) {
        fields[value.id] = value.type
    }

    const activities = []
    for (let record of records) {
        const changeData: RecordValuesChangeData = {
            changeType: ChangeType.RecordValues,
            oldValue: undefined,
            newValue: {
                ...record.recordValues
            },
            meta: {
                fields
            }
        }
        const activityData: CreateActivity = {
            parentId: "",
            workspaceId: database.workspaceId,
            createdById: userId,
            databaseId: database.id,
            recordId: record.id,
            pageId: null,
            objectType: ActivityObjectType.Record,
            objectId: record.id,
            activityType: ActivityType.ItemCreated,
            changeData,
            isResolved: false
        }
        activities.push(activityData)
    }
    await SaveActivities(database.workspaceId, database.id, "", activities)
}

export interface SuggestDbImportMappingRequestData {
    databaseId: string
    importPreview: string[][]
}

export const suggestDbImportMapping = async (userId: string, workspaceId: string, data: SuggestDbImportMappingRequestData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    if (!data.databaseId) {
        throw new RequiredParameterError("databaseId")
    }
    if (!data.importPreview) {
        throw new RequiredParameterError("importPreview")
    }
    const dS = new DatabaseService()
    const dB = await dS.findOne({workspaceId, id: data.databaseId})
    if (!dB) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    // const sys

    const fields: { id: string, title: string, type: string } [] = []
    for (let {id, type, title} of Object.values(dB.definition.columnsMap)) {
        if (!AutoGeneratedColumnTypes.includes(type)) {
            fields.push({id, title, type})
        }
    }

    let prompt = `Return a RFC 7159 compliant JSON object representing a data mapping between the fields in a database with the type and the columns in a csv file to be imported.`
    prompt += `\nfields: ${JSON.stringify(fields)}`
    prompt += `\ncsv preview: ${JSON.stringify(data.importPreview)}`
    prompt += `\n The JSON response must be an object mapping of the form {[colIndex: string]: fieldId}.`
    prompt += 'If you cannot determine an appropriate field, the field Id should be an empty  string'
    prompt += 'When matching fields, match based on name, type, the values in the columns and other heuristics as you see fit.  '
    // + 'Do a loose matching ie even when a column does not fully match another field provided there is some level of matching, you should match it.'
    prompt += 'A field in the database should be assigned to AT MOST ONE column.'
    prompt += 'Make sure that the keys in the returned object match the column indexes in the preview data.'
    prompt += 'Verify that the response matches my requirement before returning it.'
    prompt += 'Only return a RFC 7159 complaint JSON object ONLY and verify the response is a RFC 7159 compliant JSON object.'

    const {aiContentLog} = await generateAIContentAndLog('', {
        systemPrompt: '',
        userPrompt: prompt,
        provider: AIProvider.Anthropic,
        purpose: 'Suggest data mapping for import'
    }, true)

    return aiContentLog
}

export const ActivateMessaging = async (userId: string, workspaceId: string, databaseId: string) => {
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel))) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database} = resolve
    const definition = database.definition
    if (database.isMessagingEnabled) {
        return true
    }
    const contactDbDefinition = getContactDbDefinition()

    for (let [key, value] of Object.entries(contactDbDefinition.definition.columnsMap)) {
        if (definition.columnsMap[key]) {
            continue
        }
        definition.columnsMap[key] = value
        definition.columnIds.push(key)
    }
    arrayDeDuplicate(definition.columnIds)

    const update: Partial<Database> = {
        definition,
        isMessagingEnabled: true
    }
    const service = new DatabaseService()
    await service.update({id: databaseId}, update)

    broadcastDatabaseDefinitionUpdated(workspaceId, databaseId, definition, {isMessagingEnabled: true})
    return true
}

export interface SaveMessagingBouncedUnsubscribedRequestData {
    recordIds: string[]
    data: {
        bounced?: boolean
        bouncedReason?: string
        unsubscribed?: boolean
    }
}

export const SaveMessagingBouncedUnsubscribed = async (databaseId: string, requestData: SaveMessagingBouncedUnsubscribedRequestData) => {
    if (!requestData.recordIds || !Array.isArray(requestData.recordIds) || requestData.recordIds.length === 0) {
        throw new RequiredParameterError("recordIds")
    }
    if (requestData.data.bounced && !requestData.data.bouncedReason) {
        throw new RequiredParameterError("bouncedReason")
    }
    if (requestData.data.unsubscribed === undefined && requestData.data.bounced === undefined) {
        throw new RequiredParameterError("unsubscribed|bounced")
    }
    const s = new DatabaseService()
    const database = await s.findById(databaseId)
    if (!database) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const data: UpdateRecordRequestData = {
        ids: requestData.recordIds,
        values: {
            bounced: requestData.data.bounced,
            bouncedReason: requestData.data.bouncedReason,
            unsubscribed: requestData.data.unsubscribed
        }
    }
    await updateRecordValuesNoAuth(SystemParams.UserId, database, data)
}

export const UploadRecordImage = async (userId: string, workspaceId: string, databaseId: string, recordId: string, request: Request, type: 'cover' | 'profile') => {
    if (type !== 'cover' && type !== 'profile') {
        throw new BadRequestError("Invalid type")
    }
    const resolve = await resolveDatabaseAccessLevel(userId, workspaceId, databaseId)
    if (!resolve || (![AccessLevel.Full, AccessLevel.Edit].includes(resolve.accessLevel))) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const {database} = resolve
    const recordService = new RecordService();
    const record = await recordService.findOne({id: recordId, databaseId: databaseId});
    if (!record) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    const id = generateUUID();
    const data = await ProcessFormToSpaceUpload(request, `records/${record.databaseId}/${recordId}/${type}`, id, FileType.Image);

    const meta = record.meta || {};
    if (type === 'cover') {
        meta.coverImage = data.location;
    } else if (type === 'profile') {
        meta.profileImage = data.location;
    }
    const updateData: UpdateRecordRequestData = {
        ids: [recordId],
        values: {},
        meta
    }
    return await updateRecordValuesNoAuth(userId, database, updateData)

    // return {imageUrl: data.location};
}
