import {
    InvalidParameterError,
    MessageBrokerError,
    NotfoundError,
    RequiredParameterError,
    UnauthorizedError,
    UniqueConstraintError,
} from './AppError'

interface IHttpError {
    statusCode: number
    title: string
    stack: string | undefined
    errorMessage: string
}

const makeHttpError = (error: IHttpError) => {
    const {title, errorMessage, stack, statusCode} = error
    const toReturn = {
        errors: [
            {
                title,
                error: errorMessage,
                stack,
            },
        ],
    }

    return {
        headers: {
            'Content-Type': 'application/json',
        },
        statusCode,
        data: JSON.stringify(toReturn),
    }
}
export default async function withErrorHandling<T>(
    ops: () => Promise<T>
): Promise<T> {
    try {
        return await ops()
    } catch (e) {
        switch (true) {
            case e instanceof RequiredParameterError:
                return Promise.reject(new RequiredParameterError(e.message))
            case e instanceof InvalidParameterError:
                return Promise.reject(new InvalidParameterError(e.message))
            case e instanceof MessageBrokerError:
                return Promise.reject(new MessageBrokerError(e.message))
            case e instanceof UniqueConstraintError:
                return Promise.reject(new UniqueConstraintError(e.message))
            case e instanceof NotfoundError:
                return Promise.reject(new NotfoundError(e.message))
            case e instanceof UnauthorizedError:
                return Promise.reject(new UnauthorizedError(e.message))
            default:
                return Promise.reject('An unknown error occured.')
        }
    }
}

export const wrapAsync = <T>(fn: () => Promise<T>) =>
    fn().catch((error) => {
        return makeHttpError({
            errorMessage: error.message,
            title: error.name,
            stack: error.stack,
            statusCode: error.statusCode,
        })
    })
