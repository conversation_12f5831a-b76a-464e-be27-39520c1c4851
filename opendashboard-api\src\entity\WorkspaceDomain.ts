import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"
import {RecordType} from "../utility/dns";

export enum DomainConfigType {
    DKIM = 'DKIM',
    SPF = 'SPF',
    DMARC = 'DMARC',
}

export interface DomainConfig {
    type: DomainConfigType
    recordType: RecordType
    host: string
    value: string
}

@Entity()
@Index(["workspaceId", "domainId"], {unique: true})
export class WorkspaceDomain {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Column({type: 'varchar', nullable: false})
    addedByUserId: string

    @Column({type: 'int'})
    domainId: number

    @Column({default: false, type: 'boolean'})
    isVerified: boolean

    @Column({type: 'timestamp', nullable: true})
    verifiedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

