import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from 'typeorm';
import {CampaignAttachment} from "./Campaign";
import {EmailUser} from "../businessLogic/email";
import {KeyValueStore} from "./WorkspaceMemberSettings";


export enum CampaignEmailStatus {
    InReview = "in_review",
    Queued = "queued",
    Sending = "sending",
    Failed = "failed",
    Sent = "sent",
    Skipped = "skipped",
}

@Entity()
export class CampaignEmail {

    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({type: 'varchar', nullable: false})
    campaignId: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    targetRecordId: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    recordId: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    status: CampaignEmailStatus;

    @Column({type: 'varchar', nullable: true})
    skippedReason: string;

    @Column({type: 'json', nullable: true})
    emailTo: EmailUser;

    @Column({type: 'json', nullable: true})
    emailFrom: EmailUser;

    @Column({type: 'json', nullable: true})
    cc: string[];

    @Column({type: 'json', nullable: true})
    bcc: string[];

    @Column({type: 'json', nullable: true})
    attachments: CampaignAttachment[];

    @Column({type: 'text', nullable: true})
    subject: string;

    @Column({type: 'text', nullable: true})
    contentText: string;

    @Index()
    @Column({type: 'varchar', nullable: true})
    messageId: string;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    sentAt: Date;

    @Index()
    @Column({type: 'bool', default: false})
    isSent: boolean;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    bouncedAt: Date;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    deliveredAt: Date;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    spamReportedAt: Date;

    @Index()
    @Column({type: 'bool', default: false})
    isSkipped: boolean;

    @Index()
    @Column({type: 'bool', default: false})
    isBounced: boolean;

    @Index()
    @Column({type: 'bool', default: false})
    isUnsubscribed: boolean;

    @Index()
    @Column({type: 'bool', default: false})
    isDelivered: boolean;

    @Index()
    @Column({type: 'bool', default: false})
    isSpamReported: boolean;

    @Index()
    @Column({type: 'timestamp', nullable: true})
    unsubscribedAt: Date;

    @Column({type: 'text', nullable: true})
    unsubscribeReason: string;

    @Column({type: 'text', nullable: true})
    bounceReason: string;

    @Column({type: 'text', nullable: true, select: false})
    deliveryException: string;

    @Index()
    @Column({type: 'int', default: 0})
    sendCount: number;

    @Index()
    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @Index()
    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

    @Column({type: 'json', nullable: true})
    auditLog: string[]

    @Column({type: "json", nullable: true})
    meta: KeyValueStore


}
