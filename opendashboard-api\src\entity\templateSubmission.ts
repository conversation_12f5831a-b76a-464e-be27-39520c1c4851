import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";


export enum TemplateSubmissionResult {
    Approved = "approved",
    Rejected = "rejected"
}

@Entity()
export class TemplateSubmission {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    creatorId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    templateId: string

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    releaseId: number

    @Index()
    @Column({type: 'int', default: 0, unsigned: true})
    listingId: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    reviewedBy: string

    @Column({type: 'varchar', nullable: true})
    reviewResult: TemplateSubmissionResult

    @Column({type: 'text', nullable: true})
    reviewNote: string

    @Column({type: 'text', nullable: true})
    reviewInternalNote: string

    @Column({type: 'timestamp', nullable: true})
    reviewedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

