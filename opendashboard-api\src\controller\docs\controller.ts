import {NextFunction, Request, Response} from "express"
import {getTemplateContent} from "../../utility/template";
import * as ejs from "ejs";
import {apiUrl} from "../../config";

export class DocController {

    async serveIndex(request: Request, response: Response, next: NextFunction) {
        return response.send('Huh?..')
    }

    async serveBackend(request: Request, response: Response, next: NextFunction) {
        const template = 'docs/v1.ejs'
        const templateHtml = getTemplateContent(template);

        const data = {
            docYamlUrl: apiUrl(`/openapi/backend.json`),
        };

        const content = ejs.render(templateHtml, data)
        const contentType = 'text/html'

        response.writeHead(200, {'Content-Type': contentType});
        response.end(content, 'utf-8');
    }

    async servePublic_V0(request: Request, response: Response, next: NextFunction) {
        const template = 'docs/v1.ejs'
        const templateHtml = getTemplateContent(template);

        const data = {
            docYamlUrl: apiUrl(`/openapi/publicApi.v0.json`),
        };

        const content = ejs.render(templateHtml, data)
        const contentType = 'text/html'

        response.writeHead(200, {'Content-Type': contentType});
        response.end(content, 'utf-8');
    }


}