import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"


@Entity()
@Index(["workspaceId", "email"], {unique: true})
export class WorkspaceSenderEmail {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Column({type: 'varchar', nullable: false})
    addedByUserId: string

    @Column({type: 'varchar', nullable: false})
    email: string

    @Column({type: 'varchar', nullable: false})
    name: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceDomainId: string

    @Column({default: false, type: 'boolean'})
    isVerified: boolean

    @Column({type: 'timestamp', nullable: true})
    verifiedAt: Date

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

