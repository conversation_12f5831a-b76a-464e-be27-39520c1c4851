import {NextFunction, Request, Response} from "express"
import {ApiMessage, ApiResponseBody, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {CreateAPIKey, GetAPIKeys, GetProfile, GetSessions, GetSettings, PingSession, PromptEmailVerification, PushEvent, RegenerateAPIKey, RevokeAPIKey, RevokeSession, UpdateAPIKey, UpdateProfile, UpdateProfilePhoto, UpdateSettings} from "../../businessLogic/account";
import {User} from "../../entity/User";
import {UserService} from "../../service/user";
import {Token} from "../../entity/Token";
import {DeRegisterPushToken, RegisterPushToken} from "../../businessLogic/notification";
import {PushProvider} from "../../entity/pushRegistration";

export class AccountController {

    async getProfile(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const user: User = await GetProfile(authInfo.userId)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                user
            },
        }
        return response.json(
            responseData
        )
    }

    async updateProfile(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const user: User = await UpdateProfile(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                user
            },
        }
        return response.json(
            responseData
        )
    }

    async updateProfilePhoto(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const user = await UpdateProfilePhoto(authInfo.userId, request)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                user
            }
        }
        return response.json(
            responseData
        )
    }

    async getSettings(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const settings = await GetSettings(authInfo.userId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                settings
            }
        }
        return response.json(
            responseData
        )
    }

    async updateSettings(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await UpdateSettings(authInfo.userId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }

    async getSessions(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const sessions = await GetSessions(authInfo.userId)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                sessions
            }
        }
        return response.json(
            responseData
        )
    }

    async pingSession(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await PingSession(authInfo.userId, authInfo.apiToken.id, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }

    async pushEvent(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await PushEvent(authInfo.userId, request.body)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }

    async revokeSession(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await RevokeSession(authInfo.userId, request.body.id)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }

    async promptEmailVerification(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const user = await (new UserService()).findById(authInfo.userId)
        await PromptEmailVerification(user)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: undefined,
        }
        return response.json(
            responseData
        )
    }

    async getAPIKeys(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const apiKeys = await GetAPIKeys(authInfo.userId)
        for (const apiKey of apiKeys) {
            apiKey.token = "********************************"
        }

        const responseData: ApiResponseBody<{ apiKeys: Token[] }> = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                apiKeys
            }
        }
        return response.json(
            responseData
        )
    }

    async createAPIKey(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const apiKey = await CreateAPIKey(authInfo.userId, request.body.name)

        const responseData: ApiResponseBody<{ apiKey: Token }> = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.NoMessage,
            data: {
                apiKey
            }
        }
        return response.json(
            responseData
        )
    }

    async updateAPIKey(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const id: number = Number(request.params.id)

        await UpdateAPIKey(authInfo.userId, id, request.body.name)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: undefined
        }
        return response.json(
            responseData
        )
    }

    async revokeAPIKey(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const id: number = Number(request.params.id)

        await RevokeAPIKey(authInfo.userId, id)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: undefined
        }
        return response.json(
            responseData
        )
    }

    async regenerateAPIKey(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        const id: number = Number(request.params.id)

        const apiKey = await RegenerateAPIKey(authInfo.userId, id)

        const responseData: ApiResponseBody<{ apiKey: Token }> = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {apiKey}
        }
        return response.json(
            responseData
        )
    }

    async registerPushToken(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await RegisterPushToken(authInfo.userId, String(authInfo.apiToken.id), PushProvider.FCM, request.body.token)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }

    async deRegisterPushToken(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)
        await DeRegisterPushToken(authInfo.userId, String(authInfo.apiToken.id), PushProvider.FCM)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }


}