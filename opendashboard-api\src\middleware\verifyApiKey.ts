import {NextFunction, Request, Response} from "express";
import {HttpStatusCode} from "../errors/AppError";
import {TokenService} from "../service/token";
import {TokenUsageService} from "../service/tokenUsage";
import {Token, TokenType} from "../entity/Token";

export const verifyApiKey = async (request: Request, response: Response, next: NextFunction) => {
    let apiKey: string = <string>(request.headers.apikey || request.query.apiKey || request.body.apiKey || "")
    if (!apiKey) {
        return response.status(HttpStatusCode.UNAUTHORIZED).json({
            status: 'error',
            error: "ApiKey is required"
        })
    }
    const tS = new TokenService()

    let token: Token
    try {
        token = await tS.findOne({
            token: apiKey, purpose: TokenType.ApiToken
        });
    } catch (e) {
        console.log(e)
    }
    if (!token) {
        return response.status(HttpStatusCode.UNAUTHORIZED).json({
            status: 'error',
            error: "A<PERSON><PERSON><PERSON> is invalid"
        })
    }

    const tUS = new TokenUsageService()
    await tUS.logKeyUsage(request, token.id)
    await tS.update({id: token.id}, {lastActiveAt: new Date()})
    request['userId'] = token.userId

    next()
}



