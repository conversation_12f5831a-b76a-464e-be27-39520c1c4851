openapi: 3.1.0
info:
  version: '1.0'
  title: Opendashboard-public-api
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    url: opendashboard.co
    email: <EMAIL>
servers:
  - url: 'https://api.opendashboard/v0'
paths:
  /workspaces:
    get:
      summary: Get Workspaces
      operationId: get-workspaces
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      workspaces:
                        type: array
                        items:
                          $ref: '#/components/schemas/MyWorkspace'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
  '/workspaces/{id}':
    get:
      summary: Get Workspace
      operationId: get-id-workspace
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      workspace:
                        $ref: '#/components/schemas/MyWorkspace'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
  '/workspaces/{id}/members':
    get:
      summary: Get Workspace Members
      operationId: get-id-workspace-members
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      members:
                        type: array
                        items:
                          $ref: '#/components/schemas/MyWorkspaceMember'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
  '/workspaces/{id}/databases':
    get:
      summary: Get Workspace Databases
      operationId: get-workspaces-id-databases
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      databases:
                        type: array
                        items:
                          $ref: '#/components/schemas/Database'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
  '/workspaces/{id}/databases/{databaseId}':
    get:
      summary: Get Workspace Database
      operationId: get-workspaces-id-databases-databaseId
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: databaseId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      database:
                        $ref: '#/components/schemas/Database'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
  '/workspaces/{id}/databases/{databaseId}/create-select-records':
    post:
      summary: Create Select Options
      description: Create Select Options
      operationId: post-workspaces-id-databases-databaseId-create-select-records
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: databaseId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                options:
                  type: array
                  items:
                    type: object
                    properties:
                      columnId:
                        type: string
                      options:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            title:
                              type: string
                            color:
                              type: string
              x-examples:
                Example 1:
                  options:
                    - columnId: string
                      options:
                        - id: string
                          title: string
                          color: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      database:
                        $ref: '#/components/schemas/Database'
      security:
        - {}
      servers:
        - url: 'https://api.opendashboard/v0'
  '/workspaces/{id}/databases/{databaseId}/records':
    patch:
      summary: Update Record
      operationId: patch-workspaces-id-databases-databaseId-records
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: databaseId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        description: heteidpvw1m5x
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: string
                values:
                  type: object
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
    post:
      summary: Create Record
      operationId: post-workspaces-id-databases-databaseId-records
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
        - name: databaseId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                valuesList:
                  type: array
                  items:
                    type: object
                onDuplicate:
                  type: string
                  description: "enum OnDuplicateAction {\n\tIgnore = \"ignore\",\n\tUpdate = \"update\",\n\tReject = \"reject\",\n}"
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - Query: []
      servers:
        - url: 'https://api.opendashboard/v0'
components:
  securitySchemes:
    Query:
      type: apiKey
      name: apiKey
      in: query
    Header:
      type: apiKey
      name: apikey
      in: header
    Post body:
      type: apiKey
      description: As Post body
      name: apiKey
      in: query
  responses:
    DefaultResponse:
      description: Example response
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
              message:
                type: string
              data:
                type: object
  schemas:
    MyWorkspace:
      type: object
      title: MyWorkspace
      properties:
        workspace:
          $ref: '#/components/schemas/Workspace'
        workspaceMember:
          $ref: '#/components/schemas/WorkspaceMember'
        membersCount:
          type: string
    Workspace:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        domain:
          type: string
        isSetupCompleted:
          type: boolean
        logo:
          type: string
        status:
          type: string
        createdById:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          type: string
      x-examples:
        Example 1:
          id: string
          name: string
          domain: string
          isSetupCompleted: true
          logo: string
          status: Active - 1
          createdById: string
          createdAt: '2019-08-24T14:15:22Z'
          updatedAt: '2019-08-24T14:15:22Z'
          deletedAt: '2019-08-24T14:15:22Z'
    WorkspaceMember:
      type: object
      properties:
        id:
          type: integer
        workspaceId:
          type: string
        userId:
          type: string
        role:
          type: string
        createdById:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          type: string
      x-examples:
        Example 1:
          id: 0
          workspaceId: string
          userId: string
          role: string
          createdById: string
          createdAt: '2019-08-24T14:15:22Z'
          updatedAt: '2019-08-24T14:15:22Z'
          deletedAt: '2019-08-24T14:15:22Z'
    MyWorkspaceMember:
      type: object
      title: MyWorkspaceMember
      properties:
        workspaceMember:
          $ref: '#/components/schemas/WorkspaceMember'
        user:
          $ref: '#/components/schemas/User'
    User:
      type: object
      properties:
        id:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        profilePhoto:
          type: string
        isEmailVerified:
          type: boolean
        isPreRegistered:
          type: boolean
        activeWorkspaceId:
          type: string
        referredById:
          type: string
        status:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          type: string
      x-examples:
        Example 1:
          id: string
          firstName: string
          lastName: string
          email: string
          profilePhoto: string
          isEmailVerified: true
          isPreRegistered: true
          activeWorkspaceId: string
          referredById: string
          status: Active - 1
          createdAt: '2019-08-24T14:15:22Z'
          updatedAt: '2019-08-24T14:15:22Z'
          deletedAt: '2019-08-24T14:15:22Z'
    Database:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        definition:
          type: object
        workspaceId:
          type: string
        visibility:
          type: string
        accessLevel:
          type: string
        ownerId:
          type: string
        createdById:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          type: string
      x-examples:
        Example 1:
          id: string
          name: string
          description: string
          definition: {}
          workspaceId: string
          visibility: string
          accessLevel: string
          ownerId: string
          createdById: string
          createdAt: '2019-08-24T14:15:22Z'
          updatedAt: '2019-08-24T14:15:22Z'
          deletedAt: '2019-08-24T14:15:22Z'
security:
  - {}
