import {Column, CreateDate<PERSON>olumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {KeyValueStore} from "./WorkspaceMemberSettings";
import {IntegrationStoreScope} from "@opendashboard-inc/integration-core/dist";


@Entity()
@Index(["workspaceId", "workflowId", "integration", "scope", "key"], {unique: true})
export class WorkspaceIntegrationStore {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: true, length: '50'})
    workspaceId: string

    @Column({type: 'varchar', nullable: true, length: '50'})
    workflowId: string

    @Column({type: 'varchar', nullable: false, length: '50'})
    integration: string

    @Column({type: 'varchar', nullable: false})
    key: string

    @Column({type: 'varchar', nullable: false, length: '50'})
    scope: IntegrationStoreScope

    @Column({type: 'varchar', nullable: true})
    value: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

}
