import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"

export enum CreatorMemberRole {
    Tester = "tester",
    Owner = "owner",
    Admin = "admin",
    Member = "member",
    SupportUser = "supportUser",
}


@Entity()
@Index(["creatorId", "userId"], {unique: true})
export class CreatorMember {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    creatorId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Column({type: 'varchar', nullable: false})
    role: CreatorMemberRole

    @Column({type: 'varchar', nullable: false})
    invitedById: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

