import {UserService} from "../service/user";
import {User} from "../entity/User";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, ServerProcessingError} from "../errors/AppError";
import {validateEmail} from "../utility/validator";
import {FileType, ProcessFormToSpaceUpload} from "./doUpload";
import {SettingsService} from "../service/setting";
import {NotificationSettings, Settings, SettingsType} from "../entity/Settings";
import {TokenExpiryUnit, TokenService} from "../service/token";
import {Token, TokenType} from "../entity/Token";
import {EmailUser, SendEmailWithContent} from "./email";
import {apiUrl} from "../config";
import {datePlusDays} from "opendb-app-db-utils/lib/methods/date";
import {EventType} from "../entity/UserHourlyEvent";
import {UserHourlyEventService} from "../service/userHourlyEvent";
import {TriggerUserActivatedWorkflow} from "./providers/inbranded";
import {PushNewUserToDatabase} from "./providers/opendashboard";
import {Request} from "express"
import {NotificationService} from "../service/notification";


export const GetProfile = async (userId: string) => {
    const service = new UserService();
    return await service.findById(userId)
}

export interface UpdateProfileData extends Pick<User, 'firstName' | 'lastName' | 'email'> {
}

export const UpdateProfile = async (userId: string, data: UpdateProfileData) => {
    let {email, firstName, lastName} = data

    email = (email || '').trim()
    firstName = (firstName || '').trim()
    lastName = (lastName || '').trim()

    if (!email) {
        throw new RequiredParameterError('Email')
    }
    if (!firstName) {
        throw new RequiredParameterError('First Name')
    }
    if (!lastName) {
        throw new RequiredParameterError('Last Name')
    }
    if (!validateEmail(email)) {
        throw new InvalidParameterError(`'${email}' is not a valid email address`)
    }
    const service = new UserService();

    let user = await service.findById(userId);
    if (!user) throw new ServerProcessingError(ErrorMessage.UnableToProcessRequest)

    const oldEmail = user.email
    const isEmailVerified = !user.isSetupCompleted || (data.email === oldEmail && user.isEmailVerified)

    const isSetupCompleted = user.isSetupCompleted

    const update = {
        email, firstName, lastName, isEmailVerified, isSetupCompleted: true
    }
    user = {...user, ...update}
    await service.update({id: user.id}, user)

    if (data.email !== oldEmail) {
        const logMessage = `Email changed from ${oldEmail} to ${data.email}`
        await LogUserMessage(user.id, logMessage)

        await PromptEmailVerification(user)
    }
    if (!isSetupCompleted && user.isSetupCompleted) {
        await TriggerUserActivatedWorkflow(user)
        await PushNewUserToDatabase([user])
    }
    return user
}

const LogUserMessage = (id: string, message: string) => {
    const service = new UserService()
    return service.appendLog(id, message)
}

export const UpdateProfilePhoto = async (userId: string, request: Request): Promise<User> => {
    const service = new UserService();

    const user = await GetProfile(userId);
    const date = new Date(user.createdAt)

    const month = date.getMonth() + 1
    const year = date.getFullYear()
    const day = date.getDate()

    const result = await ProcessFormToSpaceUpload(request, `users/${year}/${month}/${day}/${userId}`, 'photo.png', FileType.Image)

    const update = {
        profilePhoto: `${result.location}?t=${new Date().getTime()}`
    }
    await service.update({id: userId}, update)

    return {
        ...user,
        ...update
    }
}

export const GetSettings = async (userId: string) => {
    const service = new SettingsService();
    return (await service.find({userId}))[0]
}

export interface UpdateSettingsData {
    type: SettingsType
    settings: NotificationSettings
}

export const UpdateSettings = async (userId: string, data: UpdateSettingsData) => {
    const {type, settings} = data

    if (![SettingsType.Notification].includes(type)) {
        throw new InvalidParameterError("type")
    }
    const update: Partial<Settings> = {}
    update[type] = settings

    const service = new SettingsService();
    await service.update({userId}, update)
    return true
}

export const GetSessions = async (userId: string) => {
    const service = new TokenService()
    return service.findTokens(userId, TokenType.JWT)
}

export const RevokeSession = async (userId: string, id: string) => {
    const service = new TokenService()
    await service.remove({userId, id: Number(id)})

    return true
}

export interface PingSessionRequestBody {
    name: string,
    client: object
}

export const PingSession = async (userId: string, id: number, data: PingSessionRequestBody) => {
    const {client, name} = data
    if (!client) {
        throw new RequiredParameterError("clientMeta")
    }
    if (!name) {
        throw new RequiredParameterError("name")
    }
    const service = new TokenService()
    const token = await service.findOne({
        id: id,
        userId: userId,
        purpose: TokenType.JWT
    })
    if (!token) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const meta = token.meta || {}
    meta['client'] = client

    // keep extending by 7 days
    const update: Partial<Token> = {
        meta,
        lastActiveAt: new Date(),
        clientName: name,
        expiresAt: datePlusDays(new Date(), 7)
    }
    await service.update({id}, update)
    return true
}

export interface PushEventRequestBody {
    workspaceId: string,
    pageId?: string,
    databaseId?: string,
    viewId?: string,
    event: EventType
}

export const PushEvent = async (userId: string, data: PushEventRequestBody) => {
    if (!data.event) {
        throw new RequiredParameterError("event")
    }
    if (!data.workspaceId) {
        throw new RequiredParameterError("workspaceId")
    }
    const uHS = new UserHourlyEventService()
    await uHS.logEvent({...data, userId})
    return true
}


export const PromptEmailVerification = async (user: User) => {
    const service = new TokenService();
    const token: Token = await service.createToken(user.id, TokenType.EmailVerification, {
        value: 30,
        unit: TokenExpiryUnit.Days
    })
    const verifyHash = `${user.id}.${encodeURIComponent(token.token)}`;
    const emailVerifyLink = apiUrl(`/api/v1/auth/verify-email?hash=${verifyHash}`);

    const to: EmailUser = {
        email: user.email,
        name: `${user.firstName} ${user.lastName}`.trim()
    }
    const subject = 'Verify your email - Opendashboard'
    const body = `
    <p style="font-size: 24px; font-weight: 600;">You updated your email in Opendashboard</p>
    <p style="color:#313539;">Click on the button below to verify your updated email in Opendashboard.</p>
    `
    const button = {
        label: 'Verify Email',
        url: emailVerifyLink
    }
    const messageId = await SendEmailWithContent(to, subject, body, button)

    return {messageId, token, verifyHash}
}

export const CompleteEmailVerification = async (encodedHash: string) => {
    const service = new TokenService();
    const userService = new UserService();

    const hash = decodeURIComponent(encodedHash)
    const [userId, tkn] = hash.split('.')

    const token: Token = await service.verifyToken(userId, tkn, TokenType.EmailVerification)
    if (!token) {
        throw new BadRequestError(`The token provided is invalid or expired`)
    }
    await userService.update({id: userId}, {isEmailVerified: true})
    await service.deleteToken(userId, tkn, TokenType.EmailVerification)

    const logMessage = `Email verification completed`
    await LogUserMessage(userId, logMessage)

    return true
}

export const GetAPIKeys = async (userId: string) => {
    const service = new TokenService();
    return service.findTokens(userId, TokenType.ApiToken)
}

export const RevokeAPIKey = async (userId: string, tokenId: number) => {
    const service = new TokenService();
    await service.remove({id: tokenId, userId: userId})

    return true
}

export const RegenerateAPIKey = async (userId: string, tokenId: number) => {
    const service = new TokenService();
    const token = await service.findOne({userId, id: tokenId})
    if (!token) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    await service.regenerateToken(token)

    return token
}

export const UpdateAPIKey = async (userId: string, tokenId: number, name: string) => {
    const service = new TokenService();
    const token = await service.findOne({userId, id: tokenId})
    if (!token) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    await service.update({id: tokenId}, {name: name})

    return true
}

export const CreateAPIKey = async (userId: string, name?: string) => {
    const service = new TokenService();
    return await service.createToken(userId, TokenType.ApiToken, {
        unit: TokenExpiryUnit.Days,
        value: 365
    }, name)
}

export const VerifyAPIToken = async (userId: string, tkn: string, type: TokenType) => {
    const service = new TokenService();
    return service.verifyToken(userId, tkn, type)
}

