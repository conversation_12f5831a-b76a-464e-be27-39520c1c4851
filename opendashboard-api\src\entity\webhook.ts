import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {WorkflowMeta} from "opendb-app-db-utils/lib/typings/workflow";

@Entity()
export class Webhook {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Index()
    @Column({type: "varchar"})
    workflowId: string

    @Index()
    @Column({type: "varchar"})
    workspaceId: string

    @Index()
    @Column({type: "varchar"})
    method: string

    @Column({type: 'json', nullable: true})
    headers: object

    @Column({type: 'json', nullable: true})
    body: object

    @Column({type: 'json', nullable: true})
    query: object

    @Index()
    @Column({type: 'boolean', default: false})
    isTest: boolean

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'json', nullable: true})
    meta: WorkflowMeta

}