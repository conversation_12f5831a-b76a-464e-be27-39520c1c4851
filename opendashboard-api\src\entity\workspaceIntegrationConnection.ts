import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm";
import {KeyValueStore} from "./WorkspaceMemberSettings";


export interface ConnectionCredentials {
    [key: string]: string;
}

@Entity()
@Index(["workspaceId", "integration"])
export class WorkspaceIntegrationConnection {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Column({type: 'varchar', nullable: false})
    integration: string

    @Column({type: 'varchar', nullable: false})
    name: string

    @Column({type: "json", nullable: true, select: false})
    credentials: ConnectionCredentials

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

}
