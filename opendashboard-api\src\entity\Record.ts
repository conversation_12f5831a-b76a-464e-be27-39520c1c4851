import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"
import {RecordValues} from "opendb-app-db-utils/lib/typings/db";

export interface RecordMetadata {
    coverImage?: string
    profileImage?: string

    [key: string]: any
}

@Entity()
@Index(["databaseId", "uniqueValue"], {unique: true})
export class Record {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    databaseId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    updatedById: string

    @Column({type: 'json', nullable: true})
    recordValues: RecordValues

    @Column({type: 'varchar', nullable: true})
    uniqueValue: string;

    @Column({type: 'json', nullable: true})
    summaryJSON: object

    @Index({fulltext: true})
    @Column({type: 'text', nullable: true})
    summaryText: string

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateReleaseId: number

    @Index()
    @Column({type: 'integer', nullable: true, select: false})
    templateInstallId: number

    @Column({type: 'varchar', nullable: true})
    title: string;

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: "json", nullable: true})
    meta: RecordMetadata

}




