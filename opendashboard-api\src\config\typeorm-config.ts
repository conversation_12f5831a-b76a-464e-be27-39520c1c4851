import config from "./index";
import {DataSourceOptions} from "typeorm/data-source/DataSourceOptions";


const dataSourceOption: DataSourceOptions = {
    type: 'mysql',
    synchronize: false,
    logging: ["error"],
    // timezone: 'America/Toronto',
    replication: {
        master: {
            host: config.DATABASE.host,
            password: config.DATABASE.password,
            port: Number(config.DATABASE.port),
            username: config.DATABASE.username,
            database: config.DATABASE.database,
        },
        slaves: [
            {
                host: config.DATABASE.slave_host,
                password: config.DATABASE.password,
                port: Number(config.DATABASE.port),
                username: config.DATABASE.username,
                database: config.DATABASE.database,
            }
        ],
        restoreNodeTimeout: 1000,
        removeNodeErrorCount: Number.MAX_SAFE_INTEGER,
    },
    entities: [
        `${__dirname}/../entity/*.{ts,js}`
    ],
    migrations: [
        `${__dirname}/../migration/*.{ts,js}`
    ]
};

export = dataSourceOption