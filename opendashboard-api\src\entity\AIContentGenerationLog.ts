import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"


enum ContentRating {
    Unrated = 0,
    Good = 1,
    Bad = 2,
}

@Entity()
export class AIContentGenerationLog {

    @PrimaryGeneratedColumn("increment")
    id: number

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    provider: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    providerReference: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    modelName: string

    @Column({type: "text", nullable: true})
    systemPrompt: string

    @Column({type: "longtext", nullable: true})
    userPrompt: string

    @Column({type: "text", nullable: true})
    contentGenerated: string

    @Column({type: "text", nullable: true})
    providerResponse: string

    @Index()
    @Column({type: "int", default: 0})
    inputTokens: number

    @Index()
    @Column({type: "int", default: 0})
    outputTokens: number

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    estimatedCostInCents: number

    @Index()
    @Column({type: "tinyint", default: 0, unsigned: true})
    rating: ContentRating

    @Index()
    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @Index()
    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: "json", nullable: true})
    meta: object
}