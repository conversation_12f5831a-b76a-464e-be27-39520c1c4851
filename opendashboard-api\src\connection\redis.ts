import {createClient} from "redis"
import {consoleLog} from "../businessLogic/logtail";

export const redisClient = createClient({
    // url: 'redis://alice:<EMAIL>:6380'
});

export const redisConnect = async () => {
    return redisClient.connect();
}

export const redisDisconnect = async () => {
    return redisClient.disconnect();
}

redisClient.on('error', err => console.log('Redis Client Error', err));
redisClient.on('ready', () => console.log('Redis Client Ready'));

export const redisSet = async (key: string, value: string, expirySeconds?: number) => {
    if (expirySeconds && expirySeconds > 0) {
        return await redisClient.set(key, value, {EX: expirySeconds});
    } else {
        return await redisClient.set(key, value);
    }
}

export const redisGet = async (key: string): Promise<string> => {
    return await redisClient.get(key);
}

export const redisDelete = async (key: string) => {
    return await redisClient.del(key);
}

export const redisKey = (objectType: string, objectId: string) => {
    return `${objectType}:${objectId}`;
}

/**
 * Acquires a distributed lock.
 * @param lockKey - The Redis key used for the lock.
 * @param ttl - Time-to-live for the lock in seconds (default: 60).
 * @returns A promise that resolves to `true` if the lock is acquired, `false` otherwise.
 */
export const redisAcquireLock = async (lockKey: string, ttl: number = 60): Promise<boolean> => {
    try {
        const result = await redisClient.set(lockKey, 'locked', {NX: true, EX: ttl});
        return result === 'OK'; // If the result is 'OK', the lock is acquired
    } catch (err) {
        consoleLog(`Error acquiring lock`, {err, lockKey, ttl})
        throw new Error(`Error acquiring lock: ${err}`);
        // return false
    }
};

/**
 * Releases a distributed lock.
 * @param lockKey - The Redis key used for the lock.
 * @returns A promise that resolves to `true` if the lock is released, `false` otherwise.
 */
export const redisReleaseLock = async (lockKey: string): Promise<boolean> => {
    try {
        const result = await redisClient.del(lockKey);
        return result > 0; // If the result is greater than 0, the lock was released
    } catch (err) {
        consoleLog(`Error releasing lock`, {err, lockKey})
        throw new Error(`Error releasing lock: ${err}`);
        // return false
    }
};

// /**
//  * Processes a job with distributed locking.
//  * @param lockKey - The Redis key used for the lock.
//  * @param lockTtl - Time-to-live for the lock in seconds.
//  */
// async function processJob(lockKey: string, lockTtl: number = 60): Promise<void> {
// 	try {
// 		const lockAcquired = await redisAcquireLock(lockKey, lockTtl);
//
// 		if (!lockAcquired) {
// 			console.log('Another instance is processing the job. Skipping execution.');
// 			return;
// 		}
//
// 		console.log('Lock acquired. Executing the job...');
//
// 		// Execute your job logic here
// 		await performJob();
//
// 		console.log('Job finished.');
// 	} catch (err) {
// 		console.error('Error in job execution:', err);
// 	} finally {
// 		// Ensure the lock is released in case of errors
// 		try {
// 			await releaseLock(lockKey);
// 		} catch (releaseErr) {
// 			console.error('Error releasing lock:', releaseErr);
// 		}
// 	}
// }
//
// /**
//  * Simulates job logic.
//  */
// async function performJob(): Promise<void> {
// 	// Simulate job processing
// 	return new Promise((resolve) => setTimeout(resolve, 5000));
// }
//
// // Example usage
// const LOCK_KEY = 'cron-job-lock';
// processJob(LOCK_KEY, 60);