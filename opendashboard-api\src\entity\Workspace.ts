import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from "typeorm"
import {KeyValueStore} from "./WorkspaceMemberSettings";

@Entity()
export class Workspace {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: false})
    name: string

    @Column({type: 'varchar', nullable: false, unique: true})
    domain: string

    @Index()
    @Column({type: 'bool', default: false})
    isSetupCompleted: boolean

    @Column({type: 'varchar', nullable: true})
    logo: string

    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    affiliateId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    ownerId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    stripeCustomerId: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'varchar', nullable: true, select: false})
    orgUseCase: string

    @Column({type: 'varchar', nullable: true, select: false})
    orgType: string

    @Column({type: 'varchar', nullable: true, select: false})
    orgSize: string

    @Column({type: 'varchar', nullable: true, select: false})
    orgPhoneNumber: string

    @Column({type: 'varchar', nullable: true, select: false})
    deletionReason: string

    @Index()
    @Column({type: 'varchar', default: 'UTC'})
    timezone: string

    @Index()
    @Column({type: 'bool', default: false})
    isSupportAccessEnabled: boolean

    @Column({type: 'bool', default: false})
    isFunctionalityLimited: boolean

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

}

