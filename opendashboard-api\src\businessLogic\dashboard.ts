import {resolvePageAccessLevel} from "./page";
import {AccessLevel} from "../entity/common";
import {ErrorMessage, NotfoundError, UnauthorizedError} from "../errors/AppError";
import {ViewService} from "../service/view";
import {DashboardDefinition, DashboardTransaction, DashboardViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {View} from "../entity/View";
import {arrayAddElementAdjacent, removeAllArrayItem} from "opendb-app-db-utils/lib";
import {broadcastPageViewUpdated} from "../socketio/workspace";

export const ApplyDashboardTransactions = async (userId: string, workspaceId: string, pageId: string, viewId: string, transactions: DashboardTransaction[]) => {
    const resolve = await resolvePageAccessLevel(userId, workspaceId, pageId)
    if (!resolve || ![AccessLevel.Full, AccessLevel.Edit, AccessLevel.View].includes(resolve.accessLevel)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new ViewService()
    const view = await s.findOne({pageId, id: viewId})
    if (!view || view.type !== ViewType.Dashboard) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const definition = view.definition as DashboardViewDefinition

    definition.definition = applyTransaction(definition.definition, transactions)

    const update: Partial<View> = {
        definition,
        updatedAt: new Date()
    }

    await s.update({id: viewId, pageId}, update)

    broadcastPageViewUpdated(workspaceId, pageId, resolve.page.databaseId, viewId, update)
}

const applyTransaction = (currDefinition: DashboardDefinition, transactions: DashboardTransaction[]) => {
    const definition: DashboardDefinition = {...currDefinition}

    if (transactions.length === 0) return definition

    definition.rowsMap = definition.rowsMap || {}
    definition.elementMap = definition.elementMap || {}
    definition.children = definition.children || []

    for (const transaction of transactions) {
        const {element, row, afterId, parentId, beforeId, action} = transaction

        if (action === "updateRow") {
            if (!row) throw "Row is undefined"

            const defRow = definition.rowsMap[row.id]
            if (!defRow) throw "Invalid row"
            definition.rowsMap[row.id] = {...defRow, ...row, updatedTs: new Date().getTime()}

        } else if (action === "addRow") {
            if (!row) throw "Row is undefined"
            definition.rowsMap[row.id] = row

            if (afterId) {
                definition.children = arrayAddElementAdjacent(definition.children, afterId, row.id, 'after')
            } else if (beforeId) {
                definition.children = arrayAddElementAdjacent(definition.children, beforeId, row.id, 'before')
            } else definition.children.push(row.id)

        } else if (action === 'deleteRow') {
            if (!row) throw "Row is undefined"
            delete definition.rowsMap[row.id]
            definition.children = removeAllArrayItem(definition.children, row.id)

        } else if (action === 'addElement') {
            if (!element) throw "Element is undefined"
            if (!parentId) throw "Element parent is undefined"

            if (!definition.rowsMap[parentId]) throw "Element parent is not found"
            definition.elementMap[element.id] = element

            if (afterId) {
                definition.rowsMap[parentId].children = arrayAddElementAdjacent(definition.rowsMap[parentId].children, afterId, element.id, 'after')
            } else if (beforeId) {
                definition.rowsMap[parentId].children = arrayAddElementAdjacent(definition.rowsMap[parentId].children, beforeId, element.id, 'before')
            } else definition.rowsMap[parentId].children.push(element.id)

            definition.rowsMap[parentId].updatedTs = new Date().getTime()
        } else if (action === 'updateElement') {
            if (!element || !definition.elementMap[element.id]) throw "Element is undefined"
            definition.elementMap[element.id] = {...definition.elementMap[element.id], ...element, updatedTs: new Date().getTime()}

        } else if (action === 'deleteElement') {
            if (!element) throw "Element is undefined"
            delete definition.elementMap[element.id]

            if (!parentId) throw "Element parent is undefined"
            definition.rowsMap[parentId].children = removeAllArrayItem(definition.rowsMap[parentId].children, element.id)
            definition.rowsMap[parentId].updatedTs = new Date().getTime()

        } else throw `Invalid action ${action}`
    }
    return definition
}




