import { MigrationInterface, QueryRunner } from "typeorm";

export class WorkflowMigration21739312033357 implements MigrationInterface {
    name = 'WorkflowMigration21739312033357'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workflow\` ADD \`runsCount\` int NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`workflow\` ADD \`nextRunAt\` timestamp NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_f1d3d2fb0d5b3bca472d5f7443\` ON \`workflow\` (\`runsCount\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c0ab61006e1a87f00bd0daa0c9\` ON \`workflow\` (\`nextRunAt\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_c0ab61006e1a87f00bd0daa0c9\` ON \`workflow\``);
        await queryRunner.query(`DROP INDEX \`IDX_f1d3d2fb0d5b3bca472d5f7443\` ON \`workflow\``);
        await queryRunner.query(`ALTER TABLE \`workflow\` DROP COLUMN \`nextRunAt\``);
        await queryRunner.query(`ALTER TABLE \`workflow\` DROP COLUMN \`runsCount\``);
    }

}
