import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"
import {PaymentCurrency} from "./templatePurchase";
import {KeyValueStore} from "./WorkspaceMemberSettings";

export enum PayoutProcessor {
    Paystack = "paystack"
}


export enum PayoutStatus {
    Pending = "pending",
    OnTheWay = "on_the_way",
    Settled = "settled",
}

@Entity()
export class Payout {

    @PrimaryGeneratedColumn('increment')
    id: number

    @Index()
    @Column({type: 'varchar', nullable: true})
    creatorId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    affiliateId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    payoutProvider: PayoutProcessor

    @Index()
    @Column({type: 'varchar', nullable: true})
    payoutProviderReference: string

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    amountInCents: number

    @Index()
    @Column({type: "decimal", default: 0, precision: 12, scale: 4})
    amountInLocalCurrency: number

    @Index()
    @Column({type: "varchar", nullable: true})
    currency: PaymentCurrency

    @Index()
    @Column({type: 'varchar', nullable: true})
    payoutStatus: PayoutStatus

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @Column({type: 'json', nullable: true, select: false})
    auditLog: string[]

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

}

