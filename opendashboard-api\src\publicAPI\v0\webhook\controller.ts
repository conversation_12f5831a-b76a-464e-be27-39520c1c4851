import {NextFunction, Request, Response} from "express"
import {ApiResponseStatus, GenericApiResponseBody} from "../../../controller/interface";
import {ErrorMessage} from "../../../errors/AppError";
import {DispatchApprovalAction, HandleDispatch, HandleIntegrationDispatch} from "../../../businessLogic/webhook";
import {autoCloseBody} from "../../../utility/html";

export class WebhookController {

    async getAll(request: Request, response: Response, next: NextFunction) {
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ErrorMessage.IfYouAreTryingToUseWebhook,
            data: {},
        }
        return response.json(responseData)
    }

    async handleDispatch(request: Request, response: Response, next: NextFunction) {
        await HandleDispatch(request.params.id, request.method, request.headers, request.body, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {},
        }
        return response.json(responseData)
    }

    async handleApproval(request: Request, response: Response, next: NextFunction) {
        const data = await DispatchApprovalAction(request.params.id, request.params.runId, "approve", request.query)
        // const responseData: GenericApiResponseBody = {
        //     status: ApiResponseStatus.Ok,
        //     message: '',
        //     data: data,
        // }
        return response.send(autoCloseBody(data.message));
    }

    async rejectApproval(request: Request, response: Response, next: NextFunction) {
        const data = await DispatchApprovalAction(request.params.id, request.params.runId, "reject", request.query)
        // const responseData: GenericApiResponseBody = {
        //     status: ApiResponseStatus.Ok,
        //     message: '',
        //     data: data,
        // }
        return response.send(autoCloseBody(data.message));
    }

    // async approvalDetails(request: Request, response: Response, next: NextFunction) {
    //     const data = await DispatchApprovalAction(request.params.id, request.params.runId, "details")
    //     const responseData: GenericApiResponseBody = {
    //         status: ApiResponseStatus.Ok,
    //         message: '',
    //         data: data,
    //     }
    //     return response.json(responseData)
    // }

    async handleTestDispatch(request: Request, response: Response, next: NextFunction) {
        await HandleDispatch(request.params.id, request.method, request.headers, request.body, request.query, true)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {},
        }
        return response.json(responseData)
    }

    async handleIntegrationDispatch(request: Request, response: Response, next: NextFunction) {
        const data = await HandleIntegrationDispatch(request.params.id, request.params.integration, request.params.trigger, request.method, request.headers, request.body, request.query)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {},
        }
        return response.json(responseData)
    }

    async handleIntegrationTestDispatch(request: Request, response: Response, next: NextFunction) {
        await HandleIntegrationDispatch(request.params.id, request.params.integration, request.params.trigger, request.method, request.headers, request.body, request.query, true)
        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: '',
            data: {},
        }
        return response.json(responseData)
    }

}






