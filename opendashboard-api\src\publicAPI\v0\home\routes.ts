import {HomeController} from "./controller";
import {<PERSON><PERSON>ontroller} from "../../../controller/docs/controller";
import {IntegrationsController} from "../../../controller/integrations/controller";

export const homeRoutes = {
    basePath: '/',
    routes: {
        '/': {
            get: {
                controller: HomeController,
                action: "home"
            }
        },
        '/docs': {
            get: {
                controller: DocController,
                action: "servePublic_V0"
            }
        },
        '/welcome': {
            get: {
                controller: HomeController,
                action: "welcome",
            }
        },
        '/email': {
            get: {
                controller: HomeController,
                action: "email",
            }
        },
        '/unsubscribe': {
            get: {
                controller: HomeController,
                action: "handleUnsubscribe",
            }
        },
        '/tracking.png': {
            get: {
                controller: HomeController,
                action: "openTracking",
            }
        },
        '/referral': {
            post: {
                controller: HomeController,
                action: "referralClickTracking",
            }
        },
        '/redirect': {
            get: {
                controller: HomeController,
                action: "clickTracking",
            }
        },
        '/oauth2/callback': {
            get: {controller: IntegrationsController, action: "integrationOAuth2Callback"},
        },
        '/credit-purchases/:creditId/paystack-callback': {
            get: {
                controller: HomeController,
                action: "verifyNGNWorkspaceCreditPurchase"
            },
        },
    }
};


