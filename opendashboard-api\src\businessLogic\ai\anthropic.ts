import Anthropic from '@anthropic-ai/sdk';
import config from "../../config";
import {RequiredParameterError} from "../../errors/AppError";
import {AIModelPricing} from "./ai";

const anthropic = new Anthropic({apiKey: config.ANTHROPIC.api_key});

export const Haiku: AIModelPricing = Object.freeze({
    modelName: 'claude-3-haiku-20240307',
    costInCentsPerInputToken: 0.000025,
    costInCentsPerOutputToken: 0.000125
})


export const testClaude = async () => {
    const message = await anthropic.messages.create({
        model: 'claude-3-opus-20240229',
        max_tokens: 1024,
        temperature: 0.8,
        messages: [
            {"role": "user", "content": "Hello, <PERSON>"}
        ]
    });

    return message
}

// https://docs.anthropic.com/en/docs/build-with-claude/vision to process image with message

export const generateMessage = async (prompt: string, options: {
    model?: string,
    system?: string,
    max_tokens?: number,
    temperature?: number
}) => {
    if (!prompt || !prompt.trim()) throw new RequiredParameterError("prompt")
    const model = options.model || 'claude-3-haiku-20240307'
    const system = options.system || ''
    const max_tokens = options.max_tokens || 1024
    const temperature = options.temperature || 0.7

    return anthropic.messages.create({
        model,
        max_tokens,
        system,
        temperature,
        messages: [
            {"role": "user", "content": prompt}
        ]
    });
}