"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-qr-code";
exports.ids = ["vendor-chunks/react-qr-code"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-qr-code/lib/QRCodeSvg/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-qr-code/lib/QRCodeSvg/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _propTypes = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _react = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n\nvar propTypes = {\n  bgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]).isRequired,\n  bgD: _propTypes2.default.string.isRequired,\n  fgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]).isRequired,\n  fgD: _propTypes2.default.string.isRequired,\n  size: _propTypes2.default.number.isRequired,\n  title: _propTypes2.default.string,\n  viewBoxSize: _propTypes2.default.number.isRequired,\n  xmlns: _propTypes2.default.string\n};\n\nvar QRCodeSvg = (0, _react.forwardRef)(function (_ref, ref) {\n  var bgColor = _ref.bgColor,\n      bgD = _ref.bgD,\n      fgD = _ref.fgD,\n      fgColor = _ref.fgColor,\n      size = _ref.size,\n      title = _ref.title,\n      viewBoxSize = _ref.viewBoxSize,\n      _ref$xmlns = _ref.xmlns,\n      xmlns = _ref$xmlns === undefined ? \"http://www.w3.org/2000/svg\" : _ref$xmlns,\n      props = _objectWithoutProperties(_ref, [\"bgColor\", \"bgD\", \"fgD\", \"fgColor\", \"size\", \"title\", \"viewBoxSize\", \"xmlns\"]);\n\n  return _react2.default.createElement(\n    \"svg\",\n    _extends({}, props, { height: size, ref: ref, viewBox: \"0 0 \" + viewBoxSize + \" \" + viewBoxSize, width: size, xmlns: xmlns }),\n    title ? _react2.default.createElement(\n      \"title\",\n      null,\n      title\n    ) : null,\n    _react2.default.createElement(\"path\", { d: bgD, fill: bgColor }),\n    _react2.default.createElement(\"path\", { d: fgD, fill: fgColor })\n  );\n});\n\nQRCodeSvg.displayName = \"QRCodeSvg\";\nQRCodeSvg.propTypes = propTypes;\n\nexports[\"default\"] = QRCodeSvg;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-qr-code/lib/QRCodeSvg/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-qr-code/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/react-qr-code/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.QRCode = undefined;\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _propTypes = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _ErrorCorrectLevel = __webpack_require__(/*! qr.js/lib/ErrorCorrectLevel */ \"(ssr)/./node_modules/qr.js/lib/ErrorCorrectLevel.js\");\n\nvar _ErrorCorrectLevel2 = _interopRequireDefault(_ErrorCorrectLevel);\n\nvar _QRCode = __webpack_require__(/*! qr.js/lib/QRCode */ \"(ssr)/./node_modules/qr.js/lib/QRCode.js\");\n\nvar _QRCode2 = _interopRequireDefault(_QRCode);\n\nvar _react = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _QRCodeSvg = __webpack_require__(/*! ./QRCodeSvg */ \"(ssr)/./node_modules/react-qr-code/lib/QRCodeSvg/index.js\");\n\nvar _QRCodeSvg2 = _interopRequireDefault(_QRCodeSvg);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _objectWithoutProperties(obj, keys) { var target = {}; for (var i in obj) { if (keys.indexOf(i) >= 0) continue; if (!Object.prototype.hasOwnProperty.call(obj, i)) continue; target[i] = obj[i]; } return target; }\n// A `qr.js` doesn't handle error level of zero (M) so we need to do it right, thus the deep require.\n\n\nvar propTypes = {\n  bgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]),\n  fgColor: _propTypes2.default.oneOfType([_propTypes2.default.object, _propTypes2.default.string]),\n  level: _propTypes2.default.string,\n  size: _propTypes2.default.number,\n  value: _propTypes2.default.string.isRequired\n};\n\nvar QRCode = (0, _react.forwardRef)(function (_ref, ref) {\n  var _ref$bgColor = _ref.bgColor,\n      bgColor = _ref$bgColor === undefined ? \"#FFFFFF\" : _ref$bgColor,\n      _ref$fgColor = _ref.fgColor,\n      fgColor = _ref$fgColor === undefined ? \"#000000\" : _ref$fgColor,\n      _ref$level = _ref.level,\n      level = _ref$level === undefined ? \"L\" : _ref$level,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 256 : _ref$size,\n      value = _ref.value,\n      props = _objectWithoutProperties(_ref, [\"bgColor\", \"fgColor\", \"level\", \"size\", \"value\"]);\n\n  // Use type === -1 to automatically pick the best type.\n  var qrcode = new _QRCode2.default(-1, _ErrorCorrectLevel2.default[level]);\n  qrcode.addData(value);\n  qrcode.make();\n  var cells = qrcode.modules;\n  return _react2.default.createElement(_QRCodeSvg2.default, _extends({}, props, {\n    bgColor: bgColor,\n    bgD: cells.map(function (row, rowIndex) {\n      return row.map(function (cell, cellIndex) {\n        return !cell ? \"M \" + cellIndex + \" \" + rowIndex + \" l 1 0 0 1 -1 0 Z\" : \"\";\n      }).join(\" \");\n    }).join(\" \"),\n    fgColor: fgColor,\n    fgD: cells.map(function (row, rowIndex) {\n      return row.map(function (cell, cellIndex) {\n        return cell ? \"M \" + cellIndex + \" \" + rowIndex + \" l 1 0 0 1 -1 0 Z\" : \"\";\n      }).join(\" \");\n    }).join(\" \"),\n    ref: ref,\n    size: size,\n    viewBoxSize: cells.length\n  }));\n});\n\nexports.QRCode = QRCode;\nQRCode.displayName = \"QRCode\";\nQRCode.propTypes = propTypes;\n\nexports[\"default\"] = QRCode;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXItY29kZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsY0FBYzs7QUFFZCxvREFBb0QsZ0JBQWdCLHNCQUFzQixPQUFPLDJCQUEyQiwwQkFBMEIseURBQXlELGlDQUFpQzs7QUFFaFAsaUJBQWlCLG1CQUFPLENBQUMsNERBQVk7O0FBRXJDOztBQUVBLHlCQUF5QixtQkFBTyxDQUFDLHdGQUE2Qjs7QUFFOUQ7O0FBRUEsY0FBYyxtQkFBTyxDQUFDLGtFQUFrQjs7QUFFeEM7O0FBRUEsYUFBYSxtQkFBTyxDQUFDLHdHQUFPOztBQUU1Qjs7QUFFQSxpQkFBaUIsbUJBQU8sQ0FBQyw4RUFBYTs7QUFFdEM7O0FBRUEsdUNBQXVDLHVDQUF1Qzs7QUFFOUUsK0NBQStDLGlCQUFpQixxQkFBcUIsb0NBQW9DLDZEQUE2RCxzQkFBc0I7QUFDNU07OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVFQUF1RTtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxDQUFDOztBQUVELGNBQWM7QUFDZDtBQUNBOztBQUVBLGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1xci1jb2RlL2xpYi9pbmRleC5qcz84NGM0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5RUkNvZGUgPSB1bmRlZmluZWQ7XG5cbnZhciBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gKHRhcmdldCkgeyBmb3IgKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKykgeyB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldOyBmb3IgKHZhciBrZXkgaW4gc291cmNlKSB7IGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoc291cmNlLCBrZXkpKSB7IHRhcmdldFtrZXldID0gc291cmNlW2tleV07IH0gfSB9IHJldHVybiB0YXJnZXQ7IH07XG5cbnZhciBfcHJvcFR5cGVzID0gcmVxdWlyZShcInByb3AtdHlwZXNcIik7XG5cbnZhciBfcHJvcFR5cGVzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQoX3Byb3BUeXBlcyk7XG5cbnZhciBfRXJyb3JDb3JyZWN0TGV2ZWwgPSByZXF1aXJlKFwicXIuanMvbGliL0Vycm9yQ29ycmVjdExldmVsXCIpO1xuXG52YXIgX0Vycm9yQ29ycmVjdExldmVsMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQoX0Vycm9yQ29ycmVjdExldmVsKTtcblxudmFyIF9RUkNvZGUgPSByZXF1aXJlKFwicXIuanMvbGliL1FSQ29kZVwiKTtcblxudmFyIF9RUkNvZGUyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChfUVJDb2RlKTtcblxudmFyIF9yZWFjdCA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcblxudmFyIF9yZWFjdDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KF9yZWFjdCk7XG5cbnZhciBfUVJDb2RlU3ZnID0gcmVxdWlyZShcIi4vUVJDb2RlU3ZnXCIpO1xuXG52YXIgX1FSQ29kZVN2ZzIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KF9RUkNvZGVTdmcpO1xuXG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikgeyByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDogeyBkZWZhdWx0OiBvYmogfTsgfVxuXG5mdW5jdGlvbiBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMob2JqLCBrZXlzKSB7IHZhciB0YXJnZXQgPSB7fTsgZm9yICh2YXIgaSBpbiBvYmopIHsgaWYgKGtleXMuaW5kZXhPZihpKSA+PSAwKSBjb250aW51ZTsgaWYgKCFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqLCBpKSkgY29udGludWU7IHRhcmdldFtpXSA9IG9ialtpXTsgfSByZXR1cm4gdGFyZ2V0OyB9XG4vLyBBIGBxci5qc2AgZG9lc24ndCBoYW5kbGUgZXJyb3IgbGV2ZWwgb2YgemVybyAoTSkgc28gd2UgbmVlZCB0byBkbyBpdCByaWdodCwgdGh1cyB0aGUgZGVlcCByZXF1aXJlLlxuXG5cbnZhciBwcm9wVHlwZXMgPSB7XG4gIGJnQ29sb3I6IF9wcm9wVHlwZXMyLmRlZmF1bHQub25lT2ZUeXBlKFtfcHJvcFR5cGVzMi5kZWZhdWx0Lm9iamVjdCwgX3Byb3BUeXBlczIuZGVmYXVsdC5zdHJpbmddKSxcbiAgZmdDb2xvcjogX3Byb3BUeXBlczIuZGVmYXVsdC5vbmVPZlR5cGUoW19wcm9wVHlwZXMyLmRlZmF1bHQub2JqZWN0LCBfcHJvcFR5cGVzMi5kZWZhdWx0LnN0cmluZ10pLFxuICBsZXZlbDogX3Byb3BUeXBlczIuZGVmYXVsdC5zdHJpbmcsXG4gIHNpemU6IF9wcm9wVHlwZXMyLmRlZmF1bHQubnVtYmVyLFxuICB2YWx1ZTogX3Byb3BUeXBlczIuZGVmYXVsdC5zdHJpbmcuaXNSZXF1aXJlZFxufTtcblxudmFyIFFSQ29kZSA9ICgwLCBfcmVhY3QuZm9yd2FyZFJlZikoZnVuY3Rpb24gKF9yZWYsIHJlZikge1xuICB2YXIgX3JlZiRiZ0NvbG9yID0gX3JlZi5iZ0NvbG9yLFxuICAgICAgYmdDb2xvciA9IF9yZWYkYmdDb2xvciA9PT0gdW5kZWZpbmVkID8gXCIjRkZGRkZGXCIgOiBfcmVmJGJnQ29sb3IsXG4gICAgICBfcmVmJGZnQ29sb3IgPSBfcmVmLmZnQ29sb3IsXG4gICAgICBmZ0NvbG9yID0gX3JlZiRmZ0NvbG9yID09PSB1bmRlZmluZWQgPyBcIiMwMDAwMDBcIiA6IF9yZWYkZmdDb2xvcixcbiAgICAgIF9yZWYkbGV2ZWwgPSBfcmVmLmxldmVsLFxuICAgICAgbGV2ZWwgPSBfcmVmJGxldmVsID09PSB1bmRlZmluZWQgPyBcIkxcIiA6IF9yZWYkbGV2ZWwsXG4gICAgICBfcmVmJHNpemUgPSBfcmVmLnNpemUsXG4gICAgICBzaXplID0gX3JlZiRzaXplID09PSB1bmRlZmluZWQgPyAyNTYgOiBfcmVmJHNpemUsXG4gICAgICB2YWx1ZSA9IF9yZWYudmFsdWUsXG4gICAgICBwcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfcmVmLCBbXCJiZ0NvbG9yXCIsIFwiZmdDb2xvclwiLCBcImxldmVsXCIsIFwic2l6ZVwiLCBcInZhbHVlXCJdKTtcblxuICAvLyBVc2UgdHlwZSA9PT0gLTEgdG8gYXV0b21hdGljYWxseSBwaWNrIHRoZSBiZXN0IHR5cGUuXG4gIHZhciBxcmNvZGUgPSBuZXcgX1FSQ29kZTIuZGVmYXVsdCgtMSwgX0Vycm9yQ29ycmVjdExldmVsMi5kZWZhdWx0W2xldmVsXSk7XG4gIHFyY29kZS5hZGREYXRhKHZhbHVlKTtcbiAgcXJjb2RlLm1ha2UoKTtcbiAgdmFyIGNlbGxzID0gcXJjb2RlLm1vZHVsZXM7XG4gIHJldHVybiBfcmVhY3QyLmRlZmF1bHQuY3JlYXRlRWxlbWVudChfUVJDb2RlU3ZnMi5kZWZhdWx0LCBfZXh0ZW5kcyh7fSwgcHJvcHMsIHtcbiAgICBiZ0NvbG9yOiBiZ0NvbG9yLFxuICAgIGJnRDogY2VsbHMubWFwKGZ1bmN0aW9uIChyb3csIHJvd0luZGV4KSB7XG4gICAgICByZXR1cm4gcm93Lm1hcChmdW5jdGlvbiAoY2VsbCwgY2VsbEluZGV4KSB7XG4gICAgICAgIHJldHVybiAhY2VsbCA/IFwiTSBcIiArIGNlbGxJbmRleCArIFwiIFwiICsgcm93SW5kZXggKyBcIiBsIDEgMCAwIDEgLTEgMCBaXCIgOiBcIlwiO1xuICAgICAgfSkuam9pbihcIiBcIik7XG4gICAgfSkuam9pbihcIiBcIiksXG4gICAgZmdDb2xvcjogZmdDb2xvcixcbiAgICBmZ0Q6IGNlbGxzLm1hcChmdW5jdGlvbiAocm93LCByb3dJbmRleCkge1xuICAgICAgcmV0dXJuIHJvdy5tYXAoZnVuY3Rpb24gKGNlbGwsIGNlbGxJbmRleCkge1xuICAgICAgICByZXR1cm4gY2VsbCA/IFwiTSBcIiArIGNlbGxJbmRleCArIFwiIFwiICsgcm93SW5kZXggKyBcIiBsIDEgMCAwIDEgLTEgMCBaXCIgOiBcIlwiO1xuICAgICAgfSkuam9pbihcIiBcIik7XG4gICAgfSkuam9pbihcIiBcIiksXG4gICAgcmVmOiByZWYsXG4gICAgc2l6ZTogc2l6ZSxcbiAgICB2aWV3Qm94U2l6ZTogY2VsbHMubGVuZ3RoXG4gIH0pKTtcbn0pO1xuXG5leHBvcnRzLlFSQ29kZSA9IFFSQ29kZTtcblFSQ29kZS5kaXNwbGF5TmFtZSA9IFwiUVJDb2RlXCI7XG5RUkNvZGUucHJvcFR5cGVzID0gcHJvcFR5cGVzO1xuXG5leHBvcnRzLmRlZmF1bHQgPSBRUkNvZGU7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-qr-code/lib/index.js\n");

/***/ })

};
;