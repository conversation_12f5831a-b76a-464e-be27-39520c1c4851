import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from "typeorm"
import {KeyValueStore} from "./WorkspaceMemberSettings";

@Entity()
export class Creator {

    @PrimaryGeneratedColumn('uuid')
    id: string

    @Column({type: 'varchar', nullable: false})
    name: string

    @Column({type: 'varchar', nullable: false, unique: true})
    domain: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    stripeAccountId: string

    @Index()
    @Column({type: 'bool', default: false})
    canBeEmailed: boolean

    @Column({type: 'varchar', nullable: true})
    contactEmail: string

    @Column({type: "varchar", length: 500, nullable: true, default: 'I create beautiful and functional Opendashboard templates'})
    shortDescription: string

    @Column({type: 'json', nullable: true})
    links: string[]

    @Column({type: 'varchar', nullable: true})
    logo: string

    @Column({type: 'varchar', nullable: true})
    coverImage: string

    @Column({type: 'varchar', nullable: false})
    createdById: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    ownerId: string

    @Column({type: 'varchar', nullable: true, unique: true})
    workspaceId: string

    @CreateDateColumn({type: 'timestamp', default: 0})
    createdAt: Date

    @UpdateDateColumn({type: 'timestamp', default: 0})
    updatedAt: Date

    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date

    @Column({type: 'varchar', nullable: true, select: false})
    deletionReason: string

    @Index()
    @Column({type: 'varchar', default: 'UTC'})
    timezone: string

    @Index()
    @Column({type: 'bool', default: false})
    isSupportAccessEnabled: boolean

    @Column({type: 'bool', default: false})
    isFunctionalityLimited: boolean

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

}

