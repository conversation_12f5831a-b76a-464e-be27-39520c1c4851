import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from 'typeorm';

@Entity()
@Index(['affiliateId', 'locationId'], {unique: true})
export class AffiliateAnalytic {

    @PrimaryGeneratedColumn()
    id: number;

    @Column({type: 'varchar', nullable: false})
    affiliateId: string;

    @Column({type: 'varchar', nullable: true})
    locationId: string;

    @Column({type: 'int', default: 0})
    clickCount: number;

    @Index()
    @Column({type: 'datetime', nullable: false})
    eventAt: Date;

    @Index()
    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @Index()
    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

}


