import {<PERSON>ush<PERSON><PERSON>ider} from "../entity/pushRegistration";
import {PushRegistrationService} from "../service/pushRegistration";
import {UserService} from "../service/user";
import {NotificationService} from "../service/notification";
import {WorkspaceService} from "../service/workspace";
import {EmailUser, SendEmailWithContent} from "./email";
import {appUrl} from "../config";
import {SendNotification} from "./firebase";
import {KeyValueStore} from "../entity/WorkspaceMemberSettings";
import {broadcastUserNotifications} from "../socketio/workspace";
import {User} from "../entity/User";
import {Workspace} from "../entity/Workspace";
import {arrayDeDuplicate} from "opendb-app-db-utils/lib";
import {In, MoreThanOrEqual} from "typeorm";
import {SettingsService} from "../service/setting";
import {Settings} from "../entity/Settings";
import {Notification} from "../entity/notification";
import {TokenService} from "../service/token";
import {TokenType} from "../entity/Token";


export interface NotificationData {
    title: string,
    message?: string,
    image?: string,
    link?: string
}

export interface CreateNotificationData {
    userId: string,
    workspaceId: string,
    options: NotificationData
}

export const CreateNotifications = async (data: CreateNotificationData[]) => {
    const userIds = arrayDeDuplicate(data.map(d => d.userId))
    const workspaceIds = arrayDeDuplicate(data.map(d => d.workspaceId))

    const s = new NotificationService()

    const usersMap: { [id: string]: User } = {}
    const workspacesMap: { [id: string]: Workspace } = {}
    const userSettingsMap: { [id: string]: Settings } = {}

    const users = await new UserService().find({id: In(userIds)})
    const workspaces = await new WorkspaceService().find({id: In(workspaceIds)})
    const settings = await new SettingsService().find({userId: In(userIds)})

    for (let user of users) {
        usersMap[user.id] = user
    }
    for (let workspace of workspaces) {
        workspacesMap[workspace.id] = workspace
    }
    for (let setting of settings) {
        userSettingsMap[setting.userId] = setting
    }
    const result: (Notification | Error)[] = []

    const usersNotificationsMap: {
        [id: string]: {
            userId: string,
            workspaceId: string,
            notifications: Notification[]
        }
    } = {}

    for (let datum of data) {
        const {workspaceId, userId, options} = datum
        const user = usersMap[userId]
        const workspace = workspacesMap[workspaceId]

        if (!users) {
            result.push(new Error("User not found"))
            continue
        }
        if (!workspace) {
            result.push(new Error("Workspace not found"))
            continue
        }
        const settings = userSettingsMap[userId]
        let sendEmail = true
        if (settings && settings.notification) {
            sendEmail = !!settings.notification?.email?.isActive
        }
        const meta: KeyValueStore = {}
        if (sendEmail) {
            const to: EmailUser = {
                email: user.email,
                name: `${user.firstName} ${user.lastName}`.trim()
            }

            const subject = `New Notification in ${workspace.name}:  ${options.title} | Opendashboard`
            const body = `
    <p style="font-size: 24px; font-weight: 600;">🔔 New notification in ${workspace.name}</p>
    <p>${options.title} ${options.message ? ' - ' + options.message : ''}</p>
    <p style="color:#313539;">Click on the button below to view in Opendashboard.</p>
    `
            const button = {
                label: 'View',
                url: options.link || appUrl(`/${workspace.domain}`)
            }
            const messageId = await SendEmailWithContent(to, subject, body, button)

            meta['emailMessageIds'] = [messageId]
        }
        const pushTokens = await GetPushTokens(userId, PushProvider.FCM)
        const tokens = arrayDeDuplicate(pushTokens.map(p => p.providerToken))

        let fcmResponseIds: string[] = []
        for (let pushToken of tokens) {
            const res = await SendNotification({
                token: pushToken,
                notification: {
                    title: options.title,
                    body: options.message,
                    imageUrl: options.image
                },
                data: {
                    link: options.link
                }
            })
            if (res) fcmResponseIds.push(res)
        }

        meta['fcmResponseIds'] = fcmResponseIds
        const notification = await s.insert({
            userId,
            workspaceId,
            title: options.title,
            description: options.message,
            linkUrl: options.link,
            meta
        })
        result.push(notification)

        const key = `${userId}_${workspaceId}`
        usersNotificationsMap[key] = usersNotificationsMap[key] || {
            userId,
            workspaceId,
            notifications: []
        }
        usersNotificationsMap[key].notifications.push(notification)
    }
    for (const {notifications, userId, workspaceId} of Object.values(usersNotificationsMap)) {
        broadcastUserNotifications(workspaceId, userId, notifications)
    }
    return {result}
}

export const RegisterPushToken = async (userId: string, sessionId: string, provider: PushProvider, token: string) => {
    const s = new PushRegistrationService()
    const registration = await s.findOne({userId, sessionId, provider})

    if (registration) {
        await s.update({userId, sessionId, provider}, {refreshedAt: new Date(), providerToken: token})
        registration.refreshedAt = new Date()
        return registration
    }
    return await s.insert({refreshedAt: new Date(), userId, sessionId, providerToken: token, provider})
}

export const DeRegisterPushToken = async (userId: string, sessionId: string, provider: PushProvider) => {
    const s = new PushRegistrationService()

    return await s.hardRemove({userId, sessionId, provider})
}

export const GetPushTokens = async (userId: string, provider: PushProvider) => {
    const s = new PushRegistrationService()

    const subQ = new TokenService().getRepository()
        .createQueryBuilder()
        .select('id')
        .where({userId, purpose: TokenType.JWT, expiresAt: MoreThanOrEqual(new Date())})

    const qB = s.getRepository()
        .createQueryBuilder()
        .select()
        .where({userId, provider})
        .andWhere(`sessionId IN (${subQ.getQuery()})`)
        .setParameters(subQ.getParameters())


    return qB.getMany()
}








