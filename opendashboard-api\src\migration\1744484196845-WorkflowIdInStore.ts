import { MigrationInterface, QueryRunner } from "typeorm";

export class WorkflowIdInStore1744484196845 implements MigrationInterface {
    name = 'WorkflowIdInStore1744484196845'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_ed70918a866a0d5ec1a6329381\` ON \`workspace_integration_store\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`workflowId\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`workspaceId\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`workspaceId\` varchar(50) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`integration\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`integration\` varchar(50) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`scope\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`scope\` varchar(50) NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_256f577733e716406f14c77f4f\` ON \`workspace_integration_store\` (\`workspaceId\`, \`workflowId\`, \`integration\`, \`scope\`, \`key\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_256f577733e716406f14c77f4f\` ON \`workspace_integration_store\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`scope\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`scope\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`integration\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`integration\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`workspaceId\``);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` ADD \`workspaceId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_integration_store\` DROP COLUMN \`workflowId\``);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_ed70918a866a0d5ec1a6329381\` ON \`workspace_integration_store\` (\`workspaceId\`, \`integration\`, \`scope\`, \`key\`)`);
    }

}
