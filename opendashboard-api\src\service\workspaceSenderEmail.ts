import {getRepository} from '../connection/db';
import {BaseService} from './service';
import {Repository} from 'typeorm/repository/Repository';
import {WorkspaceSenderEmail} from "../entity/WorkspaceSenderEmail";
import {ServerProcessingError, UniqueConstraintError} from "../errors/AppError";

export class WorkspaceSenderService extends BaseService<WorkspaceSenderEmail> {

    initRepository = (): Repository<WorkspaceSenderEmail> => {
        return getRepository(WorkspaceSenderEmail);
    }

    addSender = async (data: Pick<WorkspaceSenderEmail, 'email' | 'name' | 'addedByUserId' | 'workspaceId' | 'workspaceDomainId'>) => {
        try {
            return await this.insert(data);
        } catch (err) {
            if (err.message.includes("Duplicate entry")) throw new UniqueConstraintError('email')
            throw new ServerProcessingError(err.message)
        }
    }

}




