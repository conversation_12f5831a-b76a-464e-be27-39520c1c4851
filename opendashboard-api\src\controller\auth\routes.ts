import {Routes} from "../../routes";
import {AuthController} from "./controller"

export const authRoutes: Routes = {
    basePath: '/auth',
    routes: {
        '/sign-in': {
            post: {
                controller: AuthController,
                action: "signIn",
            }
        },
        '/exchange-token': {
            post: {
                controller: Auth<PERSON><PERSON>roller,
                action: "exchangeToken",
            }
        },
        '/verify-email': {
            get: {
                controller: AuthController,
                action: "verifyEmail",
            }
        },
        '/verify-sender': {
            get: {
                controller: AuthController,
                action: "verifySender",
            }
        },
        '/:provider/start': {
            get: {
                controller: AuthController,
                action: "providerAuthenticate",
            }
        },
        '/:provider/callback': {
            get: {
                controller: AuthController,
                action: "providerCallback",
            }
        },
    }
};