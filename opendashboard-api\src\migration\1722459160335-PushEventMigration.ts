import { MigrationInterface, QueryRunner } from "typeorm";

export class PushEventMigration1722459160335 implements MigrationInterface {
    name = 'PushEventMigration1722459160335'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`user_hourly_event\` (\`id\` int NOT NULL AUTO_INCREMENT, \`userId\` varchar(36) NULL, \`workspaceId\` varchar(36) NULL, \`pageId\` varchar(36) NULL, \`viewId\` varchar(36) NULL, \`databaseId\` varchar(36) NULL, \`event\` int NULL, \`eventCount\` int NOT NULL DEFAULT '1', \`anchorAt\` timestamp NULL, \`eventAt\` timestamp NULL, \`createdAt\` timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` timestamp(6) NULL, \`auditLog\` json NULL, INDEX \`IDX_fc90e58ec55263035d45724f7e\` (\`userId\`), INDEX \`IDX_811c9fbdee3676769b8698faab\` (\`workspaceId\`), INDEX \`IDX_4bf8be244f8deb9e8281e7fcf8\` (\`pageId\`), INDEX \`IDX_567b7d04b571cf40fc7db1e8da\` (\`viewId\`), INDEX \`IDX_9ea54d6451aac3077c580c22c1\` (\`databaseId\`), INDEX \`IDX_dc8fdf9f1f0ac5edf7a33145ea\` (\`event\`), INDEX \`IDX_f779d3e42dc0ed69659dbfb529\` (\`anchorAt\`), UNIQUE INDEX \`IDX_8fa3b341cbe90fdf292330ea68\` (\`userId\`, \`workspaceId\`, \`pageId\`, \`databaseId\`, \`viewId\`, \`event\`, \`anchorAt\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_8fa3b341cbe90fdf292330ea68\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_f779d3e42dc0ed69659dbfb529\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_dc8fdf9f1f0ac5edf7a33145ea\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_9ea54d6451aac3077c580c22c1\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_567b7d04b571cf40fc7db1e8da\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_4bf8be244f8deb9e8281e7fcf8\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_811c9fbdee3676769b8698faab\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP INDEX \`IDX_fc90e58ec55263035d45724f7e\` ON \`user_hourly_event\``);
        await queryRunner.query(`DROP TABLE \`user_hourly_event\``);
    }

}
