import {Webhook<PERSON>ontroller} from "./controller";
import {Routes} from "../../../routes";

export const webhooksRoutes: Routes = {
    basePath: '/webhooks',
    routes: {
        '/': {
            get: {
                controller: WebhookController,
                action: "getAll"
            }
        },
        '/:id': {
            get: {
                controller: WebhookController,
                action: "getAll"
            }
        },
        '/:id/approvals/:runId/approve': {
            get: {
                controller: WebhookController,
                action: "handleApproval"
            },
        },
        '/:id/approvals/:runId/reject': {
            get: {
                controller: WebhookController,
                action: "rejectApproval"
            },
        },
        // '/:id/approvals/:runId/details': {
        //     get: {
        //         controller: WebhookController,
        //         action: "approvalDetails"
        //     },
        // },
        '/:id/dispatch': {
            get: {
                controller: WebhookController,
                action: "handleDispatch"
            },
            post: {
                controller: WebhookController,
                action: "handleDispatch"
            },
            put: {
                controller: WebhookController,
                action: "handleDispatch"
            },
            patch: {
                controller: <PERSON>hookController,
                action: "handleDispatch"
            },
            delete: {
                controller: WebhookController,
                action: "handleDispatch"
            },
        },
        '/:id/dispatch/test': {
            get: {
                controller: WebhookController,
                action: "handleTestDispatch"
            },
            post: {
                controller: WebhookController,
                action: "handleTestDispatch"
            },
            put: {
                controller: WebhookController,
                action: "handleTestDispatch"
            },
            patch: {
                controller: WebhookController,
                action: "handleTestDispatch"
            },
            delete: {
                controller: WebhookController,
                action: "handleTestDispatch"
            },
        },
        "/:id/integrations/:integration/:trigger/dispatch": {
            get: {controller: WebhookController, action: "handleIntegrationDispatch"},
            post: {controller: WebhookController, action: "handleIntegrationDispatch"},
            patch: {controller: WebhookController, action: "handleIntegrationDispatch"},
            delete: {controller: WebhookController, action: "handleIntegrationDispatch"},
            put: {controller: WebhookController, action: "handleIntegrationDispatch"},
        },
        "/:id/integrations/:integration/:trigger/dispatch/test": {
            get: {controller: WebhookController, action: "handleIntegrationTestDispatch"},
            post: {controller: WebhookController, action: "handleIntegrationTestDispatch"},
            patch: {controller: WebhookController, action: "handleIntegrationTestDispatch"},
            delete: {controller: WebhookController, action: "handleIntegrationTestDispatch"},
            put: {controller: WebhookController, action: "handleIntegrationTestDispatch"},
        },


    }
};


