"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, selectedEvent, onClick, onDragStart, canEditData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute left-2 right-2 px-3 py-2 rounded-md text-xs shadow-sm border cursor-pointer\", \"transition-all duration-200 hover:shadow-md\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700\"),\n        style: style,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium truncate leading-tight\",\n                children: event.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-[10px] mt-0.5 opacity-75\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CalendarEventItem;\nconst TimeSlot = (param)=>{\n    let { hour, date, children, onDragOver, onDrop, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        className: \"flex-1 relative border-b border-gray-100 min-h-[60px] cursor-pointer\",\n        style: {\n            height: \"60px\"\n        },\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TimeSlot;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, onEventDrop } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), selectedDate));\n    const currentTimePosition = (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? {\n        hour: new Date().getHours(),\n        minutes: new Date().getMinutes()\n    } : null;\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Get the actual event element to match its size\n        const eventElement = e.currentTarget;\n        const eventRect = eventElement.getBoundingClientRect();\n        // Create a drag image that matches the actual event size\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"\".concat(eventRect.width, \"px\");\n        dragImg.style.height = \"\".concat(Math.max(eventRect.height, 40), \"px\");\n        dragImg.style.background = \"#1e293b\";\n        dragImg.style.border = \"1px solid #475569\";\n        dragImg.style.borderRadius = \"6px\";\n        dragImg.style.padding = \"8px 12px\";\n        dragImg.style.boxShadow = \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\";\n        dragImg.style.fontSize = \"12px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"white\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.opacity = \"0.95\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; margin-bottom: 4px; color: white; line-height: 1.2;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 10px; color: #cbd5e1; line-height: 1.2;\">\\n        ').concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        // Set drag image with center offset based on actual size\n        e.dataTransfer.setDragImage(dragImg, eventRect.width / 2, eventRect.height / 2);\n        // Clean up after a short delay\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (hour, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the time slot\n        const timeSlotHeight = 60; // height of time slot in pixels\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const minutes = Math.floor(relativeY / timeSlotHeight * 60);\n        // Create new date with calculated minutes\n        const newDate = new Date(selectedDate);\n        newDate.setHours(hour, minutes, 0, 0);\n        // Check if the new date is the same as the original\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    if (dayEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b bg-secondary sticky top-0 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events scheduled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"No events planned for \".concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \".\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    const newDate = new Date(selectedDate);\n                                    newDate.setHours(new Date().getHours(), 0, 0, 0);\n                                    openAddEventForm(newDate);\n                                },\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 200,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-secondary sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto relative bg-white\",\n                id: \"day-view-container\",\n                children: [\n                    hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex border-b border-gray-50 hover:bg-gray-25 transition-colors\",\n                            style: {\n                                height: \"60px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 sticky left-0 bg-white z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), isMobile ? \"h\" : \"h\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[10px] text-gray-400\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), \"a\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                    hour: hour,\n                                    date: selectedDate,\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(hour, e),\n                                    onClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(selectedDate);\n                                            newDate.setHours(hour, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: dayEvents.filter((event)=>{\n                                        const eventHour = new Date(event.start).getHours();\n                                        return eventHour === hour;\n                                    }).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                            event: event,\n                                            selectedEvent: selectedEvent,\n                                            style: {\n                                                top: \"\".concat(new Date(event.start).getMinutes() / 60 * 100, \"%\"),\n                                                height: \"\".concat(Math.max(30, (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventDurationInMinutes)(event) / 60 * 100), \"%\"),\n                                                zIndex: selectedEvent === event.id ? 20 : 10\n                                            },\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                const container = document.getElementById(\"day-view-container\");\n                                                if (container) {\n                                                    savedScrollTop.current = container.scrollTop;\n                                                }\n                                                setSelectedEvent(event.id);\n                                                handleEventClick(event);\n                                            },\n                                            onDragStart: handleDragStart,\n                                            canEditData: canEditData\n                                        }, event.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, undefined)),\n                    currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-0 flex items-center z-30 pointer-events-none\", isMobile ? \"left-14\" : \"left-20\"),\n                        style: {\n                            top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize\n    ];\n});\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"TimeSlot\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ })

});