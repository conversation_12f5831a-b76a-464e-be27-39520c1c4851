import {isProduction} from "../config";

export type SubscriptionCommitment = {
    [key in 'monthToMonth' | 'annual']: {
        costInCents: number
        priceId: string
    }
}

export interface UsageLimits {
    users: number
    collaborators: number
    records: number
    emails: number
    senderDomains: number
    senderEmails: number
    enrichment: number
    workflowTask: number
    aiGeneration: number
    dataHistory: number
    pollingIntervalMins: number
}

export interface PayPerUse {
    enrichment: number
    aiGeneration: number
    email: number
    workspaceTask: number
}

export interface PlanAddOn {
    id: string
    name: string
    priceId: string
    commitments: SubscriptionCommitment
    extra: {
        enrichment: number,
        aiGeneration: number,
        collaborators: number,
        emails: number,
        records: number,
        workflowTasks: number,
    }
}

export interface WorkspacePlan {
    name: string
    id: string
    description: string
    priceId: string
    commitments: SubscriptionCommitment
    limits: UsageLimits
}

const freeLimit: UsageLimits = Object.freeze({
    users: 2,
    collaborators: 5,
    records: 1000,
    emails: 1000,
    senderDomains: 1,
    senderEmails: 1,
    workflowTask: 1000,
    enrichment: 10,
    aiGeneration: 100,
    dataHistory: 7,
    pollingIntervalMins: 30
})

const starterLimit: UsageLimits = Object.freeze({
    users: 2,
    collaborators: 5,
    records: 5000,
    emails: 5000,
    senderDomains: 1,
    senderEmails: 5,
    workflowTask: 5000,
    enrichment: 100,
    aiGeneration: 1000,
    dataHistory: 30,
    pollingIntervalMins: 15
})

const proLimit: UsageLimits = Object.freeze({
    users: 5,
    collaborators: 5,
    records: 50000,
    emails: 20000,
    senderDomains: 5,
    senderEmails: 10,
    workflowTask: 20000,
    enrichment: 500,
    aiGeneration: 5000,
    dataHistory: 90,
    pollingIntervalMins: 5
})

export const free: WorkspacePlan = Object.freeze({
    id: 'free',
    name: 'Free',
    priceId: '',
    description: 'Ideal for individuals or startups just beginning their journey',
    commitments: {
        monthToMonth: {
            costInCents: 0,
            priceId: ''
        },
        annual: {
            costInCents: 0,
            priceId: ''
        }
    },
    limits: freeLimit
})

const stageStarter: WorkspacePlan = Object.freeze({
    id: 'starter',
    name: 'Starter',
    description: 'Designed for small teams ready to unlock more power',
    priceId: 'price_1Ozrl0Gqu7Shz89E3bYRa1x2',
    commitments: {
        monthToMonth: {
            costInCents: 2500,
            priceId: 'price_1Ozrl0Gqu7Shz89E3bYRa1x2'
        },
        annual: {
            costInCents: 2000,
            priceId: 'price_1OzrlZGqu7Shz89E1T7qGUFn'
        }
    },
    limits: starterLimit
})

const stagePro: WorkspacePlan = Object.freeze({
    id: 'pro',
    name: 'Pro',
    description: 'Tailored for established businesses that higher demands',
    priceId: 'price_1Ozrk8Gqu7Shz89E7XnEZBN6',
    commitments: {
        monthToMonth: {
            costInCents: 7500,
            priceId: 'price_1Ozrk8Gqu7Shz89E7XnEZBN6'
        },
        annual: {
            costInCents: 6000,
            priceId: 'price_1Ozrk8Gqu7Shz89E6T3qMSuK'
        }
    },
    limits: proLimit
})

const starter: WorkspacePlan = Object.freeze({
    id: 'starter',
    name: 'Starter',
    description: 'Designed for small teams ready to unlock more power',
    priceId: 'price_1PbD2yGqu7Shz89EE7ktHhh5',
    commitments: {
        monthToMonth: {
            costInCents: 2500,
            priceId: 'price_1PbD2yGqu7Shz89EE7ktHhh5'
        },
        annual: {
            costInCents: 2000,
            priceId: 'price_1PbD2yGqu7Shz89ExJKnDt1T'
        }
    },
    limits: starterLimit
})

const pro: WorkspacePlan = Object.freeze({
    id: 'pro',
    name: 'Pro',
    description: 'Tailored for established businesses that higher demands',
    priceId: 'price_1PbD3IGqu7Shz89ECnz00u74',
    commitments: {
        monthToMonth: {
            costInCents: 7500,
            priceId: 'price_1PbD3IGqu7Shz89ECnz00u74'
        },
        annual: {
            costInCents: 6000,
            priceId: 'price_1PbD3IGqu7Shz89EL5f9HLFr'
        }
    },
    limits: proLimit
})

const StageSubscriptionPlans: {
    [key: string]: WorkspacePlan
} = Object.freeze({
    free, starter: stageStarter, pro: stagePro
})

const ProdSubscriptionPlans: {
    [key: string]: WorkspacePlan
} = Object.freeze({
    free, starter, pro
})

const stageUsersAddOn: PlanAddOn = Object.freeze({
    commitments: {
        monthToMonth: {
            costInCents: 500,
            priceId: 'price_1OzrupGqu7Shz89EcmI5PARo'
        },
        annual: {
            costInCents: 400,
            priceId: 'price_1OzrupGqu7Shz89ErH2ItpAG'
        }
    },
    id: "users_add_on",
    name: "Additional Users",
    priceId: "price_1OzrupGqu7Shz89EcmI5PARo",
    extra: {
        enrichment: 10,
        aiGeneration: 100,
        collaborators: 5,
        emails: 2000,
        records: 5000,
        workflowTasks: 500
    }
})

const prodUsersAddOn: PlanAddOn = Object.freeze({
    commitments: {
        monthToMonth: {
            costInCents: 500,
            priceId: 'price_1QZpfMGqu7Shz89EeJnteJpo'
        },
        annual: {
            costInCents: 400,
            priceId: 'price_1QZpfMGqu7Shz89EIUZ6qIDJ'
        }
    },
    id: "users_add_on",
    name: "Additional Users",
    priceId: "price_1QZpfMGqu7Shz89EeJnteJpo",
    extra: {
        enrichment: 10,
        aiGeneration: 100,
        collaborators: 5,
        emails: 2000,
        records: 5000,
        workflowTasks: 500
    }
})

// const collaboratorsAddOn: PlanAddOn = Object.freeze({
//     commitments: {
//         monthToMonth: {
//             costInCents: 500,
//             priceId: 'price_1OzrzWGqu7Shz89EGIe4FlXW'
//         },
//         annual: {
//             costInCents: 400,
//             priceId: 'price_1OzrzpGqu7Shz89EihxuOR4d'
//         }
//     },
//     id: "collaborators_add_on",
//     name: "Additional Collaborators",
//     priceId: "price_1OzrzWGqu7Shz89EGIe4FlXW"
// })
//
// const recordsAddOn: PlanAddOn = Object.freeze({
//     commitments: {
//         monthToMonth: {
//             costInCents: 500,
//             priceId: 'price_1Ozs2XGqu7Shz89EBgtiTea0'
//         },
//         annual: {
//             costInCents: 400,
//             priceId: 'price_1Ozs2XGqu7Shz89Ezto7uK9M'
//         }
//     },
//     id: "records_add_on",
//     name: "Additional Records",
//     priceId: "price_1Ozs2XGqu7Shz89EBgtiTea0"
// })


export const WorkspacePlans = isProduction() ? ProdSubscriptionPlans : StageSubscriptionPlans

// const StageWorkspaceAddOns: {
//     [key: string]: PlanAddOn
// } = Object.freeze({
//     users: usersAddOn,
//     // collaborators: collaboratorsAddOn,
//     // records: recordsAddOn
// })
// export const WorkspaceAddOns = isProduction() ? StageWorkspaceAddOns : StageWorkspaceAddOns;

export const WorkspaceAddOns = {
    users: isProduction()? prodUsersAddOn: stageUsersAddOn
}

const constructPlanIdMap = () => {
    const map: {
        [planId: string]: WorkspacePlan
    } = {}

    for (const plan of Object.values(WorkspacePlans)) {
        map[plan.id] = plan
        for (const commitment of Object.values(plan.commitments)) {
            map[commitment.priceId] = plan
        }
    }
    return map
}

export const WorkspacePlanPriceIdMap = Object.freeze(constructPlanIdMap())

export const PayPerUsePricing: PayPerUse = Object.freeze({
    enrichment: 0.1,
    aiGeneration:0.01,
    email: 0.001,
    workspaceTask: 0.002,
})

export const PayPerUsePricingInCents: PayPerUse = Object.freeze({
    enrichment: PayPerUsePricing.enrichment * 100,
    aiGeneration: PayPerUsePricing.aiGeneration * 100,
    email: PayPerUsePricing.email * 100,
    workspaceTask: PayPerUsePricing.workspaceTask * 100,
})

export interface AddOnsQuota {
    users: number
    collaborators: number
    records: number
    sendingDomains: number
    sendingEmails: number
    workflowTask: number
    aiGeneration: number
    enrichment: number
}

export const CreatorSettings = Object.freeze({
    CommissionPercent: 20,
    PurchaseEligibilityForPayoutDays: 14,
    PayoutMinBalance: 20
})





