import {Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn} from 'typeorm';
import {KeyValueStore} from "./WorkspaceMemberSettings";

@Entity()
export class Notification {

    @PrimaryGeneratedColumn("uuid")
    id: string;

    @Index()
    @Column({type: 'varchar', nullable: false})
    userId: string

    @Index()
    @Column({type: 'varchar', nullable: false})
    workspaceId: string

    @Index()
    @Column({type: 'varchar', nullable: true})
    title: string

    @Column({type: 'text', nullable: true})
    description: string

    @Column({type: 'varchar', nullable: true})
    linkUrl: string;

    @Index()
    @Column({type: 'bool', default: false})
    isSeen: boolean

    @Column({type: 'timestamp', nullable: true})
    seenAt: Date

    @Column({type: "json", nullable: true})
    meta: KeyValueStore

    @Index()
    @CreateDateColumn({type: 'timestamp'})
    createdAt: Date;

    @Index()
    @UpdateDateColumn({type: 'timestamp'})
    updatedAt: Date;

    @Index()
    @DeleteDateColumn({type: 'timestamp', nullable: true})
    deletedAt: Date;

}
