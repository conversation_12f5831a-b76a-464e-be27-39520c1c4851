import React, { useRef } from 'react';
import { format, startOfWeek, endOfWeek, isToday, isSameDay, addDays, setHours } from 'date-fns';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { CalendarEvent } from '../index';
import { useScreenSize } from '@/providers/screenSize';

interface WeekViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  setSelectedDate: (date: Date) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  savedScrollTop: React.MutableRefObject<number>;
  handleEventClick: (event: CalendarEvent) => void;
  onEventDrop?: (event: CalendarEvent, newDate: Date) => void;
}

const CalendarEventItem = ({
  event,
  style,
  selectedEvent,
  onClick,
  canEditData
}: {
  event: CalendarEvent;
  style: React.CSSProperties;
  selectedEvent: string | null;
  onClick: (e: React.MouseEvent) => void;
  canEditData: boolean;
}) => {
  const handleDragStart = (e: React.DragEvent) => {
    if (!canEditData) return;

    e.dataTransfer.setData('text/plain', JSON.stringify(event));

    // Get the actual event element to match its size and appearance
    const eventElement = e.currentTarget as HTMLElement;
    const eventRect = eventElement.getBoundingClientRect();
    const containerElement = document.getElementById('week-view-container');
    const containerRect = containerElement?.getBoundingClientRect();

    // Calculate the actual width available for events in week view (1/7 of container minus time column)
    const timeColumnWidth = 80; // w-20 = 80px
    const availableWidth = containerRect ? (containerRect.width - timeColumnWidth) / 7 - 8 : 150; // divide by 7 days, subtract padding

    // Create a drag image that exactly matches the original event size
    const dragImg = document.createElement('div');
    dragImg.innerHTML = `
      <div style="
        background: white;
        color: #1f2937;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        font-size: 12px;
        font-weight: 500;
        width: ${Math.max(availableWidth, 120)}px;
        height: ${Math.max(eventRect.height, 40)}px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        opacity: 0.95;
      ">
        <div style="font-weight: 600; margin-bottom: 2px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: #1f2937; line-height: 1.3;">
          ${event.title}
        </div>
        <div style="font-size: 10px; opacity: 0.7; color: #6b7280; line-height: 1.3;">
          ${format(new Date(event.start), 'h:mm a')}
        </div>
      </div>
    `;

    dragImg.style.position = 'absolute';
    dragImg.style.top = '-2000px';
    dragImg.style.left = '-2000px';
    dragImg.style.pointerEvents = 'none';

    document.body.appendChild(dragImg);

    // Use the center of the drag image for better visual feedback
    const dragWidth = Math.max(availableWidth, 120);
    e.dataTransfer.setDragImage(dragImg, dragWidth / 2, eventRect.height / 2);

    setTimeout(() => {
      if (document.body.contains(dragImg)) {
        document.body.removeChild(dragImg);
      }
    }, 0);
  };

  return (
    <div
      draggable={canEditData}
      onDragStart={handleDragStart}
      className={cn(
        "absolute left-1 right-1 px-2 py-1.5 rounded-md text-xs shadow-sm border cursor-pointer",
        "transition-all duration-200 hover:shadow-md",
        selectedEvent === event.id
          ? "bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20"
          : "bg-card text-card-foreground border-border hover:border-primary/30 hover:bg-accent"
      )}
      style={style}
      onClick={onClick}
    >
      <div className="font-medium truncate leading-tight">{event.title}</div>
      <div className={cn(
        "text-[10px] mt-0.5 opacity-75",
        selectedEvent === event.id ? "text-primary-foreground/80" : "text-muted-foreground"
      )}>
        {format(new Date(event.start), 'h:mm a')}
      </div>
    </div>
  );
};

const TimeSlot = ({
  day,
  hour,
  children,
  onClick,
  onDrop,
  canEditData
}: {
  day: Date;
  hour: number;
  children: React.ReactNode;
  onClick: () => void;
  onDrop: (e: React.DragEvent) => void;
  canEditData: boolean;
}) => {
  const handleDragOver = (e: React.DragEvent) => {
    if (!canEditData) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  return (
    <div
      onDragOver={handleDragOver}
      onDrop={onDrop}
      className={cn(
        "flex-1 relative border-r border-b border-gray-100",
        "cursor-pointer"
      )}
      onClick={onClick}
      style={{ height: '60px' }}
    >
      {children}
    </div>
  );
};

export const WeekView: React.FC<WeekViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  setSelectedDate,
  openAddEventForm,
  canEditData,
  savedScrollTop,
  handleEventClick,
  onEventDrop
}) => {
  const { isMobile } = useScreenSize();
  const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 });
  const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 0 });
  const days = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
  const hours = Array.from({ length: 24 }, (_, i) => i);

  const todayIndex = days.findIndex(day => isToday(day));
  const currentTimePosition = todayIndex !== -1 ? {
    dayIndex: todayIndex,
    hour: new Date().getHours(),
    minutes: new Date().getMinutes()
  } : null;

  // Helper function for event duration calculation
  const getEventDurationInMinutes = (event: CalendarEvent): number => {
    const start = new Date(event.start);
    const end = new Date(event.end);
    return Math.max(30, (end.getTime() - start.getTime()) / (1000 * 60));
  };

  // Check if there are any events for the current week
  const weekEvents = events.filter(event => {
    const eventStart = new Date(event.start);
    return eventStart >= weekStart && eventStart <= weekEnd;
  });

  const handleDrop = (day: Date, hour: number, e: React.DragEvent) => {
    e.preventDefault();
    if (!canEditData) return;

    try {
      const eventData = JSON.parse(e.dataTransfer.getData('text/plain')) as CalendarEvent;
      const originalDate = new Date(eventData.start);

      // Calculate minutes based on drop position within the time slot
      const timeSlotHeight = 60; // height of time slot in pixels
      const rect = (e.target as HTMLElement).getBoundingClientRect();
      const relativeY = e.clientY - rect.top;
      const minutes = Math.floor((relativeY / timeSlotHeight) * 60);

      // Create new date with calculated minutes
      const newDate = new Date(day);
      newDate.setHours(hour, minutes, 0, 0);

      // Check if the new date is the same as the original
      if (newDate.getTime() === originalDate.getTime()) {
        return;
      }

      onEventDrop?.(eventData, newDate);
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  };

  if (weekEvents.length === 0) {
    return (
      <div className="flex flex-col h-full overflow-hidden">
        {/* Compact Week Header */}
        <div className="border-b bg-secondary sticky top-0 z-20">
          <div className={cn(
            "flex",
            isMobile && "overflow-x-auto"
          )}>
            <div className={cn(
              "border-r sticky left-0 bg-secondary z-10",
              isMobile ? "w-14" : "w-20"
            )}></div>
            <div className={cn(
              "flex flex-1",
              isMobile && "min-w-[700px]"
            )}>
              {days.map((day, i) => (
                <div
                  key={i}
                  className={cn(
                    "flex-1 text-center cursor-pointer transition-colors",
                    isSameDay(day, selectedDate)
                      ? "bg-accent"
                      : "hover:bg-accent/50",
                    isMobile ? "py-3 px-1" : "py-4 px-2"
                  )}
                  onClick={() => setSelectedDate(day)}
                >
                  <div className={cn(
                    "font-semibold text-foreground mb-1",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    {format(day, isMobile ? 'EEE' : 'EEE')}
                  </div>
                  <div className={cn(
                    "inline-flex items-center justify-center font-medium",
                    isMobile ? "text-base w-6 h-6" : "text-lg w-8 h-8",
                    isToday(day)
                      ? "bg-primary text-primary-foreground rounded-full shadow-sm"
                      : "text-muted-foreground"
                  )}>
                    {format(day, 'd')}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Empty State */}
        <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent">
          <div className="text-center max-w-md mx-auto px-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No events this week
            </h3>
            <p className="text-muted-foreground mb-6">
              Your week is completely free. Add some events to get organized!
            </p>
            {canEditData && (
              <Button
                onClick={() => openAddEventForm(selectedDate)}
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Create Event
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Compact Week Header */}
      <div className="border-b bg-secondary sticky top-0 z-20">
        <div className={cn(
          "flex",
          isMobile && "overflow-x-auto"
        )}>
          <div className={cn(
            "border-r sticky left-0 bg-secondary z-10",
            isMobile ? "w-14" : "w-20"
          )}></div>
          <div className={cn(
            "flex flex-1",
            isMobile && "min-w-[700px]"
          )}>
            {days.map((day, i) => {
              const dayEventCount = events.filter(event =>
                isSameDay(new Date(event.start), day)
              ).length;

              return (
                <div
                  key={i}
                  className={cn(
                    "flex-1 text-center cursor-pointer transition-colors",
                    isSameDay(day, selectedDate)
                      ? "bg-accent"
                      : "hover:bg-accent/50",
                    isMobile ? "py-3 px-1" : "py-4 px-2"
                  )}
                  onClick={() => setSelectedDate(day)}
                >
                  <div className={cn(
                    "font-semibold text-foreground mb-1",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    {format(day, isMobile ? 'EEE' : 'EEE')}
                  </div>
                  <div className={cn(
                    "inline-flex items-center justify-center font-medium",
                    isMobile ? "text-base w-6 h-6" : "text-lg w-8 h-8",
                    isToday(day)
                      ? "bg-primary text-primary-foreground rounded-full shadow-sm"
                      : "text-muted-foreground"
                  )}>
                    {format(day, 'd')}
                  </div>
                  {dayEventCount > 0 && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {dayEventCount} {dayEventCount === 1 ? 'event' : 'events'}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Enhanced Time Grid */}
      <div className={cn(
        "flex-1 relative bg-white",
        isMobile ? "overflow-x-auto" : "overflow-auto"
      )} id="week-view-container">
        <div className={cn(
          isMobile && "min-w-[700px]"
        )}>
          {hours.map((hour) => (
            <div
              key={hour}
              className="flex border-b border-gray-50 hover:bg-gray-25 transition-colors"
              style={{ height: '60px' }}
            >
              {/* Enhanced Time column */}
              <div className={cn(
                "sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 bg-white z-10",
                isMobile ? "w-14" : "w-20"
              )}>
                <div className="text-right">
                  <div className="text-sm font-semibold">
                    {format(setHours(new Date(), hour), 'h')}
                  </div>
                  <div className="text-[10px] text-gray-400">
                    {format(setHours(new Date(), hour), 'a')}
                  </div>
                </div>
              </div>

              {/* Day columns */}
              {days.map((day) => (
                <TimeSlot
                  key={`${day.toISOString()}-${hour}`}
                  day={day}
                  hour={hour}
                  canEditData={canEditData}
                  onClick={() => {
                    if (canEditData) {
                      const newDate = setHours(day, hour);
                      openAddEventForm(newDate);
                    }
                  }}
                  onDrop={(e) => handleDrop(day, hour, e)}
                >
                {events
                  .filter(event => {
                    const eventDate = new Date(event.start);
                    return isSameDay(eventDate, day) && eventDate.getHours() === hour;
                  })
                  .map((event) => (
                    <CalendarEventItem
                      key={event.id}
                      event={event}
                      selectedEvent={selectedEvent}
                      canEditData={canEditData}
                      style={{
                        top: `${new Date(event.start).getMinutes() / 60 * 100}%`,
                        height: `${Math.max(30, getEventDurationInMinutes(event) / 60 * 100)}%`,
                        zIndex: selectedEvent === event.id ? 20 : 10
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        const container = document.getElementById('week-view-container');
                        if (container) {
                          savedScrollTop.current = container.scrollTop;
                        }
                        setSelectedEvent(event.id);
                        handleEventClick(event);
                      }}
                    />
                  ))}
              </TimeSlot>
            ))}
          </div>
        ))}
        </div>
        {/* Enhanced Current Time Indicator */}
        {currentTimePosition && (
          <div
            className="absolute left-0 right-0 flex items-center z-50 pointer-events-none"
            style={{
              top: `${(currentTimePosition.hour * 60) + currentTimePosition.minutes}px`,
              width: isMobile ? "calc(100% - 14px)" : "auto"
            }}
          >
            <div className={cn(
              "flex justify-end pr-3 sticky left-0 z-10",
              isMobile ? "w-14" : "w-20"
            )}>
              <div className="w-3 h-3 bg-red-500 border-2 border-white rounded-full shadow-lg"></div>
            </div>
            <div className="flex-1 border-t-2 border-red-500 shadow-sm"></div>
          </div>
        )}
      </div>
    </div>
  );
};
