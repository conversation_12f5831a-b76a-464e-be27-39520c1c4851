import {AuthInfo, getAuthInfo} from "../../businessLogic/authInfo";
import {ApiMessage, ApiResponseStatus, GenericApiResponseBody} from "../interface";
import {NextFunction, Request, Response} from "express"
import {SetupCreator} from "../../businessLogic/creator";
import {AddAsTopPick, ApproveAffiliate, ApproveTemplateSentForReview, GetAffiliates, GetPayouts, GetTemplatesForReview, RemoveTopPick} from "../../businessLogic/admin";
import {AdminMemberService} from "../../service/adminMember";
import {ErrorMessage, NotfoundError} from "../../errors/AppError";


export class AdminController {

    async create(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const myCreator = await SetupCreator(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {myCreator}
        }
        return response.json(
            responseData
        )
    }

    async getMember(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const member = await new AdminMemberService().findOne({userId: authInfo.userId})
        if (!member) throw new NotfoundError(ErrorMessage.EntityNotFound)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                member
            }
        }
        return response.json(
            responseData
        )
    }

    async getSubmittedTemplates(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const templates = await GetTemplatesForReview(authInfo.userId, request.query)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                templates
            }
        }
        return response.json(
            responseData
        )
    }

    async processSubmittedTemplates(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {submission} = await ApproveTemplateSentForReview(authInfo.userId, Number(request.params.id), request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                submission
            }
        }
        return response.json(
            responseData
        )
    }

    async addTopPick(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const topPick = await AddAsTopPick(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                topPick
            }
        }
        return response.json(
            responseData
        )
    }

    async removeTopPick(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        await RemoveTopPick(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {}
        }
        return response.json(
            responseData
        )
    }

    async getPayouts(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {payouts} = await GetPayouts(authInfo.userId, request.query)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                payouts
            }
        }
        return response.json(responseData)
    }

    async getAffiliates(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {affiliates} = await GetAffiliates(authInfo.userId, request.query)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                affiliates
            }
        }
        return response.json(responseData)
    }

    async approveAffiliate(request: Request, response: Response, next: NextFunction) {
        const authInfo: AuthInfo = getAuthInfo(request)

        const {affiliate} = await ApproveAffiliate(authInfo.userId, request.body)

        const responseData: GenericApiResponseBody = {
            status: ApiResponseStatus.Ok,
            message: ApiMessage.ActionSuccessful,
            data: {
                affiliate
            }
        }
        return response.json(responseData)
    }

}


